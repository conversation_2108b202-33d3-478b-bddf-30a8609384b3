<?xml version="1.0" encoding="UTF-8"?>
<manualConfig>
    <!-- 项目基本信息 -->
    <projectName>数据调度中心</projectName>
    <version>2.0</version>
    <author>开发团队</author>
    <description>
        本项目是企业级数据调度平台，主要用于管理和调度各种数据处理工作流。
        支持多租户、分布式执行、实时监控等功能。
        本次上线包含以下主要功能：
        1. 工作流定义和管理
        2. 任务调度和执行
        3. 数据源管理
        4. 监控和告警
    </description>
    
    <!-- 环境信息 -->
    <environment>
        <name>生产环境</name>
        <url>https://scheduler.company.com</url>
        <database>
            <host>db.company.com</host>
            <port>3306</port>
            <name>scheduler_prod</name>
        </database>
    </environment>
    
    <!-- 部署信息 -->
    <deployment>
        <deployTime>2025-08-11 15:00:00</deployTime>
        <deployBy>运维团队</deployBy>
        <backupLocation>/backup/scheduler/20250811</backupLocation>
        <rollbackPlan>
            如果部署失败，执行以下回滚步骤：
            1. 停止应用服务
            2. 恢复数据库备份
            3. 恢复应用代码
            4. 重启服务
        </rollbackPlan>
    </deployment>
    
    <!-- 注意事项 -->
    <notes>
        <note>部署前请确保数据库已备份</note>
        <note>部署过程中会有短暂的服务中断</note>
        <note>部署完成后需要验证关键功能</note>
        <note>如有问题请及时联系开发团队</note>
    </notes>
    
    <!-- 联系人信息 -->
    <contacts>
        <contact>
            <name>张三</name>
            <role>项目经理</role>
            <phone>13800138000</phone>
            <email><EMAIL></email>
        </contact>
        <contact>
            <name>李四</name>
            <role>技术负责人</role>
            <phone>13800138001</phone>
            <email><EMAIL></email>
        </contact>
    </contacts>
</manualConfig>
