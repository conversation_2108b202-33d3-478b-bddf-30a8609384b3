1、任务状态统计

```java
curl--location'http://localhost:60006/dedp/v1/scheduler/analysis/countTask?projectCode=11508864731648&startDate=2023-12-07%2015%3A31%3A23&endDate=2023-12-07%2015%3A31%3A23' \
        --header'token: 8e62b3c95b7b447ab33fceea17bf3e6a'
```

```json
{
  "code": 0,
  "message": "success",
  "result": {
    "taskCountDtos": [
      {
        "count": 0,
        "taskStateType": "SUBMITTED_SUCCESS"
        提交成功
      },
      {
        "count": 0,
        "taskStateType": "RUNNING_EXECUTION"
        正在运行
      },
      {
        "count": 0,
        "taskStateType": "PAUSE"
        暂停
      },
      {
        "count": 0,
        "taskStateType": "FAILURE"
        失败
      },
      {
        "count": 0,
        "taskStateType": "SUCCESS"
        成功
      },
      {
        "count": 0,
        "taskStateType": "NEED_FAULT_TOLERANCE"
        需要容错
      },
      {
        "count": 0,
        "taskStateType": "KILL"
        KILL
      },
      {
        "count": 0,
        "taskStateType": "DELAY_EXECUTION"
        延时执行
      },
      {
        "count": 0,
        "taskStateType": "FORCED_SUCCESS"
        强制成功
      },
      {
        "count": 0,
        "taskStateType": "DISPATCH"
        派发
      }
    ],
    "totalCount": 0
  },
  "took": 536,
  "cip": "0:0:0:0:0:0:0:1"
}
```

2、流程状态统计

```java
curl--location'http://localhost:60006/dedp/v1/scheduler/analysis/processStateCount?projectCode=11508864731648&startDate=2023-12-07%2015%3A31%3A23&endDate=2023-12-07%2015%3A31%3A23' \
        --header'token: 8e62b3c95b7b447ab33fceea17bf3e6a'
```

```json
{
  "code": 0,
  "message": "success",
  "result": {
    "taskCountDtos": [
      {
        "count": 0,
        "taskStateType": "SUBMITTED_SUCCESS"
        提交成功
      },
      {
        "count": 0,
        "taskStateType": "RUNNING_EXECUTION"
        正在运行
      },
      {
        "count": 0,
        "taskStateType": "PAUSE"
        暂停
      },
      {
        "count": 0,
        "taskStateType": "FAILURE"
        失败
      },
      {
        "count": 0,
        "taskStateType": "SUCCESS"
        成功
      },
      {
        "count": 0,
        "taskStateType": "NEED_FAULT_TOLERANCE"
        需要容错
      },
      {
        "count": 0,
        "taskStateType": "KILL"
        KILL
      },
      {
        "count": 0,
        "taskStateType": "DELAY_EXECUTION"
        延时执行
      },
      {
        "count": 0,
        "taskStateType": "DISPATCH"
        派发
      }
    ],
    "totalCount": 0
  },
  "took": 181,
  "cip": "0:0:0:0:0:0:0:1"
}
```

3、流程定义统计

```java
curl--location'http://localhost:60006/dedp/v1/scheduler/analysis/processDefinitionCount?projectCode=11508864731648' \
        --header'token: 8e62b3c95b7b447ab33fceea17bf3e6a'
```

```json
{
  "code": 0,
  "message": "success",
  "result": {
    "userList": [
      {
        "count": 3,
        "userName": "admin",
        "userId": "1"
      }
    ],
    "count": 3
  },
  "took": 163,
  "cip": "0:0:0:0:0:0:0:1"
}
```

http://**************:8858/dedp/v1/scc/dag?jobId=870224151174971393

宁波银行接口统计sql

1、查询已发布的调度任务列表

```
1、查询已发布的调度任务列表
	请求参数（模糊查询）
		（1）文件夹名称（目录名称）
        （2）工作流名称，多个查询逗号隔开（工作流名称）
        （3）源系统编号（源端数据源中业务系统名称）
        （4）源系统名称（源端数据源中业务系统简称）
        （5）源表  (源端表名称,任务中的表名称，自定义sql？怎么获取表名称)
        （6）目标系统编号（目标端数据源中业务系统名称）
        （7）目标系统名称（目标端数据源中业务系统简称）
        （8）目标表 （目标表名称，目标端是库是表名称，如果是文件，则"落成文本"）
	响应参数
		工作流code
		目录名称 （目录名称）
		任务名称 （工作流名称）
		源端业务系统-系统名称  (源端数据源中业务系统名称)
		源端业务系统-简称      (源端数据源中业务系统简称)
		多个拼接显示  （源端表名称,多个逗号拼接，去重）
		目标端业务系统-系统名称 （目标端数据源中业务系统名称）
		目标端业务系统-简称 （目标端数据源中业务系统简称）
		多个拼接显示  （目标表名称，目标端是库是表名称，如果是文件，则"落成文本"）
		定时表达式     （cron表达式,如果没有则是固定值"非工作流定时"）

    curl：curl --location -g --request GET '127.0.0.1:18186/dedp/v1/scc/getProcessDefinitionListAndRunState?page=0&pager=10&catalogName=${catalogName}&sourceBusinessName=${sourceBusinessName}&targetBusinessName=${targetBusinessName}&sourceSimpleName=${sourceSimpleName}&targetSimpleName=${targetSimpleName}&sourceTableName=${sourceTableName}&targetTableName=${targetTableName}&processDefinitionName=${processDefinitionName}' \
         --header 'token: 40f855515f224896898813b5b63d535c'

    响应示例：{
             "code": 0,
             "message": "success",
             "result": [
                 {
                     "target_simple_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,111,111,111,111",
                     "read_row_count": "1000",
                     "datasource_id": "2b5956f53f1045c3aed9df40d61e98bf,2b5956f53f1045c3aed9df40d61e98bf,2b5956f53f1045c3aed9df40d61e98bf,2b5956f53f1045c3aed9df40d61e98bf,69d18818720f438cb4ce251014df27c3,69d18818720f438cb4ce251014df27c3,69d18818720f438cb4ce251014df27c3,69d18818720f438cb4ce251014df27c3,144f19a2abb645cca3c6fd4c422bae24,144f19a2abb645cca3c6fd4c422bae24,144f19a2abb645cca3c6fd4c422bae24,5f85307e1ac94072bc294cfcf3ca7ee4,5f85307e1ac94072bc294cfcf3ca7ee4,5f85307e1ac94072bc294cfcf3ca7ee4,5f85307e1ac94072bc294cfcf3ca7ee4,144f19a2abb645cca3c6fd4c422bae24",
                     "target_table_name": "emp_10241,emp_10241,emp_10241,emp_10241,emp_1024,emp_1024,emp_1024,emp_1024",
                     "crontab": "0 0 0 1/1 * ? *",
                     "last_task_end_time": "2024-08-29 00:02:35",
                     "source_simple_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统",
                     "task_id": "**************",
                     "source_table_name": "emp_quality1,emp_quality1,emp_quality1,emp_quality1,emp_104,emp_104,emp_104,emp_104",
                     "release_state": "上线",
                     "source_business_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统",
                     "last_task_state": "7",
                     "catalogue_name": "lihj测试",
                     "write_row_count": "1000",
                     "process_name": "2个调度",
                     "process_instance_id": "14775095321728",
                     "target_business_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,业务信息管理系统,业务信息管理系统,业务信息管理系统,业务信息管理系统",
                     "last_task_start_time": "2024-08-29 00:01:31",
                     "plugin_classify_id": 1,
                     "process_definition_code": "**************",
                     "tenant_code": "dsg"
                 }
             ],
             "took": 1361,
             "cip": "127.0.0.1"
         }
```



```sql
SELECT
	* 
FROM
	(
	SELECT
		MIN( dtc.NAME ) AS catalogue_name,
		MIN( dit.tenant_code ) AS tenant_code,
		MIN( dis.release_state ) AS release_state,
		MIN( dis.crontab ) AS crontab,
		MIN( dis.process_definition_code ) AS process_code,
		MIN( di_process_definition.NAME ) AS process_name,
		MIN( td.task_id ) AS task_id,
		MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
		GROUP_CONCAT( select_table.datasource_id ) AS datasource_id,
		GROUP_CONCAT( CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.business_name END ) AS source_business_name,
		GROUP_CONCAT( CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.business_name END ) AS target_business_name,
		GROUP_CONCAT( CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.simple_name END ) AS source_simple_name,
		GROUP_CONCAT( CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.simple_name END ) AS target_simple_name,
		GROUP_CONCAT( CASE WHEN plugin_classify_id = 1 THEN select_table.table_name END ) AS source_table_name,
		GROUP_CONCAT( CASE WHEN plugin_classify_id = 2 THEN select_table.table_name END ) AS target_table_name 
	FROM
		task_definition_info td
		LEFT JOIN (
		SELECT
			version_id,
			plugin_classify_id,
			JSON_UNQUOTE(
			JSON_EXTRACT( plugin_basics_config, '$.datasourceId' )) AS datasource_id,
		CASE
				WHEN JSON_UNQUOTE(
				JSON_EXTRACT( plugin_basics_config, '$.tableName' )) IS NULL 
				OR JSON_UNQUOTE(
					JSON_EXTRACT( plugin_basics_config, '$.tableName' )) = '' THEN
					'落成文本' ELSE JSON_UNQUOTE(
					JSON_EXTRACT( plugin_basics_config, '$.tableName' )) 
				END AS table_name 
			FROM
				task_config_info 
			) select_table ON select_table.version_id = td.current_version_id
			LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
			LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
			LEFT JOIN di_process_definition ON FIND_IN_SET( td.task_id, di_process_definition.task_ids )
			RIGHT JOIN di_scheduler dis ON dis.process_definition_code = di_process_definition.id
			LEFT JOIN di_task dit ON dis.process_definition_code = dit.id
			LEFT JOIN di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id 
		GROUP BY
			dis.process_definition_code 
		) select_table 
	WHERE
	release_state = '上线' 
	AND process_name IS NOT NULL
```



2、查询已发布的调度任务列表

```
2、查询已发布的调度任务列表
	入参
		process_definition_code
	出参
		目录名称
		工作流名称
		任务详情[
			任务名称
			任务code
			源端查询sql
			源端where条件
		]
	curl：curl --location --request GET '127.0.0.1:18186/dedp/v1/scc/getProcessDefinitionById?processDefinitionCode=**************'
    
    响应示例：{
             "code": 0,
             "message": "success",
             "result": {
                 "catalogue_name": "lihj测试",
                 "taskList": [
                     {
                         "task_name": "mysql2doris1",
                         "query_sql": "SELECT emp_id, emp_name, gender, account, org_id, birth_date, age, nationality, province, city, email, phone, begin_date, remark, create_time, update_time FROM emp_104 WHERE 1=1 ",
                         "where_sql": "",
                         "task_id": "**************"
                     },
                     {
                         "task_name": "mysql2es",
                         "query_sql": "SELECT emp_id, emp_name, gender, account, org_id, birth_date, age, nationality, province, city, email, phone, begin_date, remark, create_time, update_time FROM emp_quality1 WHERE 1=1 ",
                         "where_sql": "",
                         "task_id": "**************"
                     }
                 ],
                 "process_definition_code": "**************",
                 "task_ids": "**************,**************",
                 "process_definition_name": "2个调度"
             },
             "took": 686,
             "cip": "127.0.0.1"
         }
```

3、任务实例列表（查询对应工作流的任务实例列表）

```
3、任务实例列表（查询对应工作流的任务实例列表）
	入参
		工作流名称
	出参
		目录名称
		任务名称
		工作流名称
		任务状态
		开始时间
		结束时间
		读取总行数（查询统计表）
		写入总行数（查询统计表）

    curl：curl --location --request GET '127.0.0.1:18186/dedp/v1/scc/getProcessInstanceByName?processDefinitionName=2%E4%B8%AA%E8%B0%83%E5%BA%A6'
    
    响应示例：{
             "code": 0,
             "message": "success",
             "result": [
                 {
                     "start_time": "2024-08-28 00:01:48",
                     "read_row_count": 1000,
                     "catalogue_name": "lihj测试",
                     "write_row_count": 1000,
                     "process_name": "2个调度",
                     "end_time": "2024-08-28 00:02:56",
                     "id": "14764045924864",
                     "state": 7
                 },
                 {
                     "start_time": "2024-08-29 00:00:32",
                     "catalogue_name": "lihj测试",
                     "process_name": "2个调度",
                     "end_time": "2024-08-29 00:01:05",
                     "id": "14775095324544",
                     "state": 6
                 },
                 {
                     "start_time": "2024-08-29 00:01:31",
                     "read_row_count": 1000,
                     "catalogue_name": "lihj测试",
                     "write_row_count": 1000,
                     "process_name": "2个调度",
                     "end_time": "2024-08-29 00:01:37",
                     "id": "14775099633664",
                     "state": 7
                 },
                 {
                     "start_time": "2024-08-28 00:00:17",
                     "catalogue_name": "lihj测试",
                     "process_name": "2个调度",
                     "end_time": "2024-08-28 00:01:48",
                     "id": "14764034246144",
                     "state": 6
                 }
             ],
             "took": 1077,
             "cip": "127.0.0.1"
         }
```


4、查询已发布的调度任务列表01

```
请求参数（模糊查询）
		（1）文件夹名称（目录名称）
        （2）工作流名称，多个查询逗号隔开（工作流名称）
        （3）源系统编号（源端数据源中业务系统名称）
        （4）源系统名称（源端数据源中业务系统简称）
        （5）源表  (源端表名称,任务中的表名称，自定义sql？怎么获取表名称)
        （6）目标系统编号（目标端数据源中业务系统名称）
        （7）目标系统名称（目标端数据源中业务系统简称）
        （8）目标表 （目标表名称，目标端是库是表名称，如果是文件，则"落成文本"）
		（9）运行状态(任务状态(运行中、运行成功、运行失败))
		(10)会计时间 （任务执行时间，列表中取最新的一条）
	响应参数
		工作流code
		目录名称 （目录名称）
		任务名称 （工作流名称）
		源端业务系统-系统名称  (源端数据源中业务系统名称)
		源端业务系统-简称      (源端数据源中业务系统简称)
		多个拼接显示  （源端表名称,多个逗号拼接，去重）
		目标端业务系统-系统名称 （目标端数据源中业务系统名称）
		目标端业务系统-简称 （目标端数据源中业务系统简称）
		多个拼接显示  （目标表名称，目标端是库是表名称，如果是文件，则"落成文本"）
		定时表达式     （cron表达式,如果没有则是固定值"非工作流定时"）
		实例运行状态(取任务中最新的)
		实例运行开始时间
		实例运行结束时间
		读取总条数
		写入总条数

        
    curl:curl --location -g --request GET '127.0.0.1:18186/dedp/v1/scc/getProcessDefinitionListAndRunState?page=0&pager=10&catalogName=${catalogName}&sourceBusinessName=${sourceBusinessName}&targetBusinessName=${targetBusinessName}&sourceSimpleName=${sourceSimpleName}&targetSimpleName=${targetSimpleName}&sourceTableName=${sourceTableName}&targetTableName=${targetTableName}&taskState=${taskState}&taskStartTime=${taskStartTime}&processDefinitionName=${processDefinitionName}' \
         --header 'token: 40f855515f224896898813b5b63d535c'

    响应示例：{
             "code": 0,
             "message": "success",
             "result": [
                 {
                     "target_simple_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,111,111,111,111",
                     "read_row_count": "1000",
                     "datasource_id": "2b5956f53f1045c3aed9df40d61e98bf,2b5956f53f1045c3aed9df40d61e98bf,2b5956f53f1045c3aed9df40d61e98bf,2b5956f53f1045c3aed9df40d61e98bf,69d18818720f438cb4ce251014df27c3,69d18818720f438cb4ce251014df27c3,69d18818720f438cb4ce251014df27c3,69d18818720f438cb4ce251014df27c3,144f19a2abb645cca3c6fd4c422bae24,144f19a2abb645cca3c6fd4c422bae24,144f19a2abb645cca3c6fd4c422bae24,5f85307e1ac94072bc294cfcf3ca7ee4,5f85307e1ac94072bc294cfcf3ca7ee4,5f85307e1ac94072bc294cfcf3ca7ee4,5f85307e1ac94072bc294cfcf3ca7ee4,144f19a2abb645cca3c6fd4c422bae24",
                     "target_table_name": "emp_10241,emp_10241,emp_10241,emp_10241,emp_1024,emp_1024,emp_1024,emp_1024",
                     "crontab": "0 0 0 1/1 * ? *",
                     "last_task_end_time": "2024-08-29 00:02:35",
                     "source_simple_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统",
                     "task_id": "**************",
                     "source_table_name": "emp_quality1,emp_quality1,emp_quality1,emp_quality1,emp_104,emp_104,emp_104,emp_104",
                     "release_state": "上线",
                     "source_business_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统,研发测试系统",
                     "last_task_state": "7",
                     "catalogue_name": "lihj测试",
                     "write_row_count": "1000",
                     "process_name": "2个调度",
                     "process_instance_id": "14775095321728",
                     "target_business_name": "研发测试系统,研发测试系统,研发测试系统,研发测试系统,业务信息管理系统,业务信息管理系统,业务信息管理系统,业务信息管理系统",
                     "last_task_start_time": "2024-08-29 00:01:31",
                     "plugin_classify_id": 1,
                     "process_definition_code": "**************",
                     "tenant_code": "dsg"
                 }
             ],
             "took": 1252,
             "cip": "127.0.0.1"
         }
```

```sql
SELECT 
    MIN(dtc.name) AS catalogue_name,
    MIN(dit.tenant_code) AS tenant_code,
    MIN(dis.release_state) AS release_state,
    MIN(dis.crontab) AS crontab,
    MIN(dis.process_definition_code) AS process_definition_code,
    MIN(di_process_definition.name) AS process_name,
    MIN(td.task_id) AS task_id,
    MIN(select_table.plugin_classify_id) AS plugin_classify_id,
    GROUP_CONCAT(select_table.datasource_id) AS datasource_id,
    GROUP_CONCAT(CASE
            WHEN plugin_classify_id = 1 THEN business_database.datasource_business.business_name
        END) AS source_business_name,
    GROUP_CONCAT(CASE
            WHEN plugin_classify_id = 2 THEN business_database.datasource_business.business_name
        END) AS target_business_name,
    GROUP_CONCAT(CASE
            WHEN plugin_classify_id = 1 THEN business_database.datasource_business.simple_name
        END) AS source_simple_name,
    GROUP_CONCAT(CASE
            WHEN plugin_classify_id = 2 THEN business_database.datasource_business.simple_name
        END) AS target_simple_name,
    GROUP_CONCAT(CASE
            WHEN plugin_classify_id = 1 THEN select_table.table_name
        END) AS source_table_name,
    GROUP_CONCAT(CASE
            WHEN plugin_classify_id = 2 THEN select_table.table_name
        END) AS target_table_name,
    SUBSTRING_INDEX(GROUP_CONCAT(DISTINCT business_ds.t_ds_process_instance.id
                ORDER BY business_ds.t_ds_process_instance.update_time DESC
                SEPARATOR ','),
            ',',
            1) AS process_instance_id,
    SUBSTRING_INDEX(GROUP_CONCAT(ti.state
                ORDER BY ti.start_time DESC),
            ',',
            1) AS last_task_state,
    SUBSTRING_INDEX(GROUP_CONCAT(ti.start_time
                ORDER BY ti.start_time DESC),
            ',',
            1) last_task_start_time
FROM
    business_integration.task_definition_info td
        LEFT JOIN
    (SELECT 
        version_id,
            plugin_classify_id,
            JSON_UNQUOTE(JSON_EXTRACT(plugin_basics_config, '$.datasourceId')) AS datasource_id,
            JSON_UNQUOTE(JSON_EXTRACT(plugin_basics_config, '$.tableName')) AS table_name
    FROM
        business_integration.task_config_info) select_table ON select_table.version_id = td.current_version_id
        LEFT JOIN
    business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN
    business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN
    business_integration.di_process_definition ON FIND_IN_SET(td.task_id,
            di_process_definition.task_ids)
        RIGHT JOIN
    business_integration.di_scheduler dis ON dis.process_definition_code = di_process_definition.id
        LEFT JOIN
    business_integration.di_task dit ON dis.process_definition_code = dit.id
        LEFT JOIN
    business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        LEFT JOIN
    business_ds.t_ds_process_instance ON business_ds.t_ds_process_instance.process_definition_code = dis.process_definition_code
        LEFT JOIN
    business_ds.t_ds_task_instance ti ON ti.process_instance_id = business_ds.t_ds_process_instance.id
WHERE
    dis.release_state = '上线'
        AND dit.task_type = 2
GROUP BY dis.process_definition_code
```

