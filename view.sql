create view scc_process_instance as
select `ds`.`id` AS `id`,`ds`.`name` AS `name`,`ds`.`process_definition_code` AS `process_definition_id`,`ds`.`process_definition_version` AS `process_definition_version`,`ds`.`state` AS `state`,`ds`.`state_history` AS `state_history`,`ds`.`recovery` AS `recovery`,`ds`.`start_time` AS `start_time`,`ds`.`end_time` AS `end_time`,`ds`.`run_times` AS `run_times`,`ds`.`host` AS `host`,`ds`.`command_type` AS `command_type`,`ds`.`command_param` AS `command_param`,`ds`.`task_depend_type` AS `task_depend_type`,`ds`.`max_try_times` AS `max_try_times`,`ds`.`failure_strategy` AS `failure_strategy`,`ds`.`warning_type` AS `warning_type`,`ds`.`warning_group_id` AS `warning_group_id`,`ds`.`schedule_time` AS `schedule_time`,`ds`.`command_start_time` AS `command_start_time`,`ds`.`global_params` AS `global_params`,`ds`.`is_sub_process` AS `is_sub_process`,`ds`.`executor_id` AS `executor_id`,`ds`.`history_cmd` AS `history_cmd`,`ds`.`process_instance_priority` AS `process_instance_priority`,`ds`.`worker_group` AS `worker_group`,`ds`.`environment_code` AS `environment_code`,`ds`.`timeout` AS `timeout`,`ds`.`tenant_id` AS `tenant_id`,`ds`.`var_pool` AS `var_pool`,`ds`.`dry_run` AS `dry_run`,`ds`.`next_process_instance_id` AS `next_process_instance_id`,`ds`.`restart_time` AS `restart_time`,`ds`.`test_flag` AS `test_flag`,`ds`.`calendar_id` AS `calendar_id`,`ds`.`start_time` AS `create_time`,`ds`.`update_time` AS `last_modification_time`,`d`.`tenant_code` AS `tenant_code`,`d`.`create_by` AS `create_by`,`d`.`create_by_name` AS `create_by_name`,`d`.`is_public` AS `is_public`,`d`.`pos` AS `pos`,`d`.`dbid` AS `dbid`,`d`.`update_by` AS `update_by`,`d`.`del_flag` AS `del_flag`,`d`.`project` AS `project`,`d`.`data_owner_dept_id` AS `data_owner_dept_id`,`d`.`data_owner_dept_name` AS `data_owner_dept_name`,`d`.`data_owner_user_id` AS `data_owner_user_id`,`d`.`data_owner_user_name` AS `data_owner_user_name`,`d`.`readonly` AS `readonly`,`d`.`project_id` AS `project_id`,`d`.`product_id` AS `product_id`,(select group_concat(`scc_task`.`name` separator ',') from `scc_task` where ((`scc_task`.`tenant_code` = `scc_task`.`tenant_code`) and (`scc_task`.`project` = `scc_task`.`project`) and (`scc_task`.`project_id` = `scc_task`.`project_id`) and (0 <> find_in_set(`scc_task`.`id`,`d`.`task_ids`)))) AS `task_names`,`d`.`task_ids` AS `task_ids` from (`business_ds`.`t_ds_process_instance` `ds` left join `scc_process_definition` `d` on((`ds`.`process_definition_code` = `d`.`id`))) where (`d`.`product_id` is not null)

create view scc_task_instance as
select `ds`.`id` AS `id`,`ds`.`name` AS `name`,`ds`.`task_type` AS `task_type`,`ds`.`task_execute_type` AS `task_execute_type`,`ds`.`task_code` AS `task_id`,`ds`.`task_definition_version` AS `task_version`,`ds`.`process_instance_id` AS `process_instance_id`,`ds`.`state` AS `state`,`ds`.`submit_time` AS `submit_time`,`ds`.`start_time` AS `start_time`,`ds`.`end_time` AS `end_time`,`ds`.`host` AS `host`,`ds`.`execute_path` AS `execute_path`,`ds`.`log_path` AS `log_path`,`ds`.`alert_flag` AS `alert_flag`,`ds`.`retry_times` AS `retry_times`,`ds`.`pid` AS `pid`,`ds`.`app_link` AS `app_link`,`ds`.`task_params` AS `task_params`,`ds`.`flag` AS `flag`,`ds`.`retry_interval` AS `retry_interval`,`ds`.`max_retry_times` AS `max_retry_times`,`ds`.`task_instance_priority` AS `task_instance_priority`,`ds`.`worker_group` AS `worker_group`,`ds`.`environment_code` AS `environment_code`,`ds`.`executor_id` AS `executor_id`,`ds`.`environment_config` AS `environment_config`,`ds`.`first_submit_time` AS `first_submit_time`,`ds`.`delay_time` AS `delay_time`,`ds`.`var_pool` AS `var_pool`,`ds`.`task_group_id` AS `task_group_id`,`ds`.`cpu_quota` AS `cpu_quota`,`ds`.`dry_run` AS `dry_run`,`ds`.`memory_max` AS `memory_max`,`ds`.`test_flag` AS `test_flag`,`ds`.`start_time` AS `create_time`,`ds`.`end_time` AS `last_modification_time`,`t`.`tenant_code` AS `tenant_code`,`t`.`create_by` AS `create_by`,`t`.`create_by_name` AS `create_by_name`,`t`.`is_public` AS `is_public`,`t`.`pos` AS `pos`,`t`.`dbid` AS `dbid`,`t`.`update_by` AS `update_by`,`t`.`del_flag` AS `del_flag`,`t`.`project` AS `project`,`t`.`data_owner_dept_id` AS `data_owner_dept_id`,`t`.`data_owner_dept_name` AS `data_owner_dept_name`,`t`.`data_owner_user_id` AS `data_owner_user_id`,`t`.`data_owner_user_name` AS `data_owner_user_name`,`t`.`readonly` AS `readonly`,`t`.`project_id` AS `project_id`,`t`.`product_id` AS `product_id`,`t`.`catalog_id` AS `catalog_id`,`c`.`name` AS `catalog_name` from (((`business_ds`.`t_ds_task_instance` `ds` left join `scc_process_instance` `p` on((`ds`.`process_instance_id` = `p`.`id`))) left join `scc_task` `t` on((`ds`.`task_code` = `t`.`id`))) left join `scc_catalog` `c` on((`t`.`catalog_id` = `c`.`id`))) where (`t`.`product_id` is not null)
