# Scheduler Center

调度中心

使用seatunnel 与现在wetl区别
1、现在wetl位点无论分片与否，只有当任务完全运行完毕后才会存储savepoint。当任务暂停时候是强制kill任务，没有存储savepoint
seatunnel当分片的情况下，当任务暂停时候，需等当前运行分片内数据都写完毕后才会暂停，会记录savepoint。任务运行完毕也会存储savepoint,

外部系统使用：
1、需自己运行时候指定传输什么数据，例如增量，需传给引擎开始和结束标识。当任务运行中，暂停时候，直接调用接口即可，无需传特殊标识
2、目前目标端需自行建表，引擎暂不支持自动建表

清除数据
truncate table scc_process_definition;
truncate table scc_process_definition_log;
truncate table scc_process_task_relation;
truncate table scc_process_task_relation_log;
truncate table scc_scheduler;
truncate table scc_task;
truncate table scc_task_log;

// 是否开始定时任务， 默认值 true
// 当需要所有的定时任务都关闭，值开启指定 XXX 定时任务时候，设置为
// app_executor_enable=false
// app_executor_XXX_enable=true
// 当所有任务都需要开启，只关闭指定YYY 定时任务时候，设置为
// app_executor_enable=true
// app_executor_YYY_enable=true

http://192.168.100.58:18186/dedp/v1/scc/manager/scheduled/statusAll
http://192.168.100.58:18186/dedp/v1/scc/manager/scheduled/ProcessInstanceSyncScheduler.run/stop