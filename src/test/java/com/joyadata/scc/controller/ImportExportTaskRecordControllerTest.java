package com.joyadata.scc.controller;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 测试MDC日志分离功能
 */
public class ImportExportTaskRecordControllerTest {
    
    private static final Logger log = LoggerFactory.getLogger(ImportExportTaskRecordController.class);
    
    @Test
    public void testMDCLogSeparation() throws Exception {
        // 清理现有日志文件
        String logPath = System.getProperty("user.dir") + "/logs/import_export";
        File logDir = new File(logPath);
        if (logDir.exists()) {
            File[] existingFiles = logDir.listFiles((dir, name) -> name.contains("_"));
            if (existingFiles != null) {
                for (File file : existingFiles) {
                    file.delete();
                }
            }
        }

        // 测试批次1 - 产品1项目1
        String batchNo1 = "TEST001";
        testBatchLogging(batchNo1, "proj1", "prod1");

        // 测试批次2 - 产品2项目2
        String batchNo2 = "TEST002";
        testBatchLogging(batchNo2, "proj2", "prod2");

        // 测试批次3 - 同一产品项目的不同批次
        String batchNo3 = "TEST003";
        testBatchLogging(batchNo3, "proj1", "prod1");

        // 验证日志文件是否生成
        Thread.sleep(3000); // 等待日志写入

        if (logDir.exists()) {
            System.out.println("批次日志目录存在: " + logPath);
            File[] logFiles = logDir.listFiles();
            if (logFiles != null) {
                for (File logFile : logFiles) {
                    System.out.println("日志文件: " + logFile.getName() + " (大小: " + logFile.length() + " bytes)");
                }

                // 检查是否有按product_project_batchNo格式分离的文件
                File batch1File = new File(logDir, "prod1_proj1_TEST001.log");
                File batch2File = new File(logDir, "prod2_proj2_TEST002.log");
                File batch3File = new File(logDir, "prod1_proj1_TEST003.log");

                System.out.println("prod1_proj1_TEST001.log 存在: " + batch1File.exists());
                System.out.println("prod2_proj2_TEST002.log 存在: " + batch2File.exists());
                System.out.println("prod1_proj1_TEST003.log 存在: " + batch3File.exists());

                if (batch1File.exists()) {
                    System.out.println("\n=== prod1_proj1_TEST001.log 内容 ===");
                    Files.lines(batch1File.toPath()).limit(5).forEach(System.out::println);
                }

                if (batch2File.exists()) {
                    System.out.println("\n=== prod2_proj2_TEST002.log 内容 ===");
                    Files.lines(batch2File.toPath()).limit(5).forEach(System.out::println);
                }

                if (batch3File.exists()) {
                    System.out.println("\n=== prod1_proj1_TEST003.log 内容 ===");
                    Files.lines(batch3File.toPath()).limit(5).forEach(System.out::println);
                }
            }
        } else {
            System.out.println("批次日志目录不存在: " + logPath);
        }
    }
    
    private void testBatchLogging(String batchNo, String projectId, String productId) {
        try {
            // 设置MDC
            MDC.put("batchNo", batchNo);
            MDC.put("projectId", projectId);
            MDC.put("productId", productId);
            
            // 模拟日志输出
            log.info("开始批次异步处理");
            log.info("创建批次工作目录：/tmp/export/{}/{}/{}", productId, projectId, batchNo);
            log.info("开始处理工作流定义代码：workflow1");
            log.info("工作流 workflow1 的JSON文件路径：/tmp/export/{}/{}/{}/workflow1_123456.json", productId, projectId, batchNo);
            log.info("工作流定义代码 workflow1 处理完成，JSON文件已生成");
            log.info("工作流 workflow1 处理完成，进度：1/3");
            log.info("开始创建tar包，工作目录：/tmp/export/{}/{}/{}", productId, projectId, batchNo);
            log.info("tar包创建成功：/tmp/export/{}/{}/{}_export.tar", productId, projectId, batchNo);
            log.info("批次处理完成，所有工作流已导出并打包，临时文件已清理");
            
        } finally {
            // 清理MDC
            MDC.clear();
        }
    }
}
