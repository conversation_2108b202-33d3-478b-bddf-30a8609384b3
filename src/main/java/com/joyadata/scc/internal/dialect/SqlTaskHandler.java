package com.joyadata.scc.internal.dialect;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.scc.internal.TaskHandler;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/17 14:14
 */
public class SqlTaskHandler implements TaskHandler {
    @Override
    public List<String> extractDatasourceIds(JSONObject taskParams) {
 
        return Collections.emptyList();
    }
}
