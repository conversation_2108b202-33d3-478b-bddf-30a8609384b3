package com.joyadata.scc.internal.dialect;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.engine.common.beans.dto.EngineEntryPointDTO;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceDTO;
import com.joyadata.scc.internal.TaskHandler;
import com.joyadata.scc.internal.dialect.dto.StxDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/17 14:17
 */
@Slf4j
public class StxTaskHandler implements TaskHandler {
    @Override
    public List<String> extractDatasourceIds(JSONObject taskParams) {
        StxDTO stxDTO = JSONObject.parseObject(taskParams.toJSONString(), StxDTO.class);
        List<String> datasourceIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(stxDTO.getRawScript())) {
            EngineEntryPointDTO engineEntryPointDTO = JSONObject.parseObject(stxDTO.getRawScript(), EngineEntryPointDTO.class);
            List<EngineSourceDTO> sourceDTOS = engineEntryPointDTO.getSource();
            datasourceIds.addAll(getSourceIds(sourceDTOS));
            List<EngineSinkDTO> sinkDTOS = engineEntryPointDTO.getSink();
            datasourceIds.addAll(getSinkIds(sinkDTOS));
        }
        return datasourceIds;
    }

    private Collection<String> getSourceIds(List<EngineSourceDTO> sourceDTOS) {
        List<String> sourceIds = new ArrayList<>();
        sourceDTOS.forEach(src -> {
            try {
                for (Method method : src.getClass().getMethods()) {
                    if (method.getName().startsWith("get") && method.getName().endsWith("Source")) {
                        Object sourceObj = method.invoke(src);
                        if (sourceObj != null) {
                            // 找到不为null的source对象，尝试获取datasourceId/databaseId字段
                            try {
                                Method getDatasourceId = sourceObj.getClass().getMethod("getDatasourceInfoId");
                                Object id = getDatasourceId.invoke(sourceObj);
                                if (id != null) {
                                    sourceIds.add(id.toString());
                                }
                            } catch (NoSuchMethodException e) {
                                // 有的类型可能叫databaseId
                                try {
                                    Method getDatabaseId = sourceObj.getClass().getMethod("getDatasourceInfoId");
                                    Object id = getDatabaseId.invoke(sourceObj);
                                    if (id != null) {
                                        sourceIds.add(id.toString());
                                    }
                                } catch (NoSuchMethodException ignore) {
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return sourceIds;
    }

    private Collection<String> getSinkIds(List<EngineSinkDTO> sinkDTOS) {
        List<String> sourceIds = new ArrayList<>();
        sinkDTOS.forEach(src -> {
            try {
                for (Method method : src.getClass().getMethods()) {
                    if (method.getName().startsWith("get") && method.getName().endsWith("Sink")) {
                        Object sourceObj = method.invoke(src);
                        if (sourceObj != null) {
                            // 找到不为null的source对象，尝试获取datasourceId/databaseId字段
                            try {
                                Method getDatasourceId = sourceObj.getClass().getMethod("getDatasourceInfoId");
                                Object id = getDatasourceId.invoke(sourceObj);
                                if (id != null) {
                                    sourceIds.add(id.toString());
                                }
                            } catch (NoSuchMethodException e) {
                                // 有的类型可能叫databaseId
                                try {
                                    Method getDatabaseId = sourceObj.getClass().getMethod("getDatasourceInfoId");
                                    Object id = getDatabaseId.invoke(sourceObj);
                                    if (id != null) {
                                        sourceIds.add(id.toString());
                                    }
                                } catch (NoSuchMethodException ignore) {
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return sourceIds;
    }
}
