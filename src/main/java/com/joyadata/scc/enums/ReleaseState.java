/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.joyadata.scc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * process define release state
 */
public enum ReleaseState {

    /**
     * 0 offline
     * 1 online
     */
    OFFLINE("下线", "OFFLINE"),
    ONLINE("上线", "ONLINE");

    ReleaseState(String code, String value) {
        this.code = code;
        this.value = value;
    }

    @EnumValue
    private final String code;
    private final String value;

    public static ReleaseState getEnum(String code) {
        for (ReleaseState e:ReleaseState.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        //For values out of enum scope
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
