/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.joyadata.scc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.NonNull;

import java.util.HashMap;
import java.util.Map;

/**
 * define process and task priority
 */
public enum Priority {
    /**
     * 0 highest priority
     * 1 higher priority
     * 2 medium priority
     * 3 lower priority
     * 4 lowest priority
     */
    HIGHEST(0, "highest"),
    HIGH(1, "high"),
    <PERSON><PERSON>UM(2, "medium"),
    LOW(3, "low"),
    LOWEST(4, "lowest");

    Priority(int code, String descp) {
        this.code = code;
        this.desc = descp;
    }

    @EnumValue
    private final int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<Integer, Priority> ENUM_MAP = new HashMap<>();

    static {
        for (Priority e : Priority.values()) {
            ENUM_MAP.put(e.getCode(), e);
        }
    }

    public static @NonNull Priority of(int code) {
        Priority e = ENUM_MAP.get(code);
        if (e == null) {
            throw new IllegalArgumentException(String.format("The  status code: %s is invalidated",
                    code));
        }
        return e;
    }
}
