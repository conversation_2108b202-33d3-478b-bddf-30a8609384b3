/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.joyadata.scc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.NonNull;

import java.util.HashMap;
import java.util.Map;

/**
 * task execute type
 */
public enum TaskExecuteType {
    /**
     * 0 batch
     * 1 stream
     */
    BATCH(0, "batch"),
    STREAM(1, "stream");

    private static final Map<Integer, TaskExecuteType> ENUM_MAP = new HashMap<>();

    static {
        for (TaskExecuteType taskExecuteType : TaskExecuteType.values()) {
            ENUM_MAP.put(taskExecuteType.getCode(), taskExecuteType);
        }
    }

    TaskExecuteType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static @NonNull TaskExecuteType of(Integer code) {
        TaskExecuteType taskExecuteType = ENUM_MAP.get(code);
        if (taskExecuteType == null) {
            throw new IllegalArgumentException(String.format("The taskExecuteType execution status code: %s is invalidated",
                    code));
        }
        return taskExecuteType;
    }

    @EnumValue
    private final int code;
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
