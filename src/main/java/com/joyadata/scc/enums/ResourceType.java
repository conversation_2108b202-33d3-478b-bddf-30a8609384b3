package com.joyadata.scc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ResourceType
 * @date 2023/11/8
 */
public enum ResourceType {
    /**
     * 0 file, 1 udf
     */
    FILE(0, "file"),
    UDF(1, "udf");

    ResourceType(int code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final int code;
    private final String descp;

    public int getCode() {
        return code;
    }

    public String getDescp() {
        return descp;
    }
}
