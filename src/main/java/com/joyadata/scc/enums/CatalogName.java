package com.joyadata.scc.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: CatalogName
 * @date 2024/1/17
 */
public enum CatalogName {

    CUSTOM("自定义目录", 0),
    DEFAULT("默认目录", 1);

    @EnumValue
    private final String name;
    private final Integer type;

    CatalogName(String name, Integer type) {
        this.name = name;
        this.type = type;
    }

    public static CatalogName getEnum(String name) {
        for (CatalogName e : CatalogName.values()) {
            if (e.name.equals(name)) {
                return e;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
