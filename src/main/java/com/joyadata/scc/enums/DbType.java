//package com.joyadata.scheduler.enums;
//
//import com.baomidou.mybatisplus.annotation.EnumValue;
//import com.google.common.base.Functions;
//
//import java.util.Arrays;
//import java.util.Map;
//import java.util.NoSuchElementException;
//
//import static java.util.stream.Collectors.toMap;
//
///**
// * Created by quguijie on 2023/01/12.
// * 对应成都数据源枚举
// */
//public enum DbType {
//    MYSQL(0, "MySQL"),
//    POSTGRESQL(1, "PostgreSQL"),
//    HIVE(2, "Hive"),
//    CLICKHOUSE(4, "ClickHouse"),
//    ORACLE(5, "Oracle"),
//    SQLSERVER(6, "SQLSERVER"),
//    DB2(7, "DB2"),
//    PRESTO(8, "Presto");
//    @EnumValue
//    private final int code;
//    private final String dataType;
//
//    DbType(int code, String dataType) {
//        this.code = code;
//        this.dataType = dataType;
//    }
//
//    public int getCode() {
//        return code;
//    }
//
//    public String getDataType() {
//        return dataType;
//    }
//
//    private static final Map<Integer, DbType> DB_TYPE_MAP =
//            Arrays.stream(DbType.values()).collect(toMap(DbType::getCode, Functions.identity()));
//
//    public static DbType of(int type) {
//        if (DB_TYPE_MAP.containsKey(type)) {
//            return DB_TYPE_MAP.get(type);
//        }
//        return null;
//    }
//
//    public static DbType ofName(String name) {
//        return Arrays.stream(DbType.values()).filter(e -> e.name().equals(name)).findFirst().orElseThrow(() -> new NoSuchElementException("no such db type"));
//    }
//
//    public boolean isHive() {
//        return this == DbType.HIVE;
//    }
//}
