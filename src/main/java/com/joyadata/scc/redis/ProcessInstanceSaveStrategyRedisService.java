package com.joyadata.scc.redis;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class ProcessInstanceSaveStrategyRedisService {

    private static String INSTANCE_STATUS = "dedp:ds:clean:status";
    private static String INSTANCE_MAX_DAY = "dedp:ds:clean:day";
    private static String INSTANCE_TEST_MAX_DAY = "dedp:ds:cleantest:day";
    private static String INSTANCE_MAX_STRIP = "dedp:ds:clean:strip";
    private static String INSTANCE_MAX_QUEUE = "dedp:ds:clean:queue";
    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取项目工作流实例保存天数
     */
    public Table<String, String, Integer> getProjectIdDayMap() {
        Table<String, String, Integer> table = HashBasedTable.create();
        Map<Object, Object> dayEntries = redisTemplate.opsForHash().entries(INSTANCE_MAX_DAY);
        if (!dayEntries.isEmpty()) {
            for (Map.Entry<Object, Object> entry : dayEntries.entrySet()) {
                String projectId = (String) entry.getKey();
                String day = (String) entry.getValue();
                if (null != day) {
                    try {
                        table.put(projectId, "day", Integer.parseInt(day));
                    } catch (Exception e) {
                        log.warn("工作流实例存储天数获取失败:{}", e.getMessage(), e);
                    }
                }
            }
        }
        Map<Object, Object> stripEntries = redisTemplate.opsForHash().entries(INSTANCE_MAX_STRIP);
        if (!stripEntries.isEmpty()) {
            for (Map.Entry<Object, Object> entry : stripEntries.entrySet()) {
                String projectId = (String) entry.getKey();
                String day = (String) entry.getValue();
                if (null != day) {
                    try {
                        table.put(projectId, "strip", Integer.parseInt(day));
                    } catch (Exception e) {
                        log.warn("工作流实例存储条数获取失败:{}", e.getMessage(), e);
                    }
                }
            }
        }
        return table;
    }
}
