package com.joyadata.scc.controller;

import com.joyadata.annotation.log.Log;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.scc.model.Environment;
import com.joyadata.scc.model.WorkerGroup;
import com.joyadata.scc.service.EnvironmentService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EnvironmentController
 * @date 2023/11/8
 */
@CrossOrigin
@RestController
public class EnvironmentController {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private EnvironmentService environmentService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @GetMapping(value = "/environment/query-environment-list")
    public Response queryAllEnvironmentList() {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.ENVIRONMENT_LIST;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        return Utils.responseInfo(map);
    }
    @Auth
    @Log("新增环境管理")
    @PostMapping(value = "/environment/create")
    public Response<?> createEnvironment(@RequestBody Environment environment) {
        return environmentService.createEnvironment(environment);
    }
    @Auth
    @Log("修改环境管理")
    @PutMapping(value = "/environment/update")
    public Response<?> updateEnvironment(@RequestBody Environment environment) {
        return environmentService.updateEnvironment(environment);
    }
    @Auth
    @Log("删除环境管理")
    @DeleteMapping(value = "/environment/delete/{environmentCode}")
    public Response<?> updateEnvironment(@PathVariable String environmentCode) {
        return environmentService.deleteEnvironment(environmentCode);
    }
}
