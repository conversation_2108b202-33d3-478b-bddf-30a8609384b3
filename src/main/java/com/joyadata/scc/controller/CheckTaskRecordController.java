package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.web.Response;
import com.joyadata.scc.model.CheckTaskRecord;
import com.joyadata.scc.util.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@CrossOrigin
@RestController
public class CheckTaskRecordController extends BaseController<CheckTaskRecord> {

    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        Response<List<?>> response = super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        List<CheckTaskRecord> list = (List<CheckTaskRecord>) response.getResult();
        if (null != list && !list.isEmpty()) {
            list.forEach(t -> {
                //将工作流名称的目录id后缀去掉
                String processDefinitionName = t.getProcessDefinitionName();
                if (StringUtils.isNotBlank(processDefinitionName)){
                    t.setProcessDefinitionName(filterNameSuffix(processDefinitionName));
                }
                //将任务名称的目录id后缀去掉
                String taskName = t.getTaskName();
                if (StringUtils.isNotBlank(taskName)){
                    t.setTaskName(filterNameSuffix(taskName));
                }
            });
        }
        return response;
    }

    private String filterNameSuffix(String name) {
        if (name.contains("__[") && name.contains("_copy")) {
            //如果是复制的工作流，要截取中间的一节
            return Utils.removeBetween(name, "__", "_copy");
        } else if (name.contains("__[")) {
            return name.substring(0, name.lastIndexOf("__["));
        }
        return name;
    }
}
