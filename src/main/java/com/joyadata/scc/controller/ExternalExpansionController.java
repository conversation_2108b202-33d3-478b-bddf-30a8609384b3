package com.joyadata.scc.controller;

import cn.hutool.json.JSONString;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.cms.model.User;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.dto.StartParam;
import com.joyadata.scc.service.ExternalExpansionService;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.tms.model.Project;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.JsonUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ExternalExpansionController
 * @date 2024/6/3
 */
@Slf4j
@CrossOrigin
@RestController
public class ExternalExpansionController {

    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    private JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);

    @Autowired
    private ExternalExpansionService externalExpansionService;
    private final String PROCESS_START = "dedp:scc:externalExpansion:start:";
    private final String PROCESS_STOP = "dedp:scc:externalExpansion:stop:";


    @Log("获取三个外部接口的url")
    @Auth
    @GetMapping("/externalExpansion/getUrl")
    public Response<?> getUrl() {
        Map<String, String> resultMap = new HashMap<>();
        String prefix = ApplicationContextHelp.getAppGatewayHttp() + "/" + ApplicationContextHelp.getProject() + "/" + ApplicationContextHelp.getVersion() + "/" + ApplicationContextHelp.getModule();
        resultMap.put("startProcess", prefix + "/externalExpansion/startProcess?assetToken={assetToken}");
        resultMap.put("stopProcess", prefix + "/externalExpansion/stopProcess?assetToken={assetToken}");
        resultMap.put("getProcessInstanceState", prefix + "/externalExpansion/getProcessInstanceState?assetToken={assetToken}");
        return ResponseFactory.makeSuccess("success", resultMap);
    }

    /**
     * @return
     * @throws UnsupportedEncodingException
     */
    @Log("运行工作流")
    @PostMapping("/externalExpansion/startProcess")
    public Response<?> startProcessInstance(HttpServletRequest httpServletRequest, @RequestBody JSONObject body, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) throws UnsupportedEncodingException {
        return externalExpansionService.startProcessInstance(httpServletRequest, body, assetToken, token);
    }

    @Log("运行工作流-新")
    @PostMapping("/externalExpansion/startProcessDefinition")
    public Response<?> startProcess(HttpServletRequest httpServletRequest, @RequestBody JSONObject body, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) {
        User user = ThreadLocalUserUtil.getUser(User.class);
        //获取当前线程用户为空，认为token是过期的
        if (null == user) {
            return new Response<>(100, "token失效，请重新获取。");
        }
        String projectName = body.getString("projectName");
        String processName = body.getString("processName");
        if (null == projectName || null == processName) {
            log.error("缺少必要参数！projectName或processName为空！获取到的body={}", body.toJSONString());
            return new Response<>(106, "缺少必要参数！projectName或processName为空！");
        }
        JSONObject params = body.getJSONObject("params");
        Date callTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String stringCallTime = formatter.format(callTime);
        //同一个工作流使用相同参数，三秒内不允许重复请求
        String redisKey;
        if (null == params) {
            redisKey = PROCESS_START + projectName + ":" + processName + ":null";
        } else {
            redisKey = PROCESS_START + projectName + ":" + processName + ":" + params.toJSONString();
        }
        String lock = redisTemplate.opsForValue().get(redisKey);
        if (null != lock) {
            redisTemplate.opsForValue().set(redisKey, "1", 3, TimeUnit.SECONDS);
            externalExpansionService.addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，不允许重复提交！", "", params);
            return new Response<>(103, "不允许重复提交！");
        }
        redisTemplate.opsForValue().set(redisKey, "1", 3, TimeUnit.SECONDS);

        String projectId = projectJoyaFeignService.getQuery().eq("name", projectName).oneValue("id", String.class);
        if (StringUtils.isBlank(projectId)) {
            log.error("[{}]项目不存在", projectName);
            externalExpansionService.addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，[" + projectName + "]项目不存在", "", params);
            return new Response<>(105, "[" + projectName + "]项目不存在");
        }
        List<ProcessDefinition> list = processDefinitionService.getQuery().eq("projectId", projectId).eq("name", processName).list();
        if (CollectionUtils.isEmpty(list)) {
            log.error("[" + projectName + "]项目下不存在[" + processName + "]工作流，查询条件：projectId={},processName={}", projectId, processName);
            externalExpansionService.addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，[" + processName + "]工作流不存在！", "", params);
            return new Response<>(101, "[" + processName + "]工作流不存在！");
        } else if (list.size() > 1) {
            log.error("[" + projectName + "]项目下存在" + list.size() + "个[" + processName + "]，查询条件：projectId={},processName={}", projectId, processName);
            externalExpansionService.addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，[" + processName + "]工作流存在多个！", "", params);
            return new Response<>(102, "[" + processName + "]工作流存在多个！");
        }
        body.put("id", list.get(0).getId());
        if (null != params) {
            body.put("startParams", params);
        }
        return externalExpansionService.startProcessInstance(httpServletRequest, body, assetToken, token);
    }

    @Log("运行工作流-批量")
    @PostMapping("/externalExpansion/batchStartProcessDefinition")
    public Response<?> batchStartProcess(HttpServletRequest httpServletRequest, @RequestBody JSONObject body, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) {
        User user = ThreadLocalUserUtil.getUser(User.class);
        if (null == user) {
            return new Response<>(100, "token失效，请重新获取。");
        }
        String projectName = body.getString("projectName");
        String processName = body.getString("processName");
        if (null == projectName || null == processName) {
            log.error("缺少必要参数！projectName或processName为空！获取到的body={}", body.toJSONString());
            return new Response<>(106, "缺少必要参数！projectName或processName为空！");
        }
        JSONArray params = body.getJSONArray("params");
        String projectId = projectJoyaFeignService.getQuery().eq("name", projectName).oneValue("id", String.class);
        if (StringUtils.isBlank(projectId)) {
            log.error("[{}]项目不存在", projectName);
            return new Response<>(105, "[" + projectName + "]项目不存在");
        }
        List<ProcessDefinition> list = processDefinitionService.getQuery().eq("projectId", projectId).eq("name", processName).list();
        if (CollectionUtils.isEmpty(list)) {
            log.error("[" + projectName + "]项目下不存在[" + processName + "]工作流，查询条件：projectId={},processName={}", projectId, processName);
            return new Response<>(101, "[" + processName + "]工作流不存在！");
        } else if (list.size() > 1) {
            log.error("[" + projectName + "]项目下存在" + list.size() + "个[" + processName + "]，查询条件：projectId={},processName={}", projectId, processName);
            return new Response<>(102, "[" + processName + "]工作流存在多个！");
        }
        body.put("id", list.get(0).getId());
        if (null != params) {
            body.put("startParamsList", params);
        }
        return externalExpansionService.batchStartProcessInstance(httpServletRequest, body, assetToken, token);
    }


    @Log("停止工作流-新（以实例id为参数）")
    @PostMapping("/externalExpansion/stopProcessInstance")
    public Response<?> stopProcess(@RequestBody JSONObject body, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) throws UnsupportedEncodingException {
        User user = ThreadLocalUserUtil.getUser(User.class);
        //获取当前线程用户为空，认为token是过期的
        if (null == user) {
            return new Response<>(100, "token失效，请重新获取。");
        }
        String processInstanceId = body.getString("processInstanceId");
        //三秒内不允许重复请求
        String redisKey;
        redisKey = PROCESS_STOP + processInstanceId;
        String lock = redisTemplate.opsForValue().get(redisKey);
        if (null != lock) {
            redisTemplate.opsForValue().set(redisKey, "1", 3, TimeUnit.SECONDS);
            return new Response<>(103, "不允许重复提交！");
        }
        redisTemplate.opsForValue().set(redisKey, "1", 3, TimeUnit.SECONDS);

        return externalExpansionService.stopProcessInstanceByInstanceId(processInstanceId, assetToken, token);
    }

    @Log("通过工作实例id获取实例详情和任务实例详情列表")
    @GetMapping("/externalExpansion/getProcessInstanceById")
    public Response<?> getProcessInstanceByInstanceId(@RequestParam String processInstanceId, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) throws UnsupportedEncodingException {
        User user = ThreadLocalUserUtil.getUser(User.class);
        //获取当前线程用户为空，认为token是过期的
        if (null == user) {
            return new Response<>(100, "token失效，请重新获取。");
        }
        return externalExpansionService.getProcessInstanceByInstanceId(processInstanceId, assetToken, token);
    }

    /**
     * @return
     * @throws UnsupportedEncodingException
     */
    @Log("获取运行状态")
    @PostMapping("/externalExpansion/getProcessInstanceState")
    public Response<?> getProcessInstanceState(@RequestBody Map<String, String> body, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) {
        return externalExpansionService.getProcessInstanceState(body, assetToken, token);
    }

    /**
     * @return
     */
    @Log("停止工作流")
    @PostMapping("/externalExpansion/stopProcess")
    public Response<?> stopProcessInstance(@RequestBody Map<String, String> body, @RequestParam(required = false) String assetToken, @RequestParam(required = false) String token) {
        return externalExpansionService.stopProcessInstanceByProcessId(body, assetToken, token);
    }


    @Log("接收事件，启动工作流（永城财险项目使用）")
    @PostMapping("/externalExpansion/receiveEvent")
    public void receiveEvent(@RequestParam String processName, @RequestParam String assetToken, @RequestBody JSONObject body) {
        log.info("接收事件，启动工作流body={}", body.toJSONString());
        externalExpansionService.receiveEvent(processName, assetToken, body);
    }


    /**
     * @param catalogName           目录名称
     * @param processDefinitionName 工作流名称
     * @param sourceBusinessName    源端业务系统名称
     * @param targetBusinessName    目标端业务系统名称
     * @param sourceSimpleName      源端业务系统简称
     * @param targetSimpleName      目标端业务系统简称
     * @param sourceTableName       源表
     * @param targetTableName       目标表
     * @param page                  页数
     * @param pager                 条数
     * @param tenantCode            租户code
     * @return
     */
    @Log("查询已发布的调度任务列表")
    @GetMapping("/getProcessDefinitionList")
    public Response<?> getProcessDefinitionList(@RequestParam(required = false) String catalogueId, @RequestParam(required = false) String catalogName, @RequestParam(required = false) String processDefinitionName,
                                                @RequestParam(required = false) String sourceBusinessName, @RequestParam(required = false) String targetBusinessName,
                                                @RequestParam(required = false) String sourceSimpleName, @RequestParam(required = false) String targetSimpleName,
                                                @RequestParam(required = false) String sourceTableName, @RequestParam(required = false) String targetTableName,
                                                @RequestParam Integer page, @RequestParam Integer pager, @RequestParam String tenantCode) {
        return externalExpansionService.getProcessDefinitionList(catalogueId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName,
                sourceSimpleName, targetSimpleName,
                sourceTableName, targetTableName,
                page, pager, tenantCode);
    }

    /**
     * @param catalogName           目录名称
     * @param processDefinitionName 工作流名称
     * @param sourceBusinessName    源端业务系统名称
     * @param targetBusinessName    目标端业务系统名称
     * @param sourceSimpleName      源端业务系统简称
     * @param targetSimpleName      目标端业务系统简称
     * @param sourceTableName       源表
     * @param targetTableName       目标表
     * @param taskState             运行状态
     * @param taskStartTime         任务执行时间
     * @param page                  页数
     * @param pager                 条数
     * @param tenantCode            租户code
     * @return
     */
    @Log("查询已发布的调度任务列表")
    @GetMapping("/getProcessDefinitionListAndRunState")
    public Response<?> getProcessDefinitionListAndRunState(@RequestParam(required = false) String catalogueId, @RequestParam(required = false) String catalogName, @RequestParam(required = false) String processDefinitionName,
                                                           @RequestParam(required = false) String sourceBusinessName, @RequestParam(required = false) String targetBusinessName,
                                                           @RequestParam(required = false) String sourceSimpleName, @RequestParam(required = false) String targetSimpleName,
                                                           @RequestParam(required = false) String sourceTableName, @RequestParam(required = false) String targetTableName,
                                                           @RequestParam(required = false) String taskState, @RequestParam(required = false) String taskStartTime,
                                                           @RequestParam Integer page, @RequestParam Integer pager, @RequestParam String tenantCode) {
        return externalExpansionService.getProcessDefinitionListAndRunState(catalogueId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName,
                sourceSimpleName, targetSimpleName,
                sourceTableName, targetTableName,
                taskState, taskStartTime,
                page, pager, tenantCode);
    }

    /**
     * @param processDefinitionCode 调度任务id
     * @param tenantCode            租户code
     * @return
     */
    @Log("查询已发布的调度任务详情")
    @GetMapping("/getProcessDefinitionById")
    public Response<?> getProcessDefinitionById(@RequestParam String processDefinitionCode, @RequestParam String tenantCode) {
        try {
            return externalExpansionService.getProcessDefinitionById(processDefinitionCode, tenantCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param processDefinitionCode 调度任务id
     * @param tenantCode            租户code
     * @return
     */
    @Log("查询工作流实例列表")
    @GetMapping("/getProcessInstanceById")
    public Response<?> getProcessInstanceById(@RequestParam String processDefinitionCode, @RequestParam String tenantCode,
                                              @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime,
                                              @RequestParam(required = false) String state,
                                              @RequestParam Integer page, @RequestParam Integer pager) {
        try {
            return externalExpansionService.getProcessInstanceById(processDefinitionCode, tenantCode, startTime, endTime, state, page, pager);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param processDefinitionCode 调度任务id
     * @param tenantCode            租户code
     * @return
     */
    @Log("下载工作流实例列表")
    @GetMapping("/downloadProcessInstanceById")
    public void downloadProcessInstance(@RequestParam String processDefinitionCode, @RequestParam String tenantCode,
                                        @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime,
                                        @RequestParam(required = false) String state,
                                        HttpServletResponse response) {
        try {
            externalExpansionService.downloadProcessInstance(processDefinitionCode, tenantCode, startTime, endTime, state, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Log("查询集成分类树")
    @GetMapping("/getCatalogTree")
    public Response<?> getCatalogTree(@RequestParam String tenantCode) {
        try {
            return externalExpansionService.getCatalogTree(tenantCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Log("镇江医院需求-将任务状态发送到指定接口中")
    @GetMapping("/sendTaskState")
    public Response<?> sendTaskState(@RequestParam String toUrl,
                                     @RequestParam(defaultValue = "POST") String requestType,
                                     @RequestParam(defaultValue = "JSON") String paramType,
                                     @RequestParam(defaultValue = "yesterday") String scope,
                                     @RequestParam(required = false, defaultValue = "param") String getParamName) {
        externalExpansionService.sendTaskState(toUrl, requestType, paramType, scope,getParamName);
        return ResponseFactory.makeSuccess("success");
    }
}
