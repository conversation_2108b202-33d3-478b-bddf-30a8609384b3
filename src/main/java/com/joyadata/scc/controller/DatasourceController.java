package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.dto.DatasourceResponse;
import com.joyadata.scc.dto.ImportDatasourceDTO;
import com.joyadata.scc.service.DatasourceService;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DatasourceController
 * @date 2023/11/3
 */
@CrossOrigin
@RestController
public class DatasourceController {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private DatasourceService datasourceService;

    /**
     * 获取未引用的数据源列表
     *
     * @param productId
     * @param page
     * @param pager
     * @return
     */
    @GetMapping("/datasources/external")
    public Response<?> getMetaDatasourceList(@RequestParam String productId,
                                             @RequestParam String projectId,
                                             @RequestParam Integer page, @RequestParam Integer pager,
                                             @RequestParam(required = false) String searchValue,
                                             @RequestParam(required = false) String frame,
                                             @RequestParam(required = false) Integer status,
                                             @RequestParam(required = false) String dataTypeList) {
        page = page + 1;
        String url = ApplicationContextHelp.getAppGatewayHttp() + Constants.DATASOURCE_LIST + "?pageNum=" + page + "&pageSize=" + pager;
        Map<String, Object> header = new HashMap<>();
        header.put("token", ThreadLocalUserUtil.getCurrentUserToken());
        JSONObject body = new JSONObject();
        body.put("productId", productId);//产品id
        body.put("itemId", projectId);//项目id
        if (StringUtils.isNotBlank(frame)) {
            String[] split = frame.split(",");
            String lastModificationTime = split[0];
            String beginTime = split[1];
            String endTime = split[2];
            body.put("createStartTime", beginTime);//创建开始时间
            body.put("createEndTime", endTime);//创建结束时间
        }
        body.put("filterReference", true);//是否过滤当前产品或项目已引用数据源：false-不过滤，true-过滤（默认false，不过滤）
        body.put("searchValue", searchValue);//数据源名
        body.put("status", status);//0不可连接 1可连接
        if (StringUtils.isNotBlank(dataTypeList)) {
            body.put("dataTypeList", Arrays.asList(dataTypeList.split(",")));//数据源类型
        }
        try {
            DatasourceResponse<List<Map>> datasourceResponse = httpRequestFeignService.post4url(url, body, header, DatasourceResponse.class);
            if (null != datasourceResponse && 200 == datasourceResponse.getCode()) {
                List<Map> mapList = datasourceResponse.getData();
                return ResponseFactory.makeSuccess(mapList, page, pager, datasourceResponse.getTotal().intValue());
            }
            return ResponseFactory.makeError("获取数据源列表失败，报错信息=" + datasourceResponse.getMsg());
        } catch (Exception e) {
            return ResponseFactory.makeError("获取数据源列表失败，报错信息=" + e.getMessage());
        }
    }

    /**
     * 类型条件
     */
    @Auth
    @GetMapping("/datasource/datatype/condition")
    public Response<?> datasourceImport() {
        String url = ApplicationContextHelp.getAppGatewayHttp() + Constants.DATASOURCE_DATATYPE;
        Map<String, Object> header = new HashMap<>();
        header.put("token", ThreadLocalUserUtil.getCurrentUserToken());
        DatasourceResponse<JSONObject> datasourceResponse = httpRequestFeignService.get4url(url, header, DatasourceResponse.class);
        return ResponseFactory.makeSuccess(datasourceResponse.getData());
    }

    /**
     * 引入数据源
     */
    @Auth
    @PostMapping("/datasource/import")
    public Response<?> datasourceImport(@RequestBody ImportDatasourceDTO importDatasourceDTO) {
        return datasourceService.datasourceImport(importDatasourceDTO);
    }


    /**
     * 取消引入数据源
     */
    @Auth
    @PostMapping("/datasource/cancel")
    public Response<?> cancelDatasourceImport(@RequestBody ImportDatasourceDTO importDatasourceDTO) {
        return datasourceService.cancelDatasourceImport(importDatasourceDTO);
    }

    /**
     * 查询我引入的数据源
     */
    @Auth
    @GetMapping("/datasources/external/list")
    public Response<?> datasourceExternalList(@RequestParam String productId,
                                              @RequestParam String projectId,
                                              @RequestParam Integer page,
                                              @RequestParam Integer pager,
                                              @RequestParam(required = false) String frame,
                                              @RequestParam(required = false) String dataTypeList,
                                              @RequestParam(required = false) String searchValue,
                                              @RequestParam(required = false) Integer status) {
        page = page + 1;
        String url = ApplicationContextHelp.getAppGatewayHttp() + Constants.IMPORT_DATASOURCE + "/list?pageNum=" + page + "&pageSize=" + pager;
        Map<String, Object> header = new HashMap<>();
        header.put("token", ThreadLocalUserUtil.getCurrentUserToken());
        JSONObject body = new JSONObject();
        body.put("productId", productId);//产品id
        body.put("itemId", projectId);//项目id
        if (StringUtils.isNotBlank(frame)) {
            String[] split = frame.split(",");
            String lastModificationTime = split[0];
            String beginTime = split[1];
            String endTime = split[2];
            body.put("createStartTime", beginTime);//创建开始时间
            body.put("createEndTime", endTime);//创建结束时间
        }

        body.put("searchValue", searchValue);//数据源名
        if (StringUtils.isNotBlank(dataTypeList)) {
            body.put("dataTypeList", Arrays.asList(dataTypeList.split(",")));//数据源类型
        }
        body.put("status", status);//0不可连接 1可连接

        try {
            DatasourceResponse<List<Map>> datasourceResponse = httpRequestFeignService.post4url(url, body, header, DatasourceResponse.class);
            if (null != datasourceResponse && 200 == datasourceResponse.getCode()) {
                List<Map> mapList = datasourceResponse.getData();
                return ResponseFactory.makeSuccess(mapList, page, pager, datasourceResponse.getTotal().intValue());
            }
            return ResponseFactory.makeError("获取数据源列表失败，报错信息=" + datasourceResponse.getMsg());
        } catch (Exception e) {
            return ResponseFactory.makeError("获取数据源列表失败，报错信息=" + e.getMessage());
        }
    }
}
