package com.joyadata.scc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.dto.ExecuteParam;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Author: zhaoliang
 * @ClassName: ExecutorController
 * @Description: java类作用描述
 * @CreateDate: 2023/11/9 10:58
 * @Version: 1.0
 */
@CrossOrigin
@RestController
@RequestMapping("/executors")
public class ExecutorController {
    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Auth
    @PostMapping(value = "/execute")
    public Response executor(@RequestBody ExecuteParam param) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/projects/" + param.getProjectId() + "/executors/execute";
        Map result = Utils.webClientPost(url, JsonUtil.toMap(param, Map.class), headers.get(Constants.SESSION_ID).toString());
        if (Integer.parseInt(String.valueOf(result.get("code"))) == 0 || result.get("msg").toString().equals("execute process instance error") || Integer.parseInt(String.valueOf(result.get("code"))) == 50006) {
            //终止如果返回工作流错误 其实也是停止掉了返回成功状态
            return ResponseFactory.makeSuccess("success");
        }
        return ResponseFactory.makeError(result.get("msg").toString());
    }
}
