package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.annotation.request.Exists;
import com.joyadata.annotation.request.Frame;
import com.joyadata.annotation.request.GroupBy;
import com.joyadata.annotation.request.MustKeys;
import com.joyadata.annotation.request.NotExists;
import com.joyadata.annotation.request.ParamKeys;
import com.joyadata.annotation.request.Period;
import com.joyadata.annotation.request.SearchBy;
import com.joyadata.annotation.request.ShouldKeys;
import com.joyadata.cms.model.User;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.InCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.dto.TaskDTO;
import com.joyadata.scc.enums.WorkflowExecutionStatus;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionLog;
import com.joyadata.scc.model.ProcessInstance;
import com.joyadata.scc.model.ProcessTaskRelation;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.TaskInstance;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.scc.service.ProcessInstanceService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessInstanceController
 * @date 2023/11/3
 */
@Slf4j
@CrossOrigin
@RestController
public class ProcessInstanceController extends BaseController<ProcessInstance> {
    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private ProcessInstanceService processInstanceService;
    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);


    private Map<String, String> userMap = new HashMap<>();


    @PostConstruct
    public void init() {
        new Thread(() -> {
            userMap = userJoyaFeignService.getQuery().filters("id", "username").map("id", "username");
        }).start();
    }


//    /**
//     * @param projectId 项目id
//     * @param keywords  搜索工作流名字
//     * @param page      分页
//     * @param pager     分页
//     * @param frame     符合框架时间搜索
//     * @return
//     * @throws UnsupportedEncodingException
//     */
//    @Log("工作流实例列表")
//    @Auth
//    @GetMapping("/processInstances/list")
//    public Response<?> queryProcessInstanceList(@RequestParam String projectId,
//                                                @RequestParam(required = false) String processDefinitionId,
//                                                @RequestParam(required = false) String keywords,
//                                                @RequestParam Integer page,
//                                                @RequestParam Integer pager,
//                                                @RequestParam(required = false) String frame) throws UnsupportedEncodingException {
//        Map<String, Object> headers = Utils.getHeader();
//        //海豚中1是第一页
//        int page1 = page + 1;
//        String url = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "?pageNo=" + page1 +
//                "&pageSize=" + pager;
//        //根据模糊搜索条件
//        if (StringUtils.isNotBlank(keywords)) {
//            keywords = URLEncoder.encode(keywords, "UTF-8");
//            url = url + "&searchVal=" + keywords;
//        }
//        //根据工作流id搜索
//        if (StringUtils.isNotBlank(processDefinitionId)) {
//            processDefinitionId = URLEncoder.encode(processDefinitionId, "UTF-8");
//            url = url + "&processDefineCode=" + processDefinitionId;
//        }
//        if (StringUtils.isNotBlank(frame)) {
//            String[] split = frame.split(",");
//            String lastModificationTime = split[0];
//            String paramBeginTime = split[1];
//            String paramEndTime = split[2];
//            String beginTime = paramBeginTime.concat(" 00:00:00");
//            String endTime = paramEndTime.concat(" 23:59:59");
//            String startDate = URLEncoder.encode(beginTime, "UTF-8");
//            String endDate = URLEncoder.encode(endTime, "UTF-8");
//            url = url + "&startDate=" + startDate + "&endDate=" + endDate;
//        }
//        Map resultMap = httpRequestFeignService.get4url(url, headers, Map.class);
//        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
//            throw new AppErrorException("查看工作流实例失败！请联系管理员，报错信息：" + resultMap.get("msg"));
//        }
//        if (Integer.parseInt(String.valueOf(resultMap.get("code"))) != 0) {
//            return ResponseFactory.makeError(resultMap.get("msg").toString());
//        }
//        //返回成框架格式
//        JSONObject jsonObject = JsonUtil.toJSON(resultMap.get("data"));
//        return ResponseFactory.makeSuccess(jsonObject.getJSONArray("totalList"), page, pager, jsonObject.getInteger("total"));
//
//    }


    //    @Log("工作流实例详情")
//    @Auth
//    @GetMapping("/processInstance/{id}")
//    public Response<?> queryProcessInstanceById(@RequestParam String projectId, @PathVariable("id") String id) {
//        Map<String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "/" + id;
//        Map processInstanceResponse = httpRequestFeignService.get4url(url, headers, Map.class);
//        if (Integer.parseInt(String.valueOf(processInstanceResponse.get("code"))) != 0) {
//            throw new AppErrorException("查看工作流实例失败！请联系管理员，报错信息：" + processInstanceResponse.get("msg").toString());
//        }
//        String tasksUrl = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "/" + id + "/tasks";
//        Map tasksResponse = httpRequestFeignService.get4url(tasksUrl, headers, Map.class);
//        if (Integer.parseInt(String.valueOf(tasksResponse.get("code"))) != 0) {
//            throw new AppErrorException("查看工作流实例失败！请联系管理员，报错信息：" + tasksResponse.get("msg").toString());
//        }
//        JSONObject jsonTasksResponse = JsonUtil.toJSON(tasksResponse.get("data"));
//        JSONObject processInstance = JsonUtil.toJSON(processInstanceResponse.get("data"));
//        JSONObject dagData = JsonUtil.toJSON(processInstance.get("dagData"));
//        JSONObject processDefinitionJson = dagData.getJSONObject("processDefinition");
//        List<TaskDTO> taskDTOList = JsonUtil.toBeanList(dagData.get("taskDefinitionList"), TaskDTO.class);
//        List<ProcessTaskRelation> processTaskRelationList = JsonUtil.toBeanList(dagData.get("processTaskRelationList"), ProcessTaskRelation.class);
//
//        ProcessDefinition processDefinition = new ProcessDefinition();
//        //将依赖任务转成调度中心需要的数据格式
//        List<Task> taskList = new ArrayList<>();
//        if (null != taskDTOList && taskDTOList.size() > 0) {
//            for (TaskDTO taskDTO : taskDTOList) {
//                Task task = taskDTO2Task(taskDTO);
//                taskList.add(task);
//            }
//        }
//        if (taskList.size() > 0) {
//            JSONArray jsonArray = JsonUtil.toJSONArray(jsonTasksResponse.get("taskList"));
//            for (Task task : taskList) {
//                for (int i = 0; i < jsonArray.size(); i++) {
//                    JSONObject taskJsonObject = JsonUtil.toJSON(jsonArray.get(i));
//                    if (taskJsonObject.getString("taskCode").equals(task.getId())) {
//                        task.setTaskInstance(taskJsonObject);
//                    }
//                }
//            }
//        }
//        processDefinition.setId(processDefinitionJson.getString("code"));
//        processDefinition.setName(processDefinitionJson.getString("name"));
//        processDefinition.setVersion(processDefinitionJson.getInteger("version"));
//        processDefinition.setDescription(processDefinitionJson.getString("description"));
//        processDefinition.setReleaseState(processDefinitionJson.getString("releaseState"));
//        processDefinition.setGlobalParamMap(processDefinitionJson.getString("globalParams"));
//        processDefinition.setLocations(processDefinitionJson.getString("locations"));
//        processDefinition.setExecutionType(processDefinitionJson.getString("executionType"));
//        processDefinition.setTaskRelationList(processTaskRelationList);
//        processDefinition.setTaskList(taskList);
//        processDefinition.setTasksResponse(jsonTasksResponse);
//        return ResponseFactory.makeSuccess("success", processDefinition);
//    }

    /**
     * 重写列表接口，是为了添加列表添加任务数量
     *
     * @param request
     * @param conditionGroupList
     * @param orConditionGroupList
     * @param groupByCondition
     * @param queryFilter
     * @param page
     * @param pager
     * @return
     */
    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        long start1 = System.currentTimeMillis();
        IQueryWrapper<ProcessInstance> queryWrapper = getService().getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter).page(page, pager);
        List<ProcessInstance> result = queryWrapper.list();
        Integer total = queryWrapper.total();
        log.debug("获取工作流实例列表耗时={}毫秒", System.currentTimeMillis() - start1);
        long start2 = System.currentTimeMillis();
        if (null != result && !result.isEmpty()) {
            //设置执行人
            setExecutorName(result, userMap, false);
            //设置任务参数
            setTaskNum(result);
        }
        log.debug("获取实例下任务数量耗时={}毫秒", System.currentTimeMillis() - start2);
        return ResponseFactory.makeSuccess(result, page, pager, total);
    }


    // 设置执行人

    /**
     * @param isBreak 第一次查询后， 还是没有找到这个人， 避免进入死循环，查询后的下一次有空值也推出
     */
    private void setExecutorName(List<ProcessInstance> result, Map<String, String> userMap, boolean isBreak) {
        Set<String> unInUserMapIds = new HashSet<>();
        result.forEach(t -> {
            String executorId = t.getExecutorId();
            String username = userMap.get(executorId);
            if (null != username) {
                t.setExecutorName(username);
            } else {
                unInUserMapIds.add(executorId);
                if (isBreak) {
                    log.warn("在cms 中没有找到id为:{} 的用户", executorId);
                }
            }
        });
        if (!unInUserMapIds.isEmpty() && !isBreak) {
            Map<String, String> cUserMap = userJoyaFeignService.getQuery().in("id", unInUserMapIds).filters("id", "username").map("id", "username");
            userMap.putAll(cUserMap);
            //重新执行一次
            setExecutorName(result, userMap, true);
        }
    }

    // 设置工作流的任务数量
    private void setTaskNum(List<ProcessInstance> result) {
        result.forEach(t -> {
            String taskIds = t.getTaskIds();
            if (StringUtils.isNotBlank(taskIds)) {
                t.setTaskNum(taskIds.split(",").length);
            } else {
                t.setTaskNum(0);
            }
        });
    }


    private String selectTaskNumSql(List<String> getTaskNumCondition) {
        String sql = "SELECT process_definition_code,process_definition_version\n" +
                "FROM  scc_process_task_relation_log\n" +
                "WHERE (process_definition_code, process_definition_version) IN (%s) GROUP BY post_task_code,process_definition_code,process_definition_version;";
        String join = String.join(",", getTaskNumCondition);
        String format = String.format(sql, join, join);
        log.info("工作流实例列表查询任务数量的sql={}", format);
        return format;
    }

    @Override
    public Response<?> getById(HttpServletRequest request, String id,
                               List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter) {
        Response<?> response = super.getById(request, id, conditionGroupList, orConditionGroupList, groupByCondition,
                queryFilter);
        ProcessInstance processInstance = (ProcessInstance) response.getResult();

        //获取工作流定义
        ProcessDefinitionLog processDefinitionLog = getService(ProcessDefinitionLog.class).getQuery().eq("processDefinitionId",
                processInstance.getProcessDefinitionId()).eq("version", processInstance.getProcessDefinitionVersion()).one();
        if (null == processDefinitionLog) {
            return ResponseFactory.makeError("工作流定义不存在");
        }
        ProcessDefinition processDefinition = new ProcessDefinition();
        BeanUtils.copyProperties(processDefinitionLog, processDefinition);
        processDefinition.setId(processDefinitionLog.getProcessDefinitionId());

        processInstance.setLocations(processDefinition.getLocations());
        processInstanceService.genDagData(processDefinition);


        Map tasksResponse = processInstanceService.getTasksResponse(processDefinition.getProjectId(), id);

        JSONObject jsonTasksResponse = JsonUtil.toJSON(tasksResponse.get("data"));
        processDefinition.setTasksResponse(jsonTasksResponse);
        processDefinition.setCatalogId(processInstance.getCatalogId());
        processDefinition.setCatalogParentIds(processInstance.getCatalogParentIds());
        processDefinition.setProcessInstanceName(processInstance.getName());

        List<Task> taskList = processDefinition.getTaskList();
        if (null != taskList && taskList.size() > 0) {
            JSONArray jsonArray = JsonUtil.toJSONArray(jsonTasksResponse.get("taskList"));
            for (Task task : taskList) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject taskJsonObject = JsonUtil.toJSON(jsonArray.get(i));
                    if (taskJsonObject.getString("taskCode").equals(task.getId())) {
                        task.setTaskInstance(taskJsonObject);
                    }
                }
            }
        }

        return ResponseFactory.makeSuccess(processDefinition);
    }

    private Task taskDTO2Task(TaskDTO taskDTO) {
        Task task = new Task();
        task.setId(taskDTO.getCode());
        task.setName(taskDTO.getName());
        task.setVersion(taskDTO.getVersion());
        task.setDescription(taskDTO.getDescription());
        task.setProjectId(taskDTO.getProjectCode());
        task.setTaskType(taskDTO.getTaskType());
        task.setTaskParams(taskDTO.getTaskParams());
        task.setTaskPriority(taskDTO.getTaskPriority());
        task.setEnvironmentCode(Long.toString(taskDTO.getEnvironmentCode()));
        task.setFailRetryInterval(taskDTO.getFailRetryInterval());
        task.setFailRetryTimes(taskDTO.getFailRetryTimes());
        task.setDelayTime(taskDTO.getDelayTime());
        task.setResourceIds(taskDTO.getResourceIds());
        task.setCpuQuota(taskDTO.getCpuQuota());
        task.setMemoryMax(taskDTO.getMemoryMax());
        task.setFlag(taskDTO.getFlag());
        return task;
    }

    @Log("编辑工作流实例")
    @Auth
    @PutMapping("/processInstance/{id}")
    public Response<?> updateProcessInstanceById(@PathVariable("id") String id, @RequestBody JSONObject json) {
        ProcessDefinition processDefinition = JsonUtil.toBean(json, ProcessDefinition.class);
        if (processDefinition.getTaskRelationList() == null || processDefinition.getTaskRelationList().size() == 0) {
            log.error("未创建节点，保存失败！");
            throw new AppErrorException("未创建节点，保存失败！");
        }
        //校验全局变量中的key是否有重复的
        processDefinitionService.checkGlobalParamsKey(processDefinition);

        ProcessInstance processInstance = initUpdateProcessInstanceBody(processDefinition.getProjectId(), processDefinition);
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", processDefinition.getProjectId()) + "/" + id;
        Map<String, String> params = new HashMap<>();
        params.put("syncDefine", processInstance.getSyncDefine());
        params.put("globalParams", processInstance.getGlobalParams());
        params.put("locations", processInstance.getLocations());
        params.put("timeout", "0");
        params.put("tenantCode", "default");
        params.put("taskRelationJson", processInstance.getTaskRelationJson());
        params.put("taskDefinitionJson", processInstance.getTaskDefinitionJson());
        Map resultMap = Utils.webClientPut(url, params, headers.get(Constants.SESSION_ID).toString());
        return Utils.responseInfo(resultMap);
    }

    /**
     * 将编辑工作流实例的参数转换为海豚需要的参数格式
     *
     * @param projectId
     * @param processDefinition
     * @return
     */
    private ProcessInstance initUpdateProcessInstanceBody(String projectId, ProcessDefinition processDefinition) {
        ProcessInstance processInstance = new ProcessInstance();
        processInstance.setSyncDefine("false");
        processInstance.setProjectCode(projectId);
        processInstance.setTenantCode("default");
        List<Task> taskList = processDefinition.getTaskList();
        //Task->TaskDTO
        List<TaskDTO> taskDTOList = new ArrayList<>();
        if (taskList != null && taskList.size() > 0) {
            for (Task task : taskList) {
                taskDTOList.add(new TaskDTO(task));
            }
        }
        processInstance.setTaskDefinitionJson(JsonUtil.toJSONArray(taskDTOList).toJSONString());
        processInstance.setTaskRelationJson(JsonUtil.toJSONArray(processDefinition.getTaskRelationList()).toJSONString());
        processInstance.setLocations(JsonUtil.toJSONArray(processDefinition.getLocations()).toJSONString());
        processInstance.setGlobalParams(StringUtils.defaultIfBlank(processDefinition.getGlobalParamMap(), "[]"));
        return processInstance;
    }

    private List<ProcessTaskRelation> getProcessTaskRelation(String taskId, Task relyTask, String processDefinitionId) {
        ProcessTaskRelation processTaskRelation = new ProcessTaskRelation();
        processTaskRelation.setPostTaskVersion(1);
        processTaskRelation.setPostTaskCode(relyTask.getId());
        processTaskRelation.setPreTaskCode("0");
        processTaskRelation.setProcessDefinitionCode(processDefinitionId);
        processTaskRelation.setPreTaskVersion(0);
        processTaskRelation.setConditionType("NONE");
        processTaskRelation.setConditionParams(null);

        ProcessTaskRelation processTaskRelation1 = new ProcessTaskRelation();
        processTaskRelation1.setPostTaskVersion(0);
        processTaskRelation1.setPostTaskCode(taskId);
        processTaskRelation1.setPreTaskCode(relyTask.getId());
        processTaskRelation1.setPreTaskVersion(1);
        processTaskRelation1.setProcessDefinitionCode(processDefinitionId);
        processTaskRelation1.setConditionType("NONE");
        processTaskRelation1.setConditionParams(null);
        List<ProcessTaskRelation> list = new ArrayList<>();
        list.add(processTaskRelation);
        list.add(processTaskRelation1);
        return list;
    }

    /**
     * 删除工作流实例
     *
     * @param projectId
     * @param processInstanceIds
     * @return
     */
    @Auth
    @DeleteMapping(value = "/processInstance/batch-delete")
    public Response deleteByProcessInstanceId(@RequestParam(value = "projectId") String projectId,
                                              @RequestBody List<String> processInstanceIds) {
        Assert.notEmpty(processInstanceIds, "请求参数不能为空！");
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_INSTANCES_BATCH_DELETE.replace("{projectCode}", projectId) + "?processInstanceIds=" + StringUtils.join(processInstanceIds, ",");
        Map map = httpRequestFeignService.post4url(url, null, headers, Map.class);
        Response response = Utils.responseInfo(map);
        if (response.getCode() != 0
                && !response.getMessage().contains("does not exist")) {//忽略海豚报工作流不存在的错误
            log.error("工作流实例删除失败，错误信息是：{}", response.getMessage());
            throw new AppErrorException("工作流实例删除失败！请联系管理员，报错信息：" + response.getMessage());
        }
        //定时同步海豚库中的实例到scc时，一个小时后才会删除scc库的实例，所以在这里直接删除scc库中的实例
        getService(ProcessInstance.class).deleteBy(new InCondition("id", processInstanceIds));
        getService(TaskInstance.class).deleteBy(new InCondition("processInstanceId", processInstanceIds));
        return response;
    }

    /**
     * @param projectId
     * @param processDefinitionId
     * @return state{
     * SUBMITTED_SUCCESS: '提交成功',
     * RUNNING_EXECUTION: '正在执行',
     * READY_PAUSE: '准备暂停',
     * PAUSE: '暂停',
     * READY_STOP: '准备停止',
     * STOP: '停止',
     * FAILURE: '失败',
     * SUCCESS: '成功',
     * // NEED_FAULT_TOLERANCE: '需要容错',
     * KILL: 'KILL',
     * // WAITING_THREAD: '等待线程',
     * // WAITING_DEPEND: '等待依赖',
     * DELAY_EXECUTION: '延时执行',
     * // FORCED_SUCCESS: '强制成功',
     * SERIAL_WAIT: '串行等待',
     * DISPATCH: '派发',
     * PENDING: '挂起',
     * }
     */
    @Log("查看工作流最新实例状态")
    @Auth
    @GetMapping("/processInstance/{processDefinitionId}/state")
    public Response<?> queryProcessInstanceState(@RequestParam String projectId, @PathVariable("processDefinitionId") String processDefinitionId) {
        //根据工作流id，查工作流实例最新一条状态,分页直接查第一页第一条
        String url = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "?pageNo=1" +
                "&pageSize=1&processDefineCode=" + processDefinitionId;
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
            log.error("查看工作流实例状态失败！请联系管理员，报错信息：{}", resultMap);
            throw new AppErrorException("查看工作流实例状态失败！请联系管理员，报错信息：" + resultMap.get("msg"));
        }
        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(resultMap.get("data")));
        JSONArray totalList = data.getJSONArray("totalList");
        String state = StringUtils.EMPTY;
        if (totalList.size() > 0) {
            JSONObject processInstance = JSONObject.parseObject(JSONObject.toJSONString(totalList.get(0)));
            state = processInstance.getString("state");
        }
        return ResponseFactory.makeSuccess("success", state);
    }

    @Log("查看工作流中任务实例状态")
    @Auth
    @GetMapping("/processInstance/{processInstanceId}/tasks")
    public Response<?> queryProcessInstanceTasks(@RequestParam String projectId, @PathVariable("processInstanceId") String processInstanceId) {
        return ResponseFactory.makeSuccess(processInstanceService.getTasksResponse(projectId, processInstanceId));
    }

    @Log("查询当前工作流实例存储策略")
    @Auth
    @GetMapping("/processInstance/getStrategy")
    public Response<?> getStrategy(@RequestParam String projectId) {
        return processInstanceService.getStrategy(projectId);
    }

    @Log("工作流实例存储策略")
    @Auth
    @PostMapping("/processInstance/saveStrategy")
    public Response<?> saveStrategy(HttpServletRequest request, @RequestBody JSONObject body) {
        return processInstanceService.saveStrategy(request, body);
    }

    @Log("开启或关闭工作流实例存储策略")
    @Auth
    @PutMapping("/processInstance/saveStrategy/OpenOrClose")
    public Response<?> saveStrategyOpenOrClose(HttpServletRequest request, @RequestBody JSONObject body) {
        return processInstanceService.saveStrategyOpenOrClose(request, body);
    }

    /**
     * 工作流实例搜索后 统计
     */
    @Auth
    @GetMapping({"/processInstance/statistics"})
    public Response<JSONObject> getList(@SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList, @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList, @GroupBy GroupByCondition groupByCondition, @com.joyadata.annotation.request.QueryFilter com.joyadata.model.sql.QueryFilter queryFilter,
                                        @RequestParam(required = false) String state) throws Exception {

        JSONObject result = new JSONObject();
        IQueryWrapper<ProcessInstance> totalQueryWrapper = processInstanceService.getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);
        result.put("total", totalQueryWrapper.total());
        //拷贝一个新的，不影响原来的条件获取总数
        List<ConditionGroup> newConditionGroupList = getNewConfitionGroupList(conditionGroupList);
        IQueryWrapper<ProcessInstance> runningQueryWrapper = processInstanceService.getQuery().andConditionGroupList(newConditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);
        IQueryWrapper<ProcessInstance> successQueryWrapper = processInstanceService.getQuery().andConditionGroupList(newConditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);
        IQueryWrapper<ProcessInstance> failureQueryWrapper = processInstanceService.getQuery().andConditionGroupList(newConditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);

        long runningCount = 0;
        long successCount = 0;
        long failureCount = 0;
        if (StringUtils.isNotBlank(state)) {
            //state=in(2,3) 只需要in条件中的状态
            List<Integer> stateList = getStateList(state);
            if (stateList.contains(WorkflowExecutionStatus.RUNNING_EXECUTION.getCode())) {
                cleanStateCondition(newConditionGroupList);
                runningCount = runningQueryWrapper.eq("state", WorkflowExecutionStatus.RUNNING_EXECUTION.getCode()).total();
            }
            if (stateList.contains(WorkflowExecutionStatus.SUCCESS.getCode())) {
                cleanStateCondition(newConditionGroupList);
                successCount = successQueryWrapper.eq("state", WorkflowExecutionStatus.SUCCESS.getCode()).total();
            }
            if (stateList.contains(WorkflowExecutionStatus.FAILURE.getCode())) {
                cleanStateCondition(newConditionGroupList);
                failureCount = failureQueryWrapper.eq("state", WorkflowExecutionStatus.FAILURE.getCode()).total();
            }
        } else {
            //计算运行中数量
            cleanStateCondition(newConditionGroupList);
            runningCount = runningQueryWrapper.eq("state", WorkflowExecutionStatus.RUNNING_EXECUTION.getCode()).total();
            cleanStateCondition(newConditionGroupList);
            successCount = successQueryWrapper.eq("state", WorkflowExecutionStatus.SUCCESS.getCode()).total();
            cleanStateCondition(newConditionGroupList);
            failureCount = failureQueryWrapper.eq("state", WorkflowExecutionStatus.FAILURE.getCode()).total();
        }
        result.put("runningCount", runningCount);
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);

        return ResponseFactory.makeSuccess(result);
    }

    private List<ConditionGroup> getNewConfitionGroupList(List<ConditionGroup> conditionGroupList) {
        List<ConditionGroup> newList = new ArrayList<>();
        for (ConditionGroup group : conditionGroupList) {
            newList.add(new ConditionGroup(group.getEcs(), group.getIcs(), group.getConditions()));
        }
        return newList;
    }

    private static List<Integer> getStateList(String state) {
        // 1. 提取括号内的内容：`2,3`
        String numbersStr = state.replaceAll(".*\\((.*)\\).*", "$1");

        // 2. 按逗号分割成字符串数组 `["2", "3"]`
        String[] numbersStrArray = numbersStr.split(",");

        // 3. 转换为 int 集合 `[2, 3]`
        List<Integer> stateList = new ArrayList<>();
        for (int i = 0; i < numbersStrArray.length; i++) {
            stateList.add(Integer.parseInt(numbersStrArray[i]));
        }
        return stateList;
    }

    private void cleanStateCondition(List<ConditionGroup> conditionGroupList) {
        if (null != conditionGroupList && conditionGroupList.size() > 0) {
            for (ConditionGroup conditionGroup : conditionGroupList) {
                List<WhereCondition> conditions = conditionGroup.getConditions();
                if (null != conditions && !conditions.isEmpty()) {
                    for (int i = 0; i < conditions.size(); i++) {
                        WhereCondition condition = conditions.get(i);
                        String filed = condition.getFiled();
                        if (StringUtils.isNotBlank(filed) && "state".equals(filed)) {
                            conditions.remove(i);
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理旧数据工作流实例没有目录的，放到对应的目录下
     */
    @GetMapping("/fixProcessInstanceCatalogId")
    public Response<?> fixProcessInstanceCatalogId() {
        processInstanceService.fixProcessInstanceCatalogId();
        return ResponseFactory.makeSuccess("success");
    }


    /**
     * 通过任务实例id获取子节点工作流实例id
     */
    @Auth
    @GetMapping("/processInstance/querySubByParent")
    public Response<JSONObject> querySubProcessInstanceByTaskInstanceId(@RequestParam("projectId") String projectId, @RequestParam("taskInstanceId") String taskInstanceId) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "/query-sub-by-parent?taskId=" + taskInstanceId;
        Map resultMap = httpRequestFeignService.get4url(url, headers, Map.class);
        return Utils.responseInfo(resultMap);
    }

    @Log("强制成功")
    @Auth
    @GetMapping("/processInstances/{id}/force-success")
    public Response forceProcessSuccess(@PathVariable String id, @RequestParam(value = "projectId") String projectId) {
        processInstanceService.forceProcessSuccess(id, projectId);
        return ResponseFactory.makeSuccess("success");
    }


    @Log("概览列表-工作流运行状态")
    @Auth
    @GetMapping("/processInstances/runningState")
    public Response getRunningState(@RequestParam(value = "projectId") String projectId, @RequestParam(value = "startDate") String startDate,
                                    @RequestParam(value = "endDate") String endDate, @RequestParam(value = "type") Integer type) {
        JSONObject jsonObject = processInstanceService.getRunningState(projectId, startDate, endDate, type);
        return ResponseFactory.makeSuccess(jsonObject);
    }

    /**
     * 运维监控-工作流运维（实例列表）-导出excel
     * 1、支持根据id导出（post请求就是为了把多个id放进请求体）
     * 2、支持根据搜索条件导出
     *
     * @param request
     * @param httpResponse
     * @param ids
     * @param conditionGroupList
     * @param orConditionGroupList
     * @param groupByCondition
     * @param queryFilter
     */
    @Log("运维监控-工作流运维（实例列表）-导出excel")
    @Auth
    @PostMapping("/processInstance/exportXlsx")
    public void exportXlsx(HttpServletRequest request, HttpServletResponse httpResponse, @RequestBody(required = false) List<String> ids,
                           @SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList,
                           @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList,
                           @GroupBy GroupByCondition groupByCondition,
                           @com.joyadata.annotation.request.QueryFilter QueryFilter queryFilter) {
        if (!CollectionUtils.isEmpty(ids)) {
            conditionGroupList.add(new ConditionGroup("AND", "AND", new ArrayList<WhereCondition>()).addCondition(new InCondition("id", ids)));
            queryFilter.setSortby(new String[]{"createTime_desc"});
        }
        //调用重写的getList方法查询数据
        Response<List<?>> response = this.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, null, null);
        List<ProcessInstance> processInstanceList = (List<ProcessInstance>) response.getResult();
        try {
            processInstanceService.exportXlsx(request, httpResponse, processInstanceList);
        } catch (IOException e) {
            e.printStackTrace();
            throw new AppErrorException(e.getMessage());
        }
    }

    /**
     * 批量停止工作流实例
     *
     * @return
     */
    @Auth
    @PostMapping("/processInstances/stop/batch")
    public Response<?> batchStop(HttpServletRequest request, @RequestBody List<String> processInstanceIdList) {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        if (null == processInstanceIdList || processInstanceIdList.isEmpty()) {
            log.error("参数processInstanceIdList不能为空");
            throw new AppErrorException("参数processInstanceIdList不能为空");
        }
        List<JSONObject> result = new ArrayList<>();
        List<ProcessInstance> processInstanceList = getService().getQuery().in("id", processInstanceIdList).filters("id", "name", "state").list();

        processInstanceList.forEach(processInstance -> {
            String stop = processInstanceService.stop(projectId, processInstance.getId(), processInstance.getState());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", processInstance.getName());
            if ("success".equals(stop)) {
                jsonObject.put("status", "success");
            } else {
                jsonObject.put("status", "error");
                jsonObject.put("errormsg", stop);
            }
            result.add(jsonObject);
        });
        return ResponseFactory.makeSuccess(result);
    }
}
