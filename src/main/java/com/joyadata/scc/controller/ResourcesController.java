package com.joyadata.scc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.scc.enums.ResourceType;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ResourcesController
 * @date 2023/11/8
 */
@CrossOrigin
@RestController
public class ResourcesController {
    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Auth
    @GetMapping(value = "/resources/list")
    public Response queryResourceList(@RequestParam(value = "type") ResourceType type) {
        Map<String, Object> headers = Utils.getHeader();
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        String url = dolphinscheduler + Constants.RESOURCES_LIST;
        Map map = httpRequestFeignService.get4url(url, JsonUtil.toJSON(params), headers, Map.class);
        return Utils.responseInfo(map);
    }

    /**
     * 获取主程序包,SPARK类型的任务用到的
     *
     * @param type
     * @param programType
     * @return
     */
    @Auth
    @GetMapping(value = "/resources/query-by-type")
    public Response queryResourceJarList(@RequestParam(value = "type") String type,
                                         @RequestParam(value = "programType", required = false) String programType) {
        Map<String, Object> headers = Utils.getHeader();
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("programType", programType);
        String url = dolphinscheduler + Constants.RESOURCES_JAR_LIST;
        Map map = httpRequestFeignService.get4url(url, JsonUtil.toJSON(params), headers, Map.class);
        return Utils.responseInfo(map);
    }
}
