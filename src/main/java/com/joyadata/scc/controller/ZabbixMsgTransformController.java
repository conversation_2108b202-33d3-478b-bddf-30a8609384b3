package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.scc.service.TaskService;
import com.joyadata.tms.model.Project;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/25 9:07.
 * 浙江农信告警对接行内Zabbix，需要将告警信息转为行内指定的格式发给指定接口，因为要查询一些信息后再拼接发送，所以无法使用"信息格式转换"功能
 */
@Slf4j
@CrossOrigin
@RestController
public class ZabbixMsgTransformController {

    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    private JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);
    @Value("${zjnx_zabbix_url:http://*************:9999/third}")
    private String zabbixUrl;
    @Value("${zjnx_zabbix_host:**********}")
    private String host;//生产服务器ip,客户说怕以后机房迁移需要改，所以放在配置文件
    @Value("${zjnx_zabbix_inform_url:http://*************:9999/qywx/jqr/sendMessage}")
    private String zabbixInformUrl;
    @Value("${zjnx_zabbix_inform_key:e5480db5-61d6-4f2b-8a04-c5ac729d6b81}")
    private String informKey;//企业微信机器人发的key

    /**
     * 发送告警和告警恢复，现场只要求工作流失败需要告警和恢复，告警和恢复使用的唯一标识是工作流id
     *
     * @param msg
     */
    @PostMapping("/zabbixMsgTransform")
    public void msgTransform(@RequestBody JSONObject msg) {
        log.info("zabbix告警--->接收发送zabbix告警事件，事件对象={}", msg);
        String eventCode = msg.getString("eventCode");
        Map<String, String> zabbixMag = new HashMap<>();
        if ("B301005".equals(eventCode)) {//工作流失败
            log.info("zabbix告警--->发送zabbix告警事件！");
            //工作流失败告警
            String username = processDefinitionService.getQuery().eq("id", msg.getString("businessId")).oneValue("createByName", String.class);
            String projectName = projectJoyaFeignService.getQuery().eq("id", msg.getString("projectId")).oneValue("name", String.class);
            zabbixMag = initZabbixMag(msg, username, projectName);
            zabbixMag.put("status", "告警");//必须参数；告警状态，用于告警和告警恢复，例如："告警", "恢复"
            zabbixMag.put("level", msg.getString("alarmLevel"));//告警级别，可选，根据实际发送
        } else if ("B301011".equals(eventCode)) {//工作流恢复
            log.info("zabbix告警--->发送zabbix恢复事件！");
            //工作流恢复通知
            String businessId = msg.getString("businessId");
            String username = processDefinitionService.getQuery().eq("id", businessId).oneValue("createByName", String.class);
            String projectName = projectJoyaFeignService.getQuery().eq("id", msg.getString("projectId")).oneValue("name", String.class);
            zabbixMag = initZabbixMag(msg, username, projectName);
            zabbixMag.put("status", "恢复");//必须参数；告警状态，用于告警和告警恢复，例如："告警", "恢复"
        }
        if (zabbixMag.size() > 0) {
            JSONObject result = null;
            try {
                result = httpRequestFeignService.post4url(zabbixUrl, zabbixMag, JSONObject.class);
            } catch (Exception e) {
                log.error("zabbix告警--->发送zabbix告警事件失败！发送内容={}，失败信息={}", zabbixMag, e.getMessage());
                e.printStackTrace();
                return;
            }
            if (null != result && null != result.getInteger("status") && result.getInteger("status") == 0) {
                log.info("zabbix告警--->发送zabbix告警事件成功！发送内容={}", zabbixMag);
            } else {
                log.error("zabbix告警--->发送zabbix告警事件失败！请求结果={}，发送内容={}", result, zabbixMag);
            }
        } else {
            log.info("zabbix告警--->发送内容为空！不调用zabbix接口。");
        }

    }

    /**
     * 发送通知，只通知，没有恢复概念
     *
     * @param msg
     */
    @PostMapping("/zabbixInformMsgTransform")
    public void informMsgTransform(@RequestBody JSONObject msg) {
        log.info("zabbix通知--->接收发送zabbix通知事件，事件对象={}", msg);
        JSONObject informBody = initZabbixInformMag(msg);
        JSONObject result = null;
        try {
            result = httpRequestFeignService.post4url(zabbixInformUrl, informBody, JSONObject.class);
        } catch (Exception e) {
            log.error("zabbix通知--->发送zabbix通知事件失败！发送内容={}，失败信息={}", informBody, e.getMessage());
            e.printStackTrace();
            return;
        }
        if (null != result && null != result.getInteger("status") && result.getInteger("status") == 0) {
            log.info("zabbix通知--->发送zabbix通知事件成功！发送内容={}", informBody);
        } else {
            log.error("zabbix通知--->发送zabbix通知事件失败！请求结果={}，发送内容={}", result, informBody);
        }

    }

    private JSONObject initZabbixInformMag(JSONObject msg) {
        JSONObject body = new JSONObject();
        JSONObject message = new JSONObject();
        JSONObject contentJson = new JSONObject();
        String eventCode = msg.getString("eventCode");
        String content = StringUtils.defaultString(msg.getString("content"), "通知内容为空，事件code=" + eventCode);
        String errorMsg = msg.getString("errorMsg");
        String fullContentStr;
        if ("B202002".equals(eventCode) && StringUtils.isNotEmpty(errorMsg)) {//表字段信息发生变化时，拼接上errorMsg
            fullContentStr = content + "，详情：" + errorMsg;
        } else {
            fullContentStr = content;
        }
        contentJson.put("content", fullContentStr);
        message.put("markdown", contentJson);
        message.put("msgtype", "markdown");
        body.put("key", informKey);
        body.put("message", message);
        return body;
    }

    private Map<String, String> initZabbixMag(JSONObject msg, String username, String projectName) {
        Map<String, String> zabbixMag = new HashMap<>();
        zabbixMag.put("host", host);//host 写死生产的一个ip地址，根据这个固定把告警信息发去企业微信群中
        zabbixMag.put("key", "zjnx_sjjh_task");//必须参数；zbx_key,客户方提供；或双方约定一个值
        zabbixMag.put("@user", StringUtils.defaultString(username, "交换平台管理员"));//扩展，需要艾特的人的名字，目前先取创建人，后续改为指定告警人
        zabbixMag.put("uid", StringUtils.defaultString(msg.getString("businessId"), "001"));//必填参数；告警事件唯一标识，用于对应事件恢复识别
        zabbixMag.put("title", StringUtils.defaultString(msg.getString("title"), "告警标题为空"));//必须参数；告警摘要。
        zabbixMag.put("details", StringUtils.defaultString(msg.getString("content"), "告警内容为空"));//告警描述，详细描述
        zabbixMag.put("time", msg.getJSONObject("evenData").getString("system.now"));
        zabbixMag.put("projectName", projectName);//扩展，项目名称，让zabbix发送企业微信群消息时包含项目名称
        return zabbixMag;
    }

    @PostMapping("/zsp/test")
    public void postTest(@RequestBody JSONObject msg) {
        log.info("接收到测试对象={}", msg);
    }

    @GetMapping("/zsp/test")
    public void getTest(@RequestParam(required = false) String processInstanceId, @RequestParam(required = false) String status, @RequestParam(required = false) String message) {
        // 对URL编码的参数进行解码
        String decodedProcessInstanceId = null;
        String decodedStatus = null;
        String decodedMessage = null;
        
        try {
            if (processInstanceId != null) {
                decodedProcessInstanceId = java.net.URLDecoder.decode(processInstanceId, "UTF-8");
            }
            if (status != null) {
                decodedStatus = java.net.URLDecoder.decode(status, "UTF-8");
            }
            if (message != null) {
                decodedMessage = java.net.URLDecoder.decode(message, "UTF-8");
            }
        } catch (Exception e) {
            log.error("URL解码失败: {}", e.getMessage());
        }
        
        log.info("接收到测试参数(原始)={},{},{}", processInstanceId, status, message);
        log.info("接收到测试参数(解码后)={},{},{}", decodedProcessInstanceId, decodedStatus, decodedMessage);
    }
}
