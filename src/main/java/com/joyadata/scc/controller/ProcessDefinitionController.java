package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.annotation.request.*;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.BaseBean;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.InCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.dto.ProcessExportDTO;
import com.joyadata.scc.dto.TestTask;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionExecTime;
import com.joyadata.scc.model.ProcessInstance;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.model.dto.StartParam;
import com.joyadata.scc.service.CatalogService;
import com.joyadata.scc.service.ProcessDefinitionCatalogService;
import com.joyadata.scc.service.ProcessDefinitionExecTimeService;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinitionController
 * @date 2023/11/3
 */

@Slf4j
@CrossOrigin
@RestController
public class ProcessDefinitionController extends BaseController<ProcessDefinition> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private CatalogService catalogService;
    @Autowired
    private ProcessDefinitionCatalogService processDefinitionCatalogService;


    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, com.joyadata.model.sql.QueryFilter queryFilter, Integer page, Integer pager) {
        String projectId = request.getHeader("projectId");
//        if (StringUtils.isBlank(projectId)) {
//            log.error("项目id不能为空");
//            throw new AppErrorException("项目id不能为空");
//        }
        String[] withs = queryFilter.getWiths();
        boolean hasCronNextExecTime = false;
        if (null != withs) {
            HashSet<String> hashSet = new HashSet(Arrays.asList(withs));
            if (hashSet.contains("cron_next_exec_time")) {
                hashSet.remove("cron_next_exec_time");
                hasCronNextExecTime = true;
            }
        }
        Response<List<?>> response = super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        List<ProcessDefinition> list = (List<ProcessDefinition>) response.getResult();
        if (null != list && !list.isEmpty()) {
            Map<String, String> catalogNameMap = new HashMap<>();
            if (StringUtils.isNotEmpty(projectId)) {
                catalogNameMap = catalogService.getQuery().eq("projectId", projectId).eq("type", 1).eq("level", 1).map("productId", "name");
            }else if(null != request.getParameter("projectId")){
                String paramProjectId = request.getParameter("projectId");
                catalogNameMap = catalogService.getQuery().eq("projectId", paramProjectId).eq("type", 1).eq("level", 1).map("productId", "name");
            }
            //工作流下次执行时间
            List<String> processDefinitionIds = list.stream().map(ProcessDefinition::getId).collect(Collectors.toList());
            Map<String, String> processExecTimeMap = processDefinitionService.getProcessExecTimeMap(hasCronNextExecTime, processDefinitionIds);

            Map<String, String> finalCatalogNameMap = catalogNameMap;
            list.forEach(t -> {
                String processDefinitionId = t.getId();
                if (StringUtils.isNotEmpty(projectId) || null != request.getParameter("projectId")) {
                    //自定义目录defult是在第二级目录
                    String productName = StringUtils.defaultString("default".equals(finalCatalogNameMap.get(t.getProductId())) ? null : finalCatalogNameMap.get(t.getProductId()), "调度中心");
                    t.setProductName(productName);
                }
                //将工作流名称的目录id后缀去掉
                String name = t.getName();
                if (StringUtils.isNotBlank(name)) {
                    if (name.contains("__[") && name.contains("_copy")) {
                        //如果是复制的工作流，要截取中间的一节
                        t.setName(Utils.removeBetween(name, "__", "_copy"));
                    } else if (name.contains("__[")) {
                        t.setName(name.substring(0, name.lastIndexOf("__[")));
                    }
                }
                //赋值下一次执行时间
                t.setCronNextExecTime(processExecTimeMap.get(processDefinitionId));
            });
        }
        return response;
    }


    /**
     * 重写是为了回显时不返回普通任务和依赖任务的关联对象（ProcessTaskRelation）
     *
     * @param request
     * @param id
     * @param conditionGroupList
     * @param orConditionGroupList
     * @param groupByCondition
     * @param queryFilter
     * @return
     */
    @Override
    public Response<?> getById(HttpServletRequest request, String id, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, com.joyadata.model.sql.QueryFilter queryFilter) {
        Response<?> response = super.getById(request, id, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter);
        ProcessDefinition processDefinition = (ProcessDefinition) response.getResult();
        //将工作流名称的目录id后缀去掉
        String name = processDefinition.getName();
        if (StringUtils.isNotBlank(name)) {
            if (name.contains("__[") && name.contains("_copy")) {
                //如果是复制的工作流，要截取中间的一节
                processDefinition.setName(Utils.removeBetween(name, "__", "_copy"));
            } else if (name.contains("__[")) {
                processDefinition.setName(name.substring(0, name.lastIndexOf("__[")));
            }
        }
        return ResponseFactory.makeSuccess(processDefinitionService.initTaskList(processDefinition));
    }

    /***
     * 工作流树形图
     */
    @GetMapping("/process/{id}/viewTree")
    public Response viewTree(@PathVariable("id") String id, @RequestParam(value = "projectId") String projectId) {

        Object result = processDefinitionService.viewTree(id, projectId);
//        Map<String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectCode) + "/" + code
//                + Constants.PROCESS_VIEW_TREE + "?limit=" + limit;
//        Map result = httpRequestFeignService.get4url(url, headers, Map.class);
        return ResponseFactory.makeSuccess("success", result);
    }

    /**
     * 上线下线
     *
     * @param id
     * @return
     */
    @Auth
    @GetMapping("/processDefinition/{id}/release")
    public Response releaseProcessDefinition(@PathVariable("id") String id) {
        processDefinitionService.release(id);
        return ResponseFactory.makeSuccess("success");
    }

    @Log("切换工作流版本")
    @PutMapping("/processDefinition/{id}/switchVersion/{version}")
    public Response<?> switchVersion(@PathVariable("id") String id, @PathVariable("version") Integer version) {
        processDefinitionService.switchVersion(id, version);
        return ResponseFactory.makeSuccess("success");
    }


//    @Log("复制工作流")
//    @GetMapping("/processDefinition/{id}/copyProcessDefinition")
//    public Response<?> copyProcessDefinition(@PathVariable("id") String id) throws UnsupportedEncodingException {
//        processDefinitionService.copyProcessDefinition(id);
//        return ResponseFactory.makeSuccess("success");
//    }

    @Log("批量复制工作流")
    @PostMapping("/processDefinition/copyProcessDefinition")
    public Response<?> copyProcessDefinition(HttpServletRequest request, @RequestBody List<String> ids) {
        String productId = request.getHeader("productId");
        if (StringUtils.isBlank(productId)) {
            log.error("产品id不能为空");
            throw new AppErrorException("产品id不能为空");
        }
        Assert.notNull(ids, "参数不能为空");
        List<JSONObject> result = new ArrayList<>();
        for (String id : ids) {
            ProcessDefinition processDefinition = getService().getById(id);
            String processDefinitionProductId = processDefinition.getProductId();
            if (null != processDefinitionProductId && processDefinitionProductId.equals(productId)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", processDefinition.getName());
                try {
                    processDefinitionService.copyProcessDefinition(processDefinition, productId);
                    jsonObject.put("status", "success");
                    jsonObject.put("errorMsg", null);
                } catch (Exception e) {
                    jsonObject.put("status", "fail");
                    jsonObject.put("errorMsg", e.getMessage());
                }
                result.add(jsonObject);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", processDefinition.getName());
                jsonObject.put("status", "fail");
                jsonObject.put("errorMsg", "工作流中包含非调度中心的任务不允许克隆！");
                result.add(jsonObject);
            }
        }
        return ResponseFactory.makeSuccess(result);
    }

    @Log("运行工作流")
    @PostMapping("/processDefinition/{id}/startProcessInstance")
    public Response<?> startProcessInstance(@PathVariable("id") String id, @RequestBody StartParam startParam) throws UnsupportedEncodingException {
        Map resultMap = processDefinitionService.startProcessInstance(id, startParam, false);
        return Utils.responseInfo(resultMap);
    }

    @Log("批量运行工作流")
    @PostMapping("/processDefinition/batch/startProcessInstance")
    public Response<?> batchStartProcessInstance(@RequestBody List<StartParam> startParams) {
        List<JSONObject> result = processDefinitionService.batchStartProcessInstance(startParams);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 获取子节点
     *
     * @param projectId
     * @return
     */
    @Auth
    @GetMapping("/processDefinitions/simple-list")
    public Response<List<?>> queryProcessDefinitionSimpleList(@RequestParam(value = "projectId") String projectId) {
        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectId) + "/simple-list/filter";
        Map<String, Object> headers = Utils.getHeader();
        Map result = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(String.valueOf(result.get("code"))) == 0) {
            Object data = result.get("data");
            List<JSONObject> maps = new ArrayList<>();
            if (null != data) {
                maps = JSONArray.parseArray(JSONObject.toJSONString(data), JSONObject.class);
                if (null != maps && !maps.isEmpty()) {
                    maps.forEach(t -> {
                        String name = t.getString("name");
                        if (StringUtils.isNotBlank(name)) {
                            if (name.contains("__[") && name.contains("_copy")) {
                                //如果是复制的工作流，要截取中间的一节
                                t.put("name", Utils.removeBetween(name, "__", "_copy"));
                            } else if (name.contains("__[")) {
                                t.put("name", name.substring(0, name.lastIndexOf("__[")));
                            }
                        }
                    });
                }
            }
            return ResponseFactory.makeSuccess(result.get("msg").toString(), maps);
        }
        return ResponseFactory.makeError(result.get("msg").toString());
    }

    @Log("导入工作流")
    @Auth
    @PostMapping("/processDefinition/importProcess")
    public Response<?> importProcess(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("projectId") String projectId) throws UnsupportedEncodingException {
        String productId = request.getHeader("productId");
        if (StringUtils.isBlank(productId)) {
            log.error("产品id不能为空");
            throw new AppErrorException("产品id不能为空");
        }
        List<JSONObject> result = processDefinitionService.importProcess(file, projectId, productId);
        return ResponseFactory.makeSuccess(result);
    }

    @Log("导出工作流")
    @Auth
    @GetMapping("/processDefinition/batch-export")
    public void batchExportProcessDefinitionByIds(@RequestParam(name = "projectId") String projectId,
                                                  @RequestParam(name = "ids") String ids,
                                                  HttpServletResponse servletResponse) {

        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectId) + "/batch-export?codes=" + ids;
        Map<String, Object> headers = Utils.getHeader();
        JSONArray result = httpRequestFeignService.post4url(url, null, headers, JSONArray.class);
        servletResponse.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        String fileName = "workflow_" + System.currentTimeMillis() + ".json";
        servletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);
        BufferedOutputStream buff = null;
        ServletOutputStream out = null;
        try {
            //赋值任务挂载目录id
            //赋值工作流挂载目录id
            List<ProcessExportDTO> processExportDTOList = processDefinitionService.initProcessDefinitionCatalog(JsonUtil.toBeanList(result, ProcessExportDTO.class));
            List<ProcessExportDTO> processExportDTOList1 = processDefinitionService.initTaskCatalog(processExportDTOList);
            out = servletResponse.getOutputStream();
            buff = new BufferedOutputStream(out);
            buff.write(JSONObject.toJSONString(processExportDTOList1).getBytes(StandardCharsets.UTF_8));
            buff.flush();
            buff.close();
        } catch (IOException e) {
            log.error("工作流导出异常,{}", e.getMessage());
            throw new AppErrorException("工作流导出异常！异常信息：{}", e.getMessage());
        } finally {
            if (null != buff) {
                try {
                    buff.close();
                } catch (Exception e) {
                    log.error("关闭工作流导出的输出流异常,{}", e.getMessage());
                    System.err.println("关闭工作流导出的输出流异常，异常信息：" + e.getMessage());
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (Exception e) {
                    System.err.println("关闭工作流导出的输出流异常，异常信息：" + e.getMessage());
                }
            }
        }
    }

    /**
     * 检查工作流是否可删除
     */
    @Auth
    @GetMapping("/processDefinition/checkDelete/{id}")
    public Response<?> checkDelete(@PathVariable String id, @RequestParam(name = "projectId") String projectId) {
        Map map = processDefinitionService.checkDelete(id, projectId);
        if (Integer.valueOf(String.valueOf(map.get("code"))) == 0) {
            return ResponseFactory.makeSuccess(map.get("msg").toString(), map.get("data"));
        }
        return ResponseFactory.makeError("该工作流不可下线删除！", map.get("msg").toString());
    }

    /**
     * 离线任务上线
     */
    @Auth
    @PostMapping("/processDefinition/external")
    public Response<ProcessDefinition> onlineExternal(@RequestBody JSONObject testTaskjson) {
        TestTask testTask = JsonUtil.toBean(testTaskjson, TestTask.class);
        Scheduler scheduleParam = testTask.getScheduleParam();
        JSONObject processDefinitionJson = testTaskjson.getJSONObject("processDefinition");
        ProcessDefinition processDefinition = JsonUtil.toBean(processDefinitionJson, ProcessDefinition.class);
        processDefinition.setUpdateNull(true);
        processDefinition.setJsonKeys(processDefinitionJson.keySet());
        ProcessDefinition result = processDefinitionService.onlineExternal(processDefinition, scheduleParam);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 周期性任务发布
     */
    @Auth
    @PostMapping("/processDefinition/dataIntegration/publish")
    public Response<ProcessDefinition> periodicRelease(@RequestBody JSONObject testTaskjson) throws InterruptedException {
        TestTask testTask = JsonUtil.toBean(testTaskjson, TestTask.class);
        Scheduler scheduleParam = testTask.getScheduleParam();
        JSONObject processDefinitionJson = testTaskjson.getJSONObject("processDefinition");
        ProcessDefinition processDefinition = JsonUtil.toBean(processDefinitionJson, ProcessDefinition.class);
        processDefinition.setUpdateNull(true);
        processDefinition.setJsonKeys(processDefinitionJson.keySet());
        ProcessDefinition result = processDefinitionService.periodicRelease(testTask, processDefinition, scheduleParam);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 周期性任务删除
     */
    @Auth
    @DeleteMapping("/processDefinition/dataIntegration/external/{id}")
    public Response<Integer> periodicDelete(@PathVariable String id, @RequestParam(name = "isForceStop", required = false, defaultValue = "false") Boolean isForceStop) throws InterruptedException {
        Integer result = processDefinitionService.offlineExternal(id, isForceStop);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 离线任务下线
     *
     * @param id          工作流id
     * @param isForceStop 是否强制停止 true/false
     * @return
     * @throws InterruptedException
     */
    @Auth
    @DeleteMapping("/processDefinition/external/{id}")
    public Response<Integer> offlineExternal(@PathVariable String id,
                                             @RequestParam(name = "isForceStop", required = false, defaultValue = "false") Boolean isForceStop) throws InterruptedException {
        Integer result = processDefinitionService.offlineExternal(id, isForceStop);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 任务运维 CDC下线任务
     * 工作流运行 返回工作流实例id
     *
     * @param id
     */
    @Log("CDC停止任务")
    @Auth
    @GetMapping("/processDefinition/{id}/stopProcess")
    public Response<?> stopProcess(@PathVariable("id") String id) {
        Map resultMap = processDefinitionService.stopProcess(id);
        return Utils.responseInfo(resultMap);
    }

    /**
     * 下线
     * 供di服务调用
     * 
     * @param id
     * @return
     */
    @Auth
    @GetMapping("/processDefinition/{id}/offlineProcessDefinition")
    public Response offlineProcessDefinition(@PathVariable("id") String id) {
        ProcessDefinition processDefinition = getService().getById(id);
        if (null == processDefinition) {
            return ResponseFactory.makeSuccess("工作流定义不存在！！！");
        }
        processDefinitionService.offlineProcessDefinition(processDefinition);
        return ResponseFactory.makeSuccess("success");
    }


    /**
     * 上线
     * 供di服务调用
     *
     * @param id
     * @return
     */
    @Auth
    @GetMapping("/processDefinition/{id}/onlineProcessDefinition")
    public Response onlineProcessDefinition(@PathVariable("id") String id) {
        ProcessDefinition processDefinition = getService().getById(id);
        if (null == processDefinition) {
            return ResponseFactory.makeSuccess("工作流定义不存在！！！");
        }
        processDefinitionService.onlineProcessDefinition(processDefinition);
        return ResponseFactory.makeSuccess("success");
    }

    /**
     * 批量上线/下线
     *
     * @param releaseState ONLINE/OFFLINE
     * @param ids          工作流定义id
     * @return
     */
    @Auth
    @PostMapping("/processDefinition/onlineProcessDefinition/batch")
    public Response<List<JSONObject>> batchOnlineProcessDefinition(@RequestParam String releaseState, @RequestBody List<String> ids) {
        return processDefinitionService.batchOnlineProcessDefinition(releaseState, ids);
    }

    /**
     * 批量删除
     *
     * @param processDefinitionIds 工作流ids
     * @return
     */
    @Auth
    @DeleteMapping("/processDefinitions")
    public Response<List<JSONObject>> delete(HttpServletRequest request, @RequestBody List<String> processDefinitionIds) {
        String productId = request.getHeader("productId");
        if (StringUtils.isBlank(productId)) {
            log.error("产品id不能为空");
            throw new AppErrorException("产品id不能为空");
        }
        List<JSONObject> result = new ArrayList<>();
        List<ProcessDefinition> processDefinitionList = getService().getQuery().in("id", processDefinitionIds).list();
        if (null != processDefinitionList && processDefinitionList.size() > 0) {
            for (ProcessDefinition processDefinition : processDefinitionList) {
                if (!productId.equals(processDefinition.getProductId())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", processDefinition.getName());
                    jsonObject.put("status", "error");
                    jsonObject.put("errormsg", "非“调度中心”创建的工作流不允许删除！");
                    result.add(jsonObject);
                } else if (processDefinition.getReleaseState().equals(ProcessDefinition.ReleaseState.online)) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", processDefinition.getName());
                    jsonObject.put("status", "error");
                    jsonObject.put("errormsg", "该工作流已上线，不可删除！");
                    result.add(jsonObject);
                } else {
                    try {
                        processDefinitionService.delete(processDefinition.getId(), processDefinition);
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("name", processDefinition.getName());
                        jsonObject.put("status", "success");
                        jsonObject.put("errormsg", "");
                        result.add(jsonObject);
                    } catch (Exception e) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("name", processDefinition.getName());
                        jsonObject.put("status", "error");
                        jsonObject.put("errormsg", e.getMessage());
                        result.add(jsonObject);
                    }
                }
            }
        }
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 工作流定义挂接目录树
     */
    @Auth
    @GetMapping("/process/catalog/tree")
    public Response<JSONArray> getProcessCatalogTree(HttpServletRequest request, @SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList, @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList, @GroupBy GroupByCondition groupByCondition, @com.joyadata.annotation.request.QueryFilter com.joyadata.model.sql.QueryFilter queryFilter,
                                                     @RequestParam(required = false) Integer page,
                                                     @RequestParam(required = false) Integer pager,
                                                     @RequestParam(required = false) Integer type,
                                                     @RequestParam(required = false) String catalogProductId,
                                                     @RequestParam(required = false) String level,
                                                     @RequestParam(required = false) String sortby) {
        String productId = request.getHeader("productId");
        String projectId = request.getHeader("projectId");
        Assert.notNull(productId, "header产品id不能为空");
        Assert.notNull(projectId, "header项目id不能为空");
        //初始化默认目录
        processDefinitionCatalogService.initDefaultCatalog(productId, projectId);
        //初始化自定义目录
        processDefinitionCatalogService.initCustomCatalog(productId, projectId);
        JSONArray result = processDefinitionService.getProcessCatalogTree(conditionGroupList, orConditionGroupList, groupByCondition,
                queryFilter, page, pager, projectId, type, catalogProductId, level, sortby);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 处理旧数据工作流定义没有目录的，放到defult目录下
     */
    @GetMapping("/fixProcessDefinitionCatalogId")
    public Response<?> fixProcessDefinitionCatalogId() {
        processDefinitionService.fixProcessDefinitionCatalogId();
        return ResponseFactory.makeSuccess("success");
    }

    /**
     * 清除目录下没有任务的目录
     */
    @GetMapping("/cleanEmptyCatalog/process")
    public Response<?> cleanEmptyProcessDefinitionCatalog() {
        processDefinitionCatalogService.cleanEmptyProcessDefinitionCatalog();
        return ResponseFactory.makeSuccess("success");
    }

    /**
     * 导出工作流定义
     * 1、支持根据id导出（post请求就是为了把多个id放进请求体）
     * 2、支持根据搜索条件导出
     * @param request
     * @param httpResponse
     * @param ids
     * @param conditionGroupList
     * @param orConditionGroupList
     * @param groupByCondition
     * @param queryFilter
     */
    @Log("运维监控-工作流检查-导出excel")
    @Auth
    @PostMapping("/processDefinition/exportXlsx")
    public void exportXlsx(HttpServletRequest request, HttpServletResponse httpResponse,@RequestBody(required = false) List<String> ids,
                           @SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList,
                           @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList,
                           @GroupBy GroupByCondition groupByCondition,
                           @com.joyadata.annotation.request.QueryFilter com.joyadata.model.sql.QueryFilter queryFilter) {
        if (!CollectionUtils.isEmpty(ids)) {
            conditionGroupList.add(new ConditionGroup("AND", "AND", new ArrayList<WhereCondition>()).addCondition(new InCondition("id", ids)));
            queryFilter.setSortby(new String[]{"createTime_desc"});
        }
        try {
            Response<List<?>> response = this.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, null, null);
            List<ProcessDefinition> processDefinitionList = (List<ProcessDefinition>) response.getResult();
            processDefinitionService.exportXlsx(request,httpResponse,processDefinitionList);
        } catch (IOException e) {
            e.printStackTrace();
            throw new AppErrorException(e.getMessage());
        }
    }

}