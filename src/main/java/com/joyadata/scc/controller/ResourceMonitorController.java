package com.joyadata.scc.controller;

import com.joyadata.annotation.log.Log;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhuship<PERSON> on 2024/8/2 11:10.
 */
@CrossOrigin
@RestController
public class ResourceMonitorController {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Log("获取master列表")
    @Auth
    @GetMapping("/resource/monitor/masters")
    public Response<?> monitorMasters(){
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.MONITOR_MASTERS;
        Map map = httpRequestFeignService.get4url(url, null, headers, Map.class);
        return Utils.responseInfo(map);
    }

    @Log("获取worker列表")
    @Auth
    @GetMapping("/resource/monitor/workers")
    public Response<?> monitorWorkers(){
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.MONITOR_WORKERS;
        Map map = httpRequestFeignService.get4url(url, null, headers, Map.class);
        return Utils.responseInfo(map);
    }

}
