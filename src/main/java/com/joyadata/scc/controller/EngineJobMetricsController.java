package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.interfaces.ISqlExecutor;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.EngineJobMetrics;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.TaskInstance;
import com.joyadata.scc.service.EngineJobMetericsService;
import com.joyadata.scc.service.TaskService;
import com.joyadata.scc.util.EngineUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Slf4j
@CrossOrigin
@RestController
public class EngineJobMetricsController extends BaseController<EngineJobMetrics> {

    @Autowired
    private EngineJobMetericsService engineJobMetericsService;

    /**
     * 通过instanceId获取最新执行记录
     *
     * @param instanceId
     * @return
     */
    @GetMapping(value = "/engineJobMetrics/instanceId/{instanceId}")
    public Response getByInstanceId(@PathVariable String instanceId) {
        //2024-11-30  处理 group by 语句
//        String sql = "SELECT e1.* FROM scc_engine_job_metrics e1" +
//                " INNER JOIN ( SELECT instance_id, MAX( create_time ) AS mx_create_time FROM scc_engine_job_metrics " +
//                " WHERE instance_id ='%s' GROUP BY pipeline_id ) e2 ON e1.instance_id = e2" +
//                ".instance_id  AND e1.create_time = e2.mx_create_time" +
//                " GROUP BY pipeline_id";

        String sql = "SELECT e1.* FROM scc_engine_job_metrics e1" +
                " INNER JOIN ( SELECT pipeline_id, instance_id, MAX( create_time ) AS mx_create_time FROM scc_engine_job_metrics " +
                " WHERE instance_id ='%s' GROUP BY pipeline_id,instance_id ) e2 ON e1.instance_id = e2" +
                ".instance_id  AND e1.pipeline_id = e2.pipeline_id AND e1.create_time = e2.mx_create_time";
        List<EngineJobMetrics> result = new ArrayList<>();

        List<EngineJobMetrics> engineJobMetricsList = getService().getSqlExecutor().excuteSelect(String.format(sql, instanceId)).list(EngineJobMetrics.class);
        if (null != engineJobMetricsList && engineJobMetricsList.size() > 0) {
            result = engineJobMetricsList;
        }
        //查询任务实例状态，
        String instanceSql = "select id,task_code AS task_id, state, start_time, end_time, submit_time, task_params from business_ds.t_ds_task_instance where id = '" + instanceId + "'";
        TaskInstance taskInstance = getService().getSqlExecutor().excuteSelect(instanceSql).one(TaskInstance.class);
        //查询工作流下有多少个任务，如果未运行的给初始化值
        Map<String, Integer> taskPipelines = new HashMap<>();
        String taskId = String.valueOf(taskInstance.getTaskId());
        taskPipelines.put(taskId, EngineUtils.getSourceSinkCount(taskInstance.getTaskParams()).getV1());
        result.forEach(t -> t.setTaskId(taskId));
        engineJobMetericsService.initEngineJobMetrics(Collections.singletonList(taskInstance), taskPipelines, result);
        //计算读取/写入速率（行/s）
//        engineJobMetericsService.initRowSpeed(result);
        //根据状态排序
        engineJobMetericsService.sortByStatus(result);
        //运行时长如果是是未运行可以为0，其他状态最小给1s
        engineJobMetericsService.initTotalCost(result);
        //计算同步进度
        engineJobMetericsService.figureSyncProgress(result);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 通过instanceIds获取最新执行记录
     * 调度任务运行记录，要查多个节点的运行记录
     *
     * @param instanceIds
     * @return
     */
    @PostMapping(value = "/engineJobMetrics/instanceIds")
    public Response getByInstanceIds(@RequestBody List<String> instanceIds) {
        List<EngineJobMetrics> result = new ArrayList<>();
        //为了返回任务id
        ISqlExecutor sqlExecutor = getService().getSqlExecutor();
        //2024-12-13 因为scc_task_instance视图太慢，直接查海豚原生表
        String taskInstanceSql = "select id,task_code AS task_id from business_ds.t_ds_task_instance where id IN ('%s')";
        List<TaskInstance> taskInstanceList = sqlExecutor.excuteSelect(String.format(taskInstanceSql, StringUtils.join(instanceIds, "','"))).list(TaskInstance.class);

        if (null != taskInstanceList && taskInstanceList.size() > 0) {
            String engineJobMetricsSql = "SELECT e1.* FROM scc_engine_job_metrics e1" +
                    " INNER JOIN ( SELECT pipeline_id, instance_id, MAX( create_time ) AS mx_create_time FROM scc_engine_job_metrics " +
                    " WHERE instance_id IN ('%s') GROUP BY pipeline_id,instance_id ) e2 ON e1.instance_id = e2" +
                    ".instance_id AND e1.pipeline_id = e2.pipeline_id AND e1.create_time = e2.mx_create_time";
            result = sqlExecutor.excuteSelect(String.format(engineJobMetricsSql, StringUtils.join(instanceIds, "','"))).list(EngineJobMetrics.class);
            Map<String, Long> taskInstanceMap = taskInstanceList.stream().collect(Collectors.toMap(TaskInstance::getId, TaskInstance::getTaskId));
            result.forEach(t -> t.setTaskId(taskInstanceMap.get(t.getInstanceId()).toString()));
        }
        //计算读取/写入速率（行/s）
//        engineJobMetericsService.initRowSpeed(result);
        //根据状态排序
        engineJobMetericsService.sortByStatus(result);
        //运行时长如果是是未运行可以为0，其他状态最小给1s
        engineJobMetericsService.initTotalCost(result);
        //计算同步进度
        engineJobMetericsService.figureSyncProgress(result);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 通过instanceIds获取最新执行记录
     * 调度任务运行记录，要查多个节点的运行记录
     *
     * @param processInstanceId
     * @return
     */
    @GetMapping(value = "/engineJobMetrics/processInstanceId/{processInstanceId}/{processDefinitionId}")
    public Response getByInstanceIds(@PathVariable String processInstanceId, @PathVariable String processDefinitionId) {
        List<EngineJobMetrics> result = engineJobMetericsService.getByInstanceIds(processInstanceId, processDefinitionId);
        //计算读取/写入速率（行/s）
//        engineJobMetericsService.initRowSpeed(result);
        //根据状态排序
        engineJobMetericsService.sortByStatus(result);
        //运行时长如果是是未运行可以为0，其他状态最小给1s
        engineJobMetericsService.initTotalCost(result);
        //计算同步进度
        engineJobMetericsService.figureSyncProgress(result);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 供di定时统计调用
     *
     * @param startTime      开始时间时间戳
     * @param endTime        结束时间时间戳
     * @param instanceIdList
     * @return
     */
    @PostMapping(value = "/engineJobMetrics/instanceId")
    public Response getAllByInstanceId(@RequestParam Long startTime, @RequestParam Long endTime, @RequestBody List<String> instanceIdList) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String sql = "SELECT e1.* FROM  scc_engine_job_metrics e1" +
//                " INNER JOIN ( SELECT instance_id, MAX( create_time ) AS mx_create_time FROM scc_engine_job_metrics " +
//                " WHERE create_time > '%s' AND create_time < '%s' AND instance_id IN ( '%s' ) AND `status` in ('FINISHED','FAILED')" +
//                " GROUP BY instance_id, pipeline_id ) e2 " +
//                " ON e1.instance_id = e2.instance_id " +
//                " AND e1.create_time = e2.mx_create_time" +
//                " GROUP BY e1.instance_id,e1.pipeline_id";
        //2024-11-30  处理 group by 语句
        String sql = "SELECT e1.* FROM  scc_engine_job_metrics e1" +
                " INNER JOIN ( SELECT instance_id, MAX( create_time ) AS mx_create_time FROM scc_engine_job_metrics " +
                " WHERE create_time > '%s' AND create_time < '%s' AND instance_id IN ( '%s' ) AND `status` in ('FINISHED','FAILED')" +
                " GROUP BY instance_id, pipeline_id ) e2 " +
                " ON e1.instance_id = e2.instance_id " +
                " AND e1.create_time = e2.mx_create_time";

        List<EngineJobMetrics> result = getService().getSqlExecutor().excuteSelect(String.format(sql,
                dateFormat.format(new Date(startTime)), dateFormat.format(new Date(endTime)), StringUtils.join(instanceIdList, "','"))).list(EngineJobMetrics.class);
        //计算读取/写入速率（行/s）
//        engineJobMetericsService.initRowSpeed(result);
        //根据状态排序
        engineJobMetericsService.sortByStatus(result);
        //运行时长如果是是未运行可以为0，其他状态最小给1s
        engineJobMetericsService.initTotalCost(result);
        //计算同步进度
        engineJobMetericsService.figureSyncProgress(result);
        return ResponseFactory.makeSuccess(result);
    }
}
