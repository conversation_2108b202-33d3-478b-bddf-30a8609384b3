package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.scc.model.TaskGroup;
import com.joyadata.scc.service.EnvironmentService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhushipeng on 2024/6/21 14:04.
 */
@RestController
@CrossOrigin
public class TaskGroupController extends BaseController<TaskGroup> {
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @GetMapping(value = "/task-group/start-task-group")
    public Response startTaskGroup(@RequestParam String id) {
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("id", id);
        String url = dolphinscheduler + Constants.START_TASK_GROUP;
        Map map = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
        return Utils.responseInfo(map);
    }

    @GetMapping(value = "/task-group/close-task-group")
    public Response closeTaskGroup(@RequestParam String id) {
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("id", id);
        String url = dolphinscheduler + Constants.CLOSE_TASK_GROUP;
        Map map = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
        return Utils.responseInfo(map);
    }
}
