package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求测试接口
 * 用于测试不同的Content-Type传参方式
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestHttpController {

    /**
     * 测试 x-www-form-urlencoded 传参方式
     * Content-Type: application/x-www-form-urlencoded
     */
    @PostMapping(value = "/form-urlencoded", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public JSONObject testFormUrlencoded(HttpServletRequest request, @RequestParam Map<String, String> params) {
        log.info("=== 接收到 x-www-form-urlencoded 请求 ===");
        
        // 打印请求头
        log.info("请求头信息:");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            log.info("  {}: {}", headerName, headerValue);
        }
        
        // 打印接收到的参数
        log.info("接收到的参数:");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            log.info("  {}: {}", entry.getKey(), entry.getValue());
        }
        
        // 构造返回结果
        JSONObject result = new JSONObject();
        result.put("status", "success");
        result.put("message", "成功接收x-www-form-urlencoded请求");
        result.put("contentType", request.getContentType());
        result.put("method", request.getMethod());
        result.put("receivedParams", params);
        result.put("timestamp", System.currentTimeMillis());
        
        log.info("返回结果: {}", result.toJSONString());
        return result;
    }

    /**
     * 测试 multipart/form-data 传参方式
     * Content-Type: multipart/form-data
     */
    @PostMapping(value = "/form-data", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Map<String, Object> sendFormDataRequest(HttpServletRequest request, 
                                                  @RequestParam Map<String, String> allParams) {
        Map<String, Object> response = new HashMap<>();
        
        // 打印Content-Type
        String contentType = request.getContentType();
        log.info("接收到的Content-Type: " + contentType);
        
        // 打印所有参数
        log.info("接收到的参数: " + allParams);
        
        // 打印请求方法
        log.info("请求方法: " + request.getMethod());
        
        response.put("status", "success");
        response.put("message", "成功接收Form-Data请求");
        response.put("contentType", contentType);
        response.put("receivedParams", allParams);
        response.put("method", request.getMethod());
        
        return response;
    }

    /**
     * 通用测试接口 - 接收任何类型的POST请求
     * 用于调试和查看实际接收到的数据
     */
    @PostMapping("/debug")
    public JSONObject debugRequest(HttpServletRequest request, @RequestBody(required = false) String body) {
        log.info("=== 接收到调试请求 ===");
        
        // 打印请求信息
        log.info("请求方法: {}", request.getMethod());
        log.info("Content-Type: {}", request.getContentType());
        log.info("请求体: {}", body);
        
        // 打印所有请求头
        log.info("所有请求头:");
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> headers = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
            log.info("  {}: {}", headerName, headerValue);
        }
        
        // 打印所有参数
        log.info("所有参数:");
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> params = new HashMap<>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String paramName = entry.getKey();
            String[] paramValues = entry.getValue();
            String paramValue = paramValues.length > 0 ? paramValues[0] : "";
            params.put(paramName, paramValue);
            log.info("  {}: {}", paramName, paramValue);
        }
        
        // 构造返回结果
        JSONObject result = new JSONObject();
        result.put("status", "success");
        result.put("message", "调试接口 - 成功接收请求");
        result.put("method", request.getMethod());
        result.put("contentType", request.getContentType());
        result.put("headers", headers);
        result.put("params", params);
        result.put("body", body);
        result.put("timestamp", System.currentTimeMillis());
        
        log.info("返回结果: {}", result.toJSONString());
        return result;
    }

    /**
     * 简单测试接口 - 不限制Content-Type
     */
    @PostMapping("/simple")
    public JSONObject simpleTest(HttpServletRequest request) {
        log.info("=== 接收到简单测试请求 ===");
        log.info("Content-Type: {}", request.getContentType());
        log.info("Method: {}", request.getMethod());
        
        JSONObject result = new JSONObject();
        result.put("status", "success");
        result.put("message", "简单测试成功");
        result.put("contentType", request.getContentType());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }
}