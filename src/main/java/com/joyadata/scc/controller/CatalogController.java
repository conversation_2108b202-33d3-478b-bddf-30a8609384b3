package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.Catalog;
import com.joyadata.scc.service.CatalogService;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: Controller
 * @date 2023/11/3
 */
@Slf4j
@CrossOrigin
@RestController
public class CatalogController extends BaseController<Catalog> {

    @Autowired
    private CatalogService catalogService;

    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        String productId = request.getHeader("productId");
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(productId)) {
            log.error("产品id不能为空");
            throw new AppErrorException("产品id不能为空");
        }
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        //初始化默认目录
        catalogService.initDefaultCatalog(productId, projectId);
        //初始化自定义目录
        catalogService.initCustomCatalog(productId, projectId);
//        Response<List<?>> response = super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
//        List<Catalog> catalogList = (List<Catalog>) response.getResult();
//        //计算目录及子级目录下任务数量
//        catalogService.initTaskCount(catalogList, projectId);
        return super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
    }

    /**
     * 删除目录资源
     */
    @Override
    public Response<Integer> delete(HttpServletRequest request, String id, Catalog catalog) {
        List<Catalog> parentIdsList = getService(Catalog.class).getQuery().startsWith("parent_ids", id).list();
        if (null != parentIdsList && parentIdsList.size() > 1) {
            return ResponseFactory.makeError("当前目录下有子级,不允许删除");
        }
        return super.delete(request, id, catalog);
    }


//    /**
//     * 任务运维同步目录接口
//     */
//    @PostMapping(value = "/catalogs/sync")
//    public Response<Integer> syncCatalog(@RequestBody List<JSONObject> list) {
//        String systemToken = ApplicationContextHelp.getSystemToken();
//        ThreadLocalUserUtil.setCode("token", systemToken);
//
////        List<Catalog> rest = new ArrayList<>();
//        for (JSONObject json : list) {
//            String productId = json.getString("productId");
//            String projectId = json.getString("projectId");
//            String tenantCode = json.getString("tenantCode");
//            ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
//            String catalogId = catalogService.initCatalogId(productId, projectId);
//            Catalog c = new Catalog();
//            //匹配属性
//            c.setProductId(productId);
//            c.setProjectId(projectId);
//            c.setId(json.getString("id"));
//            c.setName(json.getString("name"));
//            String pid = json.getString("pid");
//            if ("0".equals(pid)) {
//                c.setPid(catalogId);
//            } else {
//                c.setPid(pid);
//            }
//            c.setType(1);//发送过来的都是默认目录
//            c.setAddOrUpdate(true);
//            getService().add(c);
////            rest.add(c);
//        }
////        getService().add(rest);
//        return ResponseFactory.makeSuccess(Response.Msg.Success);
//    }

    @Override
    public Response<?> add(HttpServletRequest request, String id, Catalog catalog) throws Exception {
        //2024-12-09 检查 同级目录名称不能重复
        List<Catalog> catalogs = catalogService.setIgnorePermissions().getQuery().eq("name", catalog.getName()).eq("pid", catalog.getPid()).list();
        if (null != catalogs && catalogs.size() > 0) {
            return ResponseFactory.makeWarning("该目录已存在！");
        }
        return super.add(request, id, catalog);
    }

    /**
     * 清除目录下没有任务的目录
     */
    @GetMapping("/cleanEmptyCatalog/task")
    public Response<?> cleanEmptyCatalog() {
        catalogService.cleanEmptyCatalog();
        return ResponseFactory.makeSuccess("success");
    }

    /**
     * 处理任务目录，把project这一层的目录改为default
     */
    @GetMapping("/updateTaskCatalogIdDefault")
    public Response<?> updateTaskCatalogIdDefault() {
        catalogService.updateTaskCatalogIdDefault();
        return ResponseFactory.makeSuccess("success");
    }
}

