package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.model.web.Response;
import com.joyadata.scc.model.TaskGroupQueue;
import com.joyadata.scc.service.TaskGroupQueueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/24 13:10.
 */
@RestController
@CrossOrigin
public class TaskGroupQueueController extends BaseController<TaskGroupQueue> {

    @Autowired
    private TaskGroupQueueService taskGroupQueueService;

    @PostMapping("/task-group/forceStart")
    public Response<?> forceStart(String taskGroupQueueId) {
        return taskGroupQueueService.forceStart(taskGroupQueueId);
    }

    @GetMapping("/taskGroupQueues")
    public Response<?> getTaskGroupQueueList(@RequestParam String projectId,
                                             @RequestParam Integer page,
                                             @RequestParam Integer pager,
                                             @RequestParam(required = false) String taskInstanceName,
                                             @RequestParam(required = false) String processInstanceName,
                                             @RequestParam(required = false) String groupId) {
        return taskGroupQueueService.getTaskGroupQueueList(projectId,page,pager,taskInstanceName,processInstanceName,groupId);
    }
}
