package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.joyadata.annotation.log.Log;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.service.SchedulerService;
import com.joyadata.scc.util.CronUtils;
import com.joyadata.scc.util.DateUtils;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: SchedulerController
 * @date 2023/11/3
 */
@Slf4j
@CrossOrigin
@RestController
public class SchedulerController extends BaseController<Scheduler> {

    @Autowired
    private SchedulerService scheduleService;
    @Autowired
    private HttpRequestFeignService httpRequestFeignService;

    @Log("上线、下线")
    @GetMapping("/scheduler/{id}/release")
    public Response<?> release(@PathVariable("id") String id) {
        scheduleService.release(id);
        return ResponseFactory.makeSuccess("success");
    }

    @Log("下线")
    @PostMapping("/scheduler/releaseOffline")
    public Response<?> releaseOffline(@RequestParam String projectId,
                                      @RequestParam String processDefinitionCode,
                                      @RequestParam String tenantCode) {
        AuthUtil.switchToManager(tenantCode);
        Map map = scheduleService.releaseOffline(projectId, processDefinitionCode);
        return ResponseFactory.makeSuccess("success", map);
    }

    /**
     * @param json calendarId: 日历id
     *             crontab: cron表达式
     *             startTime: 开始时间
     *             endTime: 结束时间
     *             projectId: 项目id
     *             times: 次数
     * @return
     */
    @Log("获取cron近的执行时间")
    @PostMapping("/scheduler/getCronExecTime")
    public Response<?> getCronExecTime(@RequestBody JSONObject json) {
        String cron = (String) json.get("crontab");
        int times = (int) json.get("times");
        String calendarId = json.getString("calendarId");
        //如果开始时间时空默认是当前时间
        Date startTime = null == json.getDate("startTime") ? Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()) : json.getDate("startTime");
        //如果结束时间是空默认是100年后
        Date endTime = null == json.getDate("endTime") ? Date.from(LocalDate.now().plusYears(100).atStartOfDay(ZoneId.systemDefault()).toInstant()) : json.getDate("endTime");
        if (null == cron || cron.equals("")) {
            log.error("crontab不可为空");
            throw new AppErrorException("crontab不可为空！");
        }
        List<String> workDays = new ArrayList<>();
        if (StringUtils.isNotBlank(calendarId)) {
            //获取工作日
            String url = getAppGatewayHttp() + "/dedp/v1/cms/calendar/" + calendarId + "/workdays?times=" + Year.now().length();
            List<String> response = httpRequestFeignService.get4url(url, new TypeReference<List<String>>() {
            }, true);
            if (null != response) {
                workDays = response;
            }
        }
        boolean validExpression = CronExpression.isValidExpression(cron);
        if (validExpression) {
            List<String> result = new ArrayList<>();
            try {
                List<Date> dates = CronUtils.filterWorkDays(cron, startTime, endTime, "Asia/Shanghai", new Date(), calendarId, workDays, times);
                if (!dates.isEmpty()) {
                    for (Date date : dates) {
                        result.add(DateUtils.dateToString(date));
                    }
                }
            } catch (Exception exception) {
                log.error("解析失败,错误是 {}", exception.getMessage());
                return ResponseFactory.makeError("解析失败:" + exception.getMessage());
            }
            return ResponseFactory.makeSuccess("success", result);
        }
        return ResponseFactory.makeError("cron表达式格式错误，请检查！");
    }
}
