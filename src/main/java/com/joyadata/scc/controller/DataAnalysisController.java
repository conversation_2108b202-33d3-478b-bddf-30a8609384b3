package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.enums.TaskExecutionStatus;
import com.joyadata.scc.enums.WorkflowExecutionStatus;
import com.joyadata.scc.service.ProcessInstanceService;
import com.joyadata.scc.service.TaskInstanceService;
import com.joyadata.scc.util.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DataAnalysisController
 * @date 2023/11/3
 */
@CrossOrigin
@RestController
@RequestMapping("/analysis")
public class DataAnalysisController {
    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private TaskInstanceService taskInstanceService;

    /**
     * 任务状态统计
     *
     * @param startDate
     * @param endDate
     * @param projectId
     * @return
     * @throws UnsupportedEncodingException
     */
    @Auth
    @GetMapping(value = "/countTask")
    public Response countTaskState(@RequestParam(value = "startDate", required = false) String startDate,
                                   @RequestParam(value = "endDate", required = false) String endDate,
                                   @RequestParam(value = "projectId", required = false, defaultValue = "0") long projectId) throws UnsupportedEncodingException {
//        Map <String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + "/projects/analysis/task-state-count?startDate=" + URLEncoder.encode(startDate, "UTF-8")
//                + "&endDate=" + URLEncoder.encode(endDate, "UTF-8") + "&projectCode=" + projectId;
//        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
//        return Utils.responseInfo(map);
        //拼成海豚返回的结构，这样前端不需要修改
        JSONObject result = new JSONObject();
        List<JSONObject> data = new ArrayList<>();
        // 定义所有需要统计的状态
        List<Integer> allStates = Arrays.asList(0, 1, 3, 6, 7, 9, 12, 17);
        // 查询有数据的状态统计
        List<JSONObject> jsonObjectList = taskInstanceService.getQuery()
                .in("state", allStates)
                .eq("project_id", projectId)
                .ge("start_time", startDate)
                .le("start_time", endDate)
                .groupCount("state")
                .listJson();
        
        // 将查询结果转换为Map，方便查找
        Map<Integer, Integer> stateCountMap = new HashMap<>();
        for (JSONObject jsonObject : jsonObjectList) {
            Integer state = jsonObject.getInteger("state");
            Integer count = jsonObject.getInteger("stateCount");
            if (state != null && count != null) {
                stateCountMap.put(state, count);
            }
        }
        
        int total = 0;
        // 遍历所有状态，确保每个状态都有返回值
        for (Integer state : allStates) {
            JSONObject jsonObject1 = new JSONObject();
            Integer count = stateCountMap.getOrDefault(state, 0); // 没有数据的状态返回0
            total += count;
            jsonObject1.put("taskStateType", TaskExecutionStatus.of(state).name());
            jsonObject1.put("count", count);
            data.add(jsonObject1);
        }
        result.put("totalCount", total);
        result.put("taskCountDtos", data);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 流程状态统计
     *
     * @param startDate
     * @param endDate
     * @param projectId
     * @return
     * @throws UnsupportedEncodingException
     */
    @Auth
    @GetMapping(value = "/processStateCount")
    public Response processStateCount(@RequestParam(value = "startDate", required = false) String startDate,
                                      @RequestParam(value = "endDate", required = false) String endDate,
                                      @RequestParam(value = "projectId", required = false, defaultValue = "0") long projectId) throws UnsupportedEncodingException {
//        Map <String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + "/projects/analysis/process-state-count?startDate=" + URLEncoder.encode(startDate, "UTF-8")
//                + "&endDate=" + URLEncoder.encode(endDate, "UTF-8") + "&projectCode=" + projectId;
//        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
//        return Utils.responseInfo(map);
        //拼成海豚返回的结构，这样前端不需要修改
        JSONObject result = new JSONObject();
        List<JSONObject> data = new ArrayList<>();
        // 定义所有需要统计的状态
        List<Integer> allStates = Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 14);
        // 查询有数据的状态统计
        List<JSONObject> jsonObjectList = processInstanceService.getQuery()
                .in("state", allStates)
                .eq("project_id", projectId)
                .ge("start_time", startDate)
                .le("start_time", endDate)
                .groupCount("state")
                .listJson();

        // 将查询结果转换为Map，方便查找
        Map<Integer, Integer> stateCountMap = new HashMap<>();
        for (JSONObject jsonObject : jsonObjectList) {
            Integer state = jsonObject.getInteger("state");
            Integer count = jsonObject.getInteger("stateCount");
            if (state != null && count != null) {
                stateCountMap.put(state, count);
            }
        }

        int total = 0;
        // 遍历所有状态，确保每个状态都有返回值
        for (Integer state : allStates) {
            JSONObject jsonObject1 = new JSONObject();
            Integer count = stateCountMap.getOrDefault(state, 0); // 没有数据的状态返回0
            total += count;
            jsonObject1.put("taskStateType", WorkflowExecutionStatus.of(state).name());
            jsonObject1.put("count", count);
            data.add(jsonObject1);
        }
        result.put("totalCount", total);
        result.put("taskCountDtos", data);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 流程定义统计
     * @param projectId
     * @return
     */
    @Auth
    @GetMapping(value = "/processDefinitionCount")
    public Response processDefinitionCount(
            @RequestParam(value = "projectId", required = false, defaultValue = "0") long projectId) {
        Map <String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/projects/analysis/define-user-count?projectCode=" + projectId;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        return Utils.responseInfo(map);
    }
}
