package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.pcs.model.MyApp;
import com.joyadata.scc.service.ApiCallRecordService;
import com.joyadata.tms.model.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/17 9:50.
 * http接收告警的自定义接口
 */
@Slf4j
@CrossOrigin
@RestController
public class AlarmReceiveController {
    @Autowired
    private ApiCallRecordService apiCallRecordService;
    private JoyaFeignService<MyApp> myAppJoyaFeignService = FeignFactory.make(MyApp.class);

    @Autowired
    private RestTemplate restTemplate;
    @Value("${push_fail_retries_num:3}")
    private Integer pushFailRetriesNum;

    /**
     * 浙江农信需求：将工作流运行状态推送到我的应用中设置的“访问地址”，请求体原文推送
     * 请求体格式使用告警中的“信息格式转换”处理，不写死，其他项目也可以使用此接口
     *
     * @param jsonObject
     */
    @PostMapping("/processDefinitionStatusPush")
    public void processDefinitionStatusPush(@RequestBody JSONObject jsonObject) {
//        log.info("processDefinitionStatusPush--->接收到推送请求，请求体={}，开始拼装dataops接口请求体", body);
//        JSONObject jsonObject = new JSONObject();
//        JSONObject evenData = body.getJSONObject("evenData");
//        jsonObject.put("instanceId", evenData.getString("processInstanceId"));
//        jsonObject.put("content", body.getString("content"));
//        String eventCode = body.getString("eventCode");
//        if ("B301004".equals(eventCode)) {
//            jsonObject.put("status", 0);
//            jsonObject.put("message", "成功");
//        } else if ("B301005".equals(eventCode)) {
//            jsonObject.put("status", 1);
//            jsonObject.put("message", "失败");
//        }
        log.info("processDefinitionStatusPush--->开始推送运行状态，请求体={}", jsonObject);
        String processInstanceId = jsonObject.getString("instanceId");
        //如果没有使用“信息格式转换”处理请求体，processInstanceId可能为空，则使用evenData中的processInstanceId
        if (null == processInstanceId) {
            JSONObject evenData = jsonObject.getJSONObject("evenData");
            if (null != evenData) {
                processInstanceId = evenData.getString("processInstanceId");
            }
        }
        String appId = apiCallRecordService.setIgnoreTenantCode().setIgnorePermissions().getQuery().eq("processInstanceId", processInstanceId).oneValue("appId", String.class);
        log.info("processDefinitionStatusPush--->获取到的appId={},查询条件processInstanceId={}", appId, processInstanceId);
        if (null == appId) {
            log.error("processDefinitionStatusPush--->获取到的appId为null,退出接口");
            return;
        }
        String urls = myAppJoyaFeignService.setIgnoreTenantCode().setIgnorePermissions().getQuery().eq("id", appId).oneValue("url", String.class);
        log.info("processDefinitionStatusPush--->获取到的urls={}", urls);
        String[] httpUrls = urls.trim().split(",\\s*(?=(?i)https?://)");

        /**
         * 循环调用多个接口url，每个接口都默认失败重试3次，如果请求成功，就全部跳出循环
         */
        boolean shouldBreak = false;  // 用于标记是否需要跳出外层循环
        for (String httpUrl : httpUrls) {
            int retries = 0;
            while (retries < pushFailRetriesNum) {
                try {
                    JSONObject result = restTemplate.postForObject(httpUrl, jsonObject, JSONObject.class);
                    if (null != result && null != result.getInteger("code") && 0 != result.getInteger("code")) {
                        throw new RuntimeException("请求返回code码异常，code=" + result.getInteger("code"));
                    }
                    log.info("processDefinitionStatusPush--->推送结果成功！httpUrl={}", httpUrl);
                    shouldBreak = true;  // 设置标志
                    break;// 如果请求成功，跳出当前URL的重试循环
                } catch (Exception e) {
                    log.error("processDefinitionStatusPush--->[{}]接口请求失败，失败信息={}", httpUrl, e.getMessage());
                    retries++;// 失败则增加重试次数
                    if (retries == pushFailRetriesNum) {
                        // 达到最大重试次数，跳到下一个 URL
                        log.error("processDefinitionStatusPush--->[{}]接口请求失败{}次，尝试请求下一个地址", httpUrl, pushFailRetriesNum);
                    }
                }
            }
            if (shouldBreak) {
                break;
            }
        }
    }
}
