package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.ProcessDefinitionCatalog;
import com.joyadata.scc.service.ProcessDefinitionCatalogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@CrossOrigin
@RestController
public class ProcessDefinitionCatalogController extends BaseController<ProcessDefinitionCatalog> {
    @Autowired
    private ProcessDefinitionCatalogService processDefinitionCatalogService;

    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        String productId = request.getHeader("productId");
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(productId)) {
            log.error("产品id不能为空");
            throw new AppErrorException("产品id不能为空");
        }
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        //初始化默认目录
        processDefinitionCatalogService.initDefaultCatalog(productId, projectId);
        //初始化自定义目录
        processDefinitionCatalogService.initCustomCatalog(productId, projectId);
        return super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
    }

    @Override
    public Response<Integer> delete(HttpServletRequest request, String id, ProcessDefinitionCatalog processDefinitionCatalog) {
        List<ProcessDefinitionCatalog> parentIdsList = processDefinitionCatalogService.getQuery().startsWith("parent_ids", id).list();
        if (null != parentIdsList && parentIdsList.size() > 1) {
            return ResponseFactory.makeError("当前目录下有子级,不允许删除");
        }
        return super.delete(request, id, processDefinitionCatalog);
    }

    @Override
    public Response<?> add(HttpServletRequest request, String id, ProcessDefinitionCatalog processDefinitionCatalog) throws Exception {
        //检查 同级目录名称不能重复
        List<ProcessDefinitionCatalog> catalogs = processDefinitionCatalogService.setIgnorePermissions().getQuery().eq("name", processDefinitionCatalog.getName()).eq("pid", processDefinitionCatalog.getPid()).list();
        if (null != catalogs && catalogs.size() > 0) {
            return ResponseFactory.makeWarning("该目录已存在！");
        }
        return super.add(request, id, processDefinitionCatalog);
    }
}
