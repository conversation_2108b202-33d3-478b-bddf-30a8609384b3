package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.annotation.request.*;
import com.joyadata.cms.model.User;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.dto.TestTask;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.dto.Release;
import com.joyadata.scc.service.CatalogService;
import com.joyadata.scc.service.TaskLogService;
import com.joyadata.scc.service.TaskService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.GeneratorUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskController
 * @date 2023/11/3
 */
@Slf4j
@CrossOrigin
@RestController
public class TaskController extends BaseController<Task> {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private CatalogService catalogService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    private final String DS_KEY = "DEDP:SCHEDULER:TASK:TEST:";

    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, com.joyadata.model.sql.QueryFilter queryFilter, Integer page, Integer pager) {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        Response<List<?>> response = super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        List<Task> result = (List<Task>) response.getResult();
        if (null != result && result.size() > 0) {
            //获取所有产品名,目录名就是产品名,默认二级目录都是产品名
            Map<String, String> catalogNameMap = catalogService.getQuery().eq("projectId", projectId).eq("type", 1).eq("level", 1).map("productId", "name");
            result.forEach(t -> {
                //如果是外置任务，获取产品名称
                //自定义目录defult是在第二级目录
                t.setProductName(StringUtils.defaultString("default".equals(catalogNameMap.get(t.getProductId())) ? null : catalogNameMap.get(t.getProductId()), "调度中心"));
                //将任务名称的目录id后缀去掉
                String name = t.getName();
                if (name.contains("__[") && name.contains("_copy")) {
                    //如果是复制的工作流，要截取中间的一节
                    t.setName(Utils.removeBetween(name, "__", "_copy"));
                } else if (name.contains("__[")) {
                    t.setName(name.substring(0, name.lastIndexOf("__[")));
                }
            });
        }
        return response;
    }

    @Transactional
    @Override
    public Response<?> update(HttpServletRequest request, String id, Task task) throws Exception {
        Integer version = taskLogService.getQuery().eq("taskId", task.getId()).sortbyDesc("version").page(0, 1).oneValue("version", Integer.class);
        //单独修改任务时需要调用海豚的接口添加新版本
        String url = dolphinscheduler + Constants.TASK_DEFINITION.replace("{projectCode}", task.getProjectId()) + "/" + task.getId();
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> params = new HashMap<>();
        params.put("updateVersion", "1");
        params.put("taskDefinitionJsonObj", JSONObject.toJSONString(task));
        Map saveMap = Utils.webClientPut(url, params, headers.get(Constants.SESSION_ID).toString());
        if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
            log.error("修改任务失败,{}", saveMap);
            throw new AppErrorException("修改任务失败！");
        }
        task.setVersion(version + 1);
        return super.update(request, id, task);
    }

    /**
     * 获取任务code
     */
    @Auth
    @GetMapping("/task/generateCode")
    public Response<String> generateCode() {
        return ResponseFactory.makeSuccess("success", GeneratorUtil.genCode());
    }


    /**
     * 任务挂接目录树
     */
    @Auth
    @GetMapping("/task/catalog/tree")
    public Response<JSONArray> getTaskCatalogTree(HttpServletRequest request, @SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList, @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList, @GroupBy GroupByCondition groupByCondition, @QueryFilter com.joyadata.model.sql.QueryFilter queryFilter,
                                                  @RequestParam(required = false) Integer page,
                                                  @RequestParam(required = false) Integer pager,
                                                  @RequestParam(required = false) Integer type,
                                                  @RequestParam(required = false) String sortby
    ) {
        String productId = request.getHeader("productId");
        String projectId = request.getHeader("projectId");
        Assert.notNull(productId, "header产品id不能为空");
        Assert.notNull(projectId, "header项目id不能为空");
        //初始化默认目录
        catalogService.initDefaultCatalog(productId, projectId);
        //初始化自定义目录
        catalogService.initCustomCatalog(productId, projectId);
        JSONArray result = taskService.getTaskCatalogTree(conditionGroupList, orConditionGroupList, groupByCondition,
                queryFilter, page, pager, productId, projectId, type, sortby);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 任务试运行
     * 工作流名称，任务名称修改为  试运行-name-yyyyMMddHHmmss-[temp]
     * 新增的试运行工作流定义，任务 定时任务每天00:00:00清除
     */
    @Auth
    @PostMapping("/task/test")
    public Response testTask(@RequestBody TestTask testTask) throws Exception {
        log.info("试运行----收到测试任务内容是{}", JSON.toJSONString(testTask));
        String newId = GeneratorUtil.genCode();
        taskService.initTestTaskId(testTask, newId);
        //新增
        Map pdResult = taskService.createProcessDefinition(testTask.getProcessDefinition());
        log.info("试运行----新增工作流返回{}", pdResult);
        if (Integer.valueOf(String.valueOf(pdResult.get("code"))) != 0) {
            log.error("试运行----新增出现错误{}", pdResult.get("msg"));
            return Utils.responseInfo(pdResult);
        }

        //---------------------上线---------------------
        Release online = new Release();
        online.setProjectCode(testTask.getProcessDefinition().getProjectId());
        online.setReleaseState("ONLINE");
        Map onlineResult = taskService.onlineOrOffline(online, testTask.getProcessDefinition().getId());
        if (Integer.valueOf(String.valueOf(onlineResult.get("code"))) != 0) {
            log.error("试运行----上线出现错误{}", onlineResult.get("msg"));
            return Utils.responseInfo(onlineResult);
        }
        //---------------------上线---------------------
        //---------------------运行---------------------
        Map runResult = taskService.runTask(testTask.getStartParam());
        if (Integer.valueOf(String.valueOf(runResult.get("code"))) != 0) {
            //运行报错return前要先下线工作流，否则离线开发和海豚的状态不一致
            //---------------------下线---------------------
            Release offline = new Release();
            offline.setProjectCode(testTask.getProcessDefinition().getProjectId());
            offline.setReleaseState("OFFLINE");
            Map offlineResult = taskService.onlineOrOffline(offline, testTask.getProcessDefinition().getId());
            log.info("试运行----下线返回结果是{}", offlineResult);
            if (Integer.valueOf(String.valueOf(offlineResult.get("code"))) != 0) {
                log.error("试运行----下线出现错误{}", offlineResult.get("msg"));
            }
            //---------------------下线---------------------
            log.error("试运行----运行出现错误{}", runResult.get("msg"));
            return Utils.responseInfo(runResult);
        }
        //---------------------运行---------------------
        JSONArray array = null;
        do {
            Thread.sleep(1000L);
            //---------------------先根据流程实例id和开始时间查询工作流实例，然后根据工作流实例id，或者名字查询---------------------
            Map instanceMap = taskService.getProcessInstanceId(testTask.getProcessDefinition().getId(),
                    testTask.getProcessDefinition().getProjectId());//根据code和开始时间查询工作流实例
            if (Integer.valueOf(String.valueOf(instanceMap.get("code"))) != 0) {
                log.error("试运行----查询工作流实例出现错误{}", instanceMap.get("msg"));
                return Utils.responseInfo(instanceMap);
            }
            JSONObject dataJson = (JSONObject) instanceMap.get("data");
            array = dataJson.getJSONArray("totalList");
        } while (null == array || array.isEmpty());

        log.info("试运行----查询工作实例返回array={},pd={},pc={}", JSONObject.toJSONString(array), testTask.getProcessDefinition().getId(), testTask.getProcessDefinition().getProjectId());
        String processInstanceId = "";
        if (null != array && array.size() > 0) {
            JSONObject djson = array.getJSONObject(0);
            processInstanceId = String.valueOf(djson.get("id"));
        }
        //根据id查询任务实例
        String taskInstance = "";
        do {
            Thread.sleep(1000L);
            //获取taskDataJson
            JSONArray taskArray = getTaskDataJson(processInstanceId, testTask.getProcessDefinition().getProjectId());
            log.info("试运行----查询任务实例返回array={},pd={},pc={}", JSONObject.toJSONString(taskArray),
                    testTask.getProcessDefinition().getId(), testTask.getProcessDefinition().getProjectId());
            if (null != taskArray && !taskArray.isEmpty()) {
                JSONObject taskDjson = taskArray.getJSONObject(0);
                taskInstance = String.valueOf(taskDjson.get("id"));
            }
        } while (StringUtils.isBlank(taskInstance));
        //---------------------查看日志---------------------
/*        int logCode = 0;
        do {
            //防止日志还没生成
            Thread.sleep(500L);
            logResult = taskService.showLogger(taskInstance, testTask.getSkipLineNum(), testTask.getLimit());
            logCode = Integer.valueOf(String.valueOf(logResult.get("code")));
        } while (logCode == 10191);
        if (logCode != 0) {
            log.error("试运行----查询工作流实例出现错误{}", logResult.get("msg"));
            return Utils.responseInfo(logResult);
        }*/
        //---------------------查看日志---------------------
        //---------------------下线---------------------
        Release offline = new Release();
        offline.setProjectCode(testTask.getProcessDefinition().getProjectId());
        offline.setReleaseState("OFFLINE");
        Map offlineResult = taskService.onlineOrOffline(offline, testTask.getProcessDefinition().getId());
        log.info("试运行----下线返回结果是{}", offlineResult);
        if (Integer.valueOf(String.valueOf(offlineResult.get("code"))) != 0) {
            log.error("试运行----下线出现错误{}", offlineResult.get("msg"));
            return Utils.responseInfo(offlineResult);
        }
        //返回工作流id
       /* if (Integer.valueOf(String.valueOf(logResult.get("code"))) != 0) {
            return Utils.responseInfo(logResult);
        }*/
        /*JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(logResult.get("data")));
        data.put("processInstanceId", processInstanceId);
        data.put("processDefinitionId", newId);
        logResult.put("data", data);*/
        Map logResult = new HashMap();
        JSONObject data = new JSONObject();
        data.put("processInstanceId", processInstanceId);
        data.put("processDefinitionId", newId);
        data.put("taskInstanceId", taskInstance);
        logResult.put("msg", "success");
        logResult.put("code", 0);
        logResult.put("data", data);
        logResult.put("success", true);
        logResult.put("failed", false);
        return Utils.responseInfo(logResult);
    }

    private JSONArray getTaskDataJson(String processInstanceId, String projectId) {
        Map taskInstanceMap = taskService.getTaskInstance(processInstanceId, projectId);
        if (Integer.valueOf(String.valueOf(taskInstanceMap.get("code"))) != 0) {
            log.error("试运行----查询任务实例出现错误{}", taskInstanceMap.get("msg"));
            throw new RuntimeException("试运行----查询任务实例出现错误:" + taskInstanceMap.get("msg"));
        }
        JSONObject taskDataJson = (JSONObject) taskInstanceMap.get("data");
        return taskDataJson.getJSONArray("totalList");
    }

    @Log("手动任务发布")
    @Auth
    @PostMapping("/task/manualTaskPublish")
    public Response<Task> taskPublish(@RequestBody Task task) {
        Integer update = taskService.manualTaskPublishing(task);
        if (update < 1) {
            return ResponseFactory.makeError("任务发布失败！");
        }
        return ResponseFactory.makeSuccess(task);
    }

    @Log("手动任务删除")
    @Auth
    @DeleteMapping("/task/taskDelete/{id}")
    public Response<?> taskDelete(@PathVariable String id) {
        return ResponseFactory.makeSuccess(taskService.delete(id, new Task()));
    }

    @Log("预览文件内容，只支持文本文件")
    @Auth
    @GetMapping("/task/previewFile")
    public Response<?> previewFile(@RequestParam String path) {
        return taskService.previewFile(path);
    }

    /**
     * 批量删除
     *
     * @param taskIds 任务ids
     * @return
     */
    @Auth
    @DeleteMapping("/tasks")
    public Response<List<JSONObject>> delete(HttpServletRequest request, @RequestBody List<String> taskIds) {
        String productId = request.getHeader("productId");
        if (StringUtils.isBlank(productId)) {
            log.error("产品id不能为空");
            throw new AppErrorException("产品id不能为空");
        }
        List<JSONObject> result = new ArrayList<>();
        List<Task> taskList = getService().getQuery().in("id", taskIds).withs("catalogName", "releaseState", "processDefinitionName").list();
        if (null != taskList && taskList.size() > 0) {
            for (Task task : taskList) {
                if (!productId.equals(task.getProductId())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", task.getName());
                    jsonObject.put("catalogName", task.getCatalogName());
                    jsonObject.put("status", "error");
                    jsonObject.put("errormsg", "非“调度中心”创建的任务不允许删除！");
                    result.add(jsonObject);
                } else if (StringUtils.isNotBlank(task.getReleaseState())) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", task.getName());
                    jsonObject.put("catalogName", task.getCatalogName());
                    jsonObject.put("status", "error");
                    jsonObject.put("errormsg", "请先将当前任务从【 " + task.getProcessDefinitionName() + "】工作流中删除！");
                    result.add(jsonObject);
                } else {
                    try {
                        taskService.delete(task.getId(), task);
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("name", task.getName());
                        jsonObject.put("catalogName", task.getCatalogName());
                        jsonObject.put("status", "success");
                        jsonObject.put("errormsg", "");
                        result.add(jsonObject);
                    } catch (Exception e) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("name", task.getName());
                        jsonObject.put("catalogName", task.getCatalogName());
                        jsonObject.put("status", "error");
                        jsonObject.put("errormsg", e.getMessage());
                        result.add(jsonObject);
                    }
                }
            }
        }
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * @param request
     * @param body    tenantCode 租户code
     *                taskParams 任务参数
     * @return
     */
    @Log("预编译stx任务")
    @Auth
    @PostMapping("/task/preview/stx")
    public Response<String> previewStx(HttpServletRequest request, @RequestBody JSONObject body) {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        return taskService.previewStx(projectId, body);
    }


}
