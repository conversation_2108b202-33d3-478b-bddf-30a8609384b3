package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.TaskType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskTypeController
 * @date 2023/11/7
 */
@CrossOrigin
@RestController
public class TaskTypeController extends BaseController<TaskType> {


    //2025-04-19 类型数据的租户code 是 -1 必须忽略租户才能查询
    // 设置忽略租户后，当前存在当前线程都生鲜，在查询任务数据，需要强制指定租户
    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        Integer total = getService().setIgnoreTenantCode().getTotal(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter);
        List<TaskType> list = getService().setIgnoreTenantCode().getList(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter,
                page, pager);
        if (null != list) {
            String tenantCode = AuthUtil.getCurrentUser().getTenantCode();
            Set<String> nameSet = list.stream().map(TaskType::getName).collect(Collectors.toSet());
            Map<String, Integer> map = getService(Task.class)
                    .setIgnoreTenantCode()
                    .getQuery()
                    .eq("tenantCode", tenantCode)
                    .in("taskType", nameSet)
                    .groupCount("taskType")
                    .filters("taskType")
                    .map("taskType", "taskTypeCount");
            list.forEach(t -> {
                Integer count = map.get(t.getName());
                if (null != count) {
                    t.setTaskCount(count);
                } else {
                    t.setTaskCount(0);
                }
            });
        }
        return ResponseFactory.makeSuccess(list, page, pager, total);
    }
}
