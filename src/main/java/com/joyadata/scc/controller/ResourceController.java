package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.request.*;
import com.joyadata.cms.model.User;
import com.joyadata.controller.BaseController;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.Resource;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.service.ResourceService;
import com.joyadata.scc.util.Utils;
import com.joyadata.util.JsonUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@CrossOrigin
@RestController
public class ResourceController extends BaseController<Resource> {

    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Autowired
    private ResourceService resourceService;

    @Override
    public Response<?> getById(HttpServletRequest request, String id, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, com.joyadata.model.sql.QueryFilter queryFilter) {
        Response<?> response = super.getById(request, id, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter);
        Resource resource = (Resource) response.getResult();
        String fileName = resource.getFileName();
        //页面查看文件详情 不希望看到文件名是携带后缀的
        if (null != fileName && !resource.getIsFolder()) {
            resource.setFileName(fileName.substring(0, fileName.lastIndexOf(".")));
        }
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/resources/" + id + "/view?skipLineNum=" + 0 + "&limit=" + 99999;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(String.valueOf(map.get("code"))) == 20006) {
//            return ResponseFactory.makeWarning("资源文件后缀不支持查看");
            resource.setFileContent("资源文件后缀不支持查看！");
            return response;
        }
        Response result = Utils.responseInfo(map);
        if (result.getCode() == 0) {
            resource.setFileContent(JsonUtil.toJSON(result.getResult()).getString("content"));
            return response;
        } else {
            return result;
        }
    }

    @DeleteMapping("/resource/{id}")
    public Response<?> customDelete(@PathVariable String id) {
        return resourceService.customDelete(id);
    }

    @GetMapping("/resources")
    public Response<List<?>> getList(@SearchBy @Period @Frame @ParamKeys List<ConditionGroup> conditionGroupList,
                                     @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList,
                                     @GroupBy GroupByCondition groupByCondition,
                                     @QueryFilter com.joyadata.model.sql.QueryFilter queryFilter,
                                     @RequestParam(required = false) Integer page,
                                     @RequestParam(required = false) Integer pager,
                                     @RequestParam(required = false) String type,
                                     HttpServletRequest request) {
        Response<List<?>> response = super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        List<Resource> result = (List<Resource>) response.getResult();
        //查询资源目录 注入是否关联任务
        if (null != type && type.equals("UDF") && null != result) {
            long b = System.currentTimeMillis();
            result = result.stream().parallel().map(this::initCount).collect(Collectors.toList());
            long d = System.currentTimeMillis();
            System.err.println("stream循环查询:" + (d - b));
        }
        return ResponseFactory.makeSuccess(result, page, pager, response.getTotal());
    }

//    @Override
//    public Response<?> getById(HttpServletRequest request, String id, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, com.joyadata.model.sql.QueryFilter queryFilter) {
//        Response<?> response = super.getById(request, id, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter);
//        Resource resource = (Resource) response.getResult();
//        String fileName = resource.getFileName();
//        //页面查看文件详情 不希望看到文件名是携带后缀的
//        if (null != fileName && !resource.getIsFolder()) {
//            resource.setFileName(fileName.substring(0, fileName.lastIndexOf(".")));
//        }
//        return response;
//    }

    private Resource initCount(Resource resource) {
        System.err.println(Thread.currentThread().getId() + " : " + resource.getId() + " user:" + ThreadLocalUserUtil.getUser(User.class));
        //是否关联任务, 是true,否false
        Integer taskTotal = getService(Task.class).getQuery().like("resourceIds", resource.getId()).total();
        if (null != taskTotal && taskTotal > 0) {
            resource.setIsRelevanceTask(true);
        } else {
            resource.setIsRelevanceTask(false);
        }
        return resource;
    }

}
