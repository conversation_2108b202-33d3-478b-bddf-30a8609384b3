package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.dto.WorkFlowLineage;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zhaoliang
 * @ClassName: WorkFlowLineageController
 * @CreateDate: 2023/11/8 14:46
 */
@CrossOrigin
@RestController
@RequestMapping("/lineages")
public class WorkFlowLineageController {
    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @Log("工作流关系")
    @Auth
    @GetMapping("/queryWorkFlowList")
    public Response queryWorkFlowList(@RequestParam(value = "projectId") String projectId, @RequestParam(value = "processDefinitionId", required = false) String processDefinitionId) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.LINEAGES.replace("{projectCode}", projectId);
        if (StringUtils.isNotBlank(processDefinitionId)) {
            url = url + "/list/filter/" + processDefinitionId;
        } else {
            url = url + "/list/filter";
        }
        Map result = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(String.valueOf(result.get("code"))) != 0) {
            return ResponseFactory.makeError(result.get("msg").toString());
        }
        JSONObject jsonObject = JsonUtil.toJSON(result.get("data"));
        List<WorkFlowLineage> workFlowLineages = JsonUtil.toBeanList(jsonObject.get("workFlowList"), WorkFlowLineage.class);
        if (StringUtils.isNotBlank(processDefinitionId)) {
            jsonObject.put("workFlowList", initIsSearch(workFlowLineages, processDefinitionId));
        }

        return ResponseFactory.makeSuccess(result.get("msg").toString(), result.get("data"));
    }

    private List<WorkFlowLineage> initIsSearch(List<WorkFlowLineage> workFlowLineages, String processDefinitionId) {
        if (null != workFlowLineages && workFlowLineages.size() > 0) {
            workFlowLineages.forEach(t -> {
                if (processDefinitionId.equals(String.valueOf(t.getWorkFlowCode()))) {
                    t.setIsSearch(1);
                }
            });
        }
        return workFlowLineages;
    }

    private List<WorkFlowLineage> filterWorkFlowLineages(List<WorkFlowLineage> workFlowLineages, List<String> processDefinitionIds) {
        if (null != processDefinitionIds && processDefinitionIds.size() > 0) {
            if (null != workFlowLineages && workFlowLineages.size() > 0) {
                return workFlowLineages.stream().filter(w -> processDefinitionIds.contains(String.valueOf(w.getWorkFlowCode()))).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

//    @Auth
//    @GetMapping("/queryWorkFlowByName")
//    public Response queryWorkFlowByName(@RequestParam(value = "projectId") String projectId,
//                                        @RequestParam(value = "workFlowName", required = false) String workFlowName) {
//        Map<String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + Constants.LINEAGES_QUERY_BY_NAME.replace("{projectCode}", projectId) +
//                "?workFlowName=" + StringUtils.defaultIfBlank(workFlowName, "");
//        Map result = httpRequestFeignService.get4url(url, headers, Map.class);
//        return Utils.responseInfo(result);
//    }

//    @Auth
//    @GetMapping("/{id}")
//    public Response queryWorkFlowByName(@PathVariable String id, @RequestParam(value = "projectId") String projectId) {
//        Map<String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + Constants.LINEAGES.replace("{projectCode}", projectId) + "/" + id;
//        Map result = httpRequestFeignService.get4url(url, headers, Map.class);
//        return Utils.responseInfo(result);
//    }

}
