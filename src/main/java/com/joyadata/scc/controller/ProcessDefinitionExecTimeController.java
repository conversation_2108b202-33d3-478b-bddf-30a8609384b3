package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.ProcessDefinitionExecTime;
import com.joyadata.scc.service.ProcessDefinitionExecTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@CrossOrigin
@RestController
public class ProcessDefinitionExecTimeController extends BaseController<ProcessDefinitionExecTime> {

    @Autowired
    private ProcessDefinitionExecTimeService processDefinitionExecTimeService;

    /**
     * 日历提交/撤回时调用，对应修改工作流下次执行时间
     */
    @Auth
    @GetMapping("/processDefinitionExecTime/calendar/status")
    public Response<?> initProcessExecTimes(@RequestParam(value = "calendarId") String calendarId,
                                            @RequestParam(value = "calendarStatus") Integer calendarStatus) {
        processDefinitionExecTimeService.initProcessExecTimes(calendarId, calendarStatus);
        return ResponseFactory.makeSuccess("success");
    }
}
