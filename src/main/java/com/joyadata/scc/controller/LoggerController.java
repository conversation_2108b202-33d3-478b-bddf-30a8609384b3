package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.service.EngineJobMetericsService;
import com.joyadata.scc.service.TaskService;
import com.joyadata.scc.util.Utils;
import com.joyadata.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: LoggerController
 * @date 2023/12/19
 */
@CrossOrigin
@RestController
@RequestMapping("/log")
public class LoggerController {

    @Autowired
    private TaskService taskService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private EngineJobMetericsService engineJobMetericsService;

    @Log("查看日志")
    @Auth
    @GetMapping("/detail")
    public Response<?> queryLog(@RequestParam(value = "taskInstanceId") String taskInstanceId,
                                @RequestParam(value = "skipLineNum") String skipLineNum,
                                @RequestParam(value = "limit") String limit) {
        Map resultMap = taskService.showLogger(taskInstanceId, skipLineNum, limit);
        return Utils.responseInfo(resultMap);
    }

    @Log("查看引擎日志")
    @Auth
    @GetMapping("/engineDetail")
    public Response<?> queryEngineLog(@RequestParam(value = "taskInstanceId") String taskInstanceId,
                                      @RequestParam(value = "skipLineNum") String skipLineNum,
                                      @RequestParam(value = "limit") String limit) {
        Map resultMap = taskService.showEngineLogger(taskInstanceId, skipLineNum, limit);
        return Utils.responseInfo(resultMap);
    }

//    @Log("下载日志")
//    @Auth
//    @GetMapping("/download")
//    public void downloadTaskLog(@RequestParam(value = "taskInstanceId") String taskInstanceId, HttpServletResponse servletResponse) {
//        Map<String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + Constants.LOG_DETAIL + "?taskInstanceId=" + taskInstanceId + "&limit=99999&skipLineNum=0";
//        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
//        if (Integer.parseInt(String.valueOf(map.get("code"))) != 0) {
//            throw new AppErrorException("下载日志失败！请联系管理员，报错信息：" + map.get("msg").toString());
//        }
//        JSONObject jsonObject = (JSONObject) map.get("data");
//        byte[] messages = jsonObject.get("message").toString().getBytes();
//        servletResponse.setContentType("application/json;charset=UTF-8");
//        String fileName = System.currentTimeMillis() + ".log";
//        servletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);
//        BufferedOutputStream buff = null;
//        ServletOutputStream out = null;
//        try {
//            out = servletResponse.getOutputStream();
//            buff = new BufferedOutputStream(out);
//            buff.write(messages);
//            buff.flush();
//            buff.close();
//        } catch (IOException e) {
//            throw new AppErrorException("下载任务日志异常！异常信息：{}", e.getMessage());
//        } finally {
//            if (null != buff) {
//                try {
//                    buff.close();
//                } catch (Exception e) {
//                    System.err.println("关闭下载任务日志的输出流异常，异常信息：" + e.getMessage());
//                }
//            }
//            if (null != out) {
//                try {
//                    out.close();
//                } catch (Exception e) {
//                    System.err.println("关闭下载任务日志的输出流异常，异常信息：" + e.getMessage());
//                }
//            }
//        }
//    }

    @Log("下载日志")
    @Auth
    @GetMapping("/downloadEngine")
    @ResponseBody
    public ResponseEntity downloadEngineLog(@RequestParam(value = "taskInstanceId") String taskInstanceId) {
        String jobId = engineJobMetericsService.setIgnorePermissions().getQuery().eq("instanceId", taskInstanceId).sortbyDesc("startDate").oneValue("jobId", String.class);
        if (StringUtils.isBlank(jobId)) {
            return ResponseEntity
                    .ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + System.currentTimeMillis() + ".log" + "\"").
                    body(new byte[0]);
        }
        String url = dolphinscheduler + "/log/download-engine-log?taskInstanceId=" + taskInstanceId + "&jobId=" + jobId;
        RestTemplate restTemplate = new RestTemplate();
        // 如果需要认证信息或其他头部信息，添加在这里
        HttpHeaders headers = new HttpHeaders();
        headers.set("sessionId", Utils.getHeader().get("sessionId").toString());
        HttpEntity<Map<String, Object>> entity = new HttpEntity("parameters", headers);
        ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);
        if (response.getStatusCode().is2xxSuccessful()) {
            byte[] logBytes = response.getBody();
            saveToFile(logBytes, "downloaded_log.log");
        } else {
            System.out.println("Failed to download the log file.");
        }
        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + System.currentTimeMillis() + ".log" + "\"").
                body(response.getBody());
    }

    @Log("下载日志")
    @Auth
    @GetMapping("/download")
    @ResponseBody
    public ResponseEntity downloadTaskLog(@RequestParam(value = "taskInstanceId") String taskInstanceId) {
        String url = dolphinscheduler + "/log/download-log?taskInstanceId=" + taskInstanceId;
        RestTemplate restTemplate = new RestTemplate();
        // 如果需要认证信息或其他头部信息，添加在这里
        HttpHeaders headers = new HttpHeaders();
        headers.set("sessionId", Utils.getHeader().get("sessionId").toString());
        HttpEntity<Map<String, Object>> entity = new HttpEntity("parameters", headers);
        ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET, entity, byte[].class);
        if (response.getStatusCode().is2xxSuccessful()) {
            byte[] logBytes = response.getBody();
            saveToFile(logBytes, "downloaded_log.log");
        } else {
            System.out.println("Failed to download the log file.");
        }

        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + System.currentTimeMillis() + ".log" + "\"").
                body(response.getBody());
    }

    private static void saveToFile(byte[] data, String fileName) {
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(data);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Log("解析日志")
    @Auth
    @GetMapping("/analysis")
    public Response<List<String>> queryAnalysisLog(@RequestParam(value = "taskInstanceId") String taskInstanceId) {
        String allLogDetails = getAllLogDetails(taskInstanceId).toString();

        List<String> result = new ArrayList<>();
        //需要解析的日志

        String line;
        //dolp
        String getSourceDatasource = "获取源端数据源";
        String getSinkDatasource = "获取目标端数据源";
        String sinkPreDdl = "执行\"当目标端表存在时\":";
        String createAlterTable = "表不存在时是否建表:";
        String checkCreateTable = "建表后查询表不存在:";
        String createIndex = "创建索引:";
        String syncProgress = "任务同步进度统计:";
        String incrField = "抽取方式:";
        String generateSTXConfig = "生成引擎配置";
        String submitSTXTask = "提交stx任务:";
        String preSql = "前置sql:";
        String postSql = "后置sql:";
        String stxTaskEnd = "stx任务结束:";

        /**
         * stx日志
         * 连接源端目标端成功/失败
         * 执行前置sql成功/失败
         * 执行同步 执行中/执行失败
         * 执行后置sql成功/失败
         *
         */
        String stxLog = "seatunnel:";

        if (StringUtils.isNotBlank(allLogDetails)) {
            BufferedReader br = null;
            try {
                br = new BufferedReader(new StringReader(allLogDetails));
                while ((line = br.readLine()) != null) {
                    if (line.contains(getSourceDatasource)//获取源端数据源
                            || line.contains(getSinkDatasource)//获取目标端数据源
                            || line.contains(sinkPreDdl)//执行"当目标端表存在时"
                            || line.contains(syncProgress)//任务同步进度统计
                            || line.contains(incrField)//抽取方式
                            || line.contains(generateSTXConfig)//生成引擎配置
                    ) {
                        result.add(line);
                    } else if (line.contains(createAlterTable)) { //表不存在时是否建表
                        result.add(line.replace(createAlterTable, ""));
                    } else if (line.contains(checkCreateTable)) { //建表后查询表不存在
                        result.add(line.replace(checkCreateTable, ""));
                    } else if (line.contains(createIndex)) { //创建索引
                        result.add(line.replace(createIndex, ""));
                    } else if (line.contains(submitSTXTask)) {//提交stx任务
                        result.add(line.replace(submitSTXTask, ""));
                    } else if (line.contains(preSql)) {//前置sql
                        result.add(line.replace(preSql, ""));
                    } else if (line.contains(stxLog) && !line.contains("\t")) {//stx连接源端目标端
                        //过滤seatunnel日志
                        result.add(this.subLog(line.replace(stxLog, "")));
                    } else if (line.contains(postSql)) {//后置sql
                        result.add(line.replace(postSql, ""));
                    } else if (line.contains(stxTaskEnd)) {//任务结束
                        result.add(line.replace(stxTaskEnd, ""));
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (br != null) {
                        br.close();
                    }
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
        }
        return ResponseFactory.makeSuccess(filterDuplicates(result.stream().distinct().collect(Collectors.toList())));
    }

    /**
     * 去重包含相同中文内容的日志（" - "），保留第一次出现的日志
     */
    public static List<String> filterDuplicates(List<String> logs) {
        List<String> result = new ArrayList<>();
        Set<String> seenMessages = new HashSet<>();

        for (String log : logs) {
            // 找到" - "的位置
            int index = log.indexOf(" - ");
            if (index == -1) {
                // 如果没有" - "，直接添加到结果中
                result.add(log);
                continue;
            }

            // 提取" - "后面的内容
            String message = log.substring(index + 3);

            // 如果这个内容还没出现过，就保留这个日志
            if (!seenMessages.contains(message)) {
                seenMessages.add(message);
                result.add(log);
            }
        }
        return result;
    }

    //为了对齐日志展示截取中间多余部分
    private String subLog(String line) {
        if (line.contains("信息: ")) {
            line = line.replace("信息: ", "");
        } else if (line.contains(" - ")) {
            line = line.replace(line.substring(line.indexOf(" - "), line.lastIndexOf(" - ")), "");
        }
        return line;
    }

    private StringBuilder getAllLogDetails(String taskInstanceId) {
        StringBuilder logContent = new StringBuilder();
        int skipLineNum = 0;
        final int limit = 1000; // 提取为常量

        while (true) {
            Map<String, Object> logMap = taskService.showLogger(taskInstanceId, String.valueOf(skipLineNum), String.valueOf(limit));

            // 校验响应数据
            if (logMap == null
                    || !logMap.containsKey("code")
                    || Integer.parseInt(String.valueOf(logMap.get("code"))) != 0
                    || !logMap.containsKey("data")) {
                break;
            }

            JSONObject dataJson = JsonUtil.toJSON(logMap.get("data"));
            String message = dataJson.getString("message");
            int lineNumCount = dataJson.getInteger("lineNum");

            // 添加日志内容
            if (StringUtils.isNotBlank(message)) {
                logContent.append(message);
            }

            // 终止条件检查
            if (lineNumCount == 0 || StringUtils.isEmpty(message)) {
                break;
            }

            skipLineNum += lineNumCount;
        }

        return logContent;
    }
}
