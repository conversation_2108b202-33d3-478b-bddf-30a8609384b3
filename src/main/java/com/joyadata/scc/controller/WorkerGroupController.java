package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.cms.model.User;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.WorkerGroup;
import com.joyadata.scc.service.WorkerGroupService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.JsonUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: WorkGroupController
 * @date 2023/11/8
 */
@CrossOrigin
@RestController
public class WorkerGroupController {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private WorkerGroupService workerGroupService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Log("工作分组")
    @GetMapping(value = "/worker-groups/all")
    public Response queryAllWorkerGroupsAll() {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.WORKER_GROUPS_ALL;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        return Utils.responseInfo(map);
    }

    @Log("分页查询工作分组")
    @Auth
    @GetMapping(value = "/worker-groups")
    public Response queryAllWorkerGroups(@RequestParam Integer page,
                                         @RequestParam Integer pager,
                                         @RequestParam(required = false) String keywords) throws UnsupportedEncodingException {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.WORKER_GROUPS_LIST + "?pageNo=" + (page + 1) + "&pageSize=" + pager;
        if (StringUtils.isNotBlank(keywords)) {
            url = url + "&searchVal=" + URLEncoder.encode(keywords, "UTF-8");
        }
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.valueOf(String.valueOf(map.get("code"))) == 0) {
            JSONObject data = JsonUtil.toJSON(map.get("data"));
            return ResponseFactory.makeSuccess(data.getJSONArray("totalList"), page, pager, data.getInteger("total"));
        } else {
            return ResponseFactory.makeError(map.get("msg").toString());
        }
    }

    @Auth
    @Log("工作分组ip端口")
    @GetMapping(value = "/worker-groups/worker-address-list")
    public Response getAddressList() {
        return workerGroupService.getAddressList();
    }

    @Auth
    @Log("修改工作分组")
    @PutMapping(value = "/workerGroup/update")
    public Response<?> updateWorkerGroup(@RequestBody WorkerGroup workerGroup) {
        return workerGroupService.updateWorkerGroup(workerGroup);
    }

    @Auth
    @Log("新增工作分组")
    @PostMapping(value = "/workerGroup/create")
    public Response<?> createWorkerGroup(@RequestBody WorkerGroup workerGroup) {
        return workerGroupService.createWorkerGroup(workerGroup);
    }

    @Auth
    @Log("删除工作分组")
    @DeleteMapping(value = "/workerGroup/delete/{id}")
    public Response<?> deleteWorkerGroup(@PathVariable String id) {
        return workerGroupService.deleteWorkerGroup(id);
    }
}
