package com.joyadata.scc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.Gpfdist;
import com.joyadata.scc.service.GpfdistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/14 19:18.
 */
@Slf4j
@RestController
@CrossOrigin
public class GpfdistController extends BaseController<Gpfdist> {
    @Autowired
    private GpfdistService gpfdistService;
    @Override
    public Response<?> add(HttpServletRequest request, String id, Gpfdist gpfdist) throws Exception {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        boolean addCheck = gpfdistService.addCheck(gpfdist.getGpfdistPath(), projectId, true);
        if (!addCheck){
            return ResponseFactory.makeWarning("gpfdist路径必须全部保持一致！");
        }
        return super.add(request, id, gpfdist);
    }

    @Override
    public Response<?> update(HttpServletRequest request, String id, Gpfdist gpfdist) throws Exception {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            log.error("项目id不能为空");
            throw new AppErrorException("项目id不能为空");
        }
        boolean addCheck = gpfdistService.addCheck(gpfdist.getGpfdistPath(), projectId, false);
        if (!addCheck){
            return ResponseFactory.makeWarning("gpfdist路径必须全部保持一致！");
        }
        return super.update(request, id, gpfdist);
    }
}
