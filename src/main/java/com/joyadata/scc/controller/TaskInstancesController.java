package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.annotation.log.Log;
import com.joyadata.annotation.request.*;
import com.joyadata.cms.model.User;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.enums.TaskExecutionStatus;
import com.joyadata.scc.model.TaskInstance;
import com.joyadata.scc.service.TaskInstanceService;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhaoliang
 * @ClassName: TaskInstancesController
 * @Description: java类作用描述
 * @CreateDate: 2023/11/9 11:06
 * @Version: 1.0
 */
@CrossOrigin
@RestController
public class TaskInstancesController extends BaseController<TaskInstance> {
    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private TaskInstanceService taskInstanceService;
    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);

//    @Log("查看任务实例列表")
//    @Auth
//    @GetMapping("/taskInstances")
//    public Response queryTaskListPaging(@RequestParam(value = "projectId") String projectId,
//                                        @RequestParam(value = "keywords", required = false) String keywords,
//                                        @RequestParam(value = "frame", required = false) String frame,
//                                        @RequestParam(value = "executorName", required = false) String executorName,
//                                        @RequestParam(value = "host", required = false) String host,
//                                        @RequestParam(value = "stateType", required = false) String stateType,
//                                        @RequestParam(value = "processInstanceId", required = false, defaultValue = "0") Integer processInstanceId,
//                                        @RequestParam(value = "processInstanceName", required = false) String processInstanceName,
//                                        @RequestParam(value = "processDefinitionName", required = false) String processDefinitionName,
//                                        @RequestParam(value = "taskName", required = false) String taskName,
//                                        @RequestParam(value = "taskExecuteType", required = false, defaultValue = "BATCH") String taskExecuteType,
//                                        @RequestParam("page") Integer page,
//                                        @RequestParam("pager") Integer pager) throws UnsupportedEncodingException {
//
//        //时间传参按照框架时间搜索传参
//        String startDate = null;
//        String endDate = null;
//        if (StringUtils.isNotBlank(frame)) {
//            String[] split = frame.split(",");
//            String lastModificationTime = split[0];
//            String paramBeginTime = split[1];
//            String paramEndTime = split[2];
//            String beginTime = paramBeginTime.concat(" 00:00:00");
//            String endTime = paramEndTime.concat(" 23:59:59");
//            startDate = URLEncoder.encode(beginTime, "UTF-8");
//            endDate = URLEncoder.encode(endTime, "UTF-8");
//        }
//        Map<String, Object> headers = Utils.getHeader();
//        String url = dolphinscheduler + Constants.TASK_INSTANCES.replace("{projectCode}", projectId) +
//                "/list?processInstanceId=" + StringUtils.defaultIfBlank(processInstanceId.toString(), "")
//                + "&processInstanceName=" + StringUtils.defaultIfBlank(processInstanceName, "")
//                + "&processDefinitionName=" + StringUtils.defaultIfBlank(processDefinitionName, "")
//                + "&searchVal=" + StringUtils.defaultIfBlank(keywords, "")
//                + "&taskName=" + StringUtils.defaultIfBlank(taskName, "")
//                + "&executorName=" + StringUtils.defaultIfBlank(executorName, "")
//                + "&stateType=" + StringUtils.defaultIfBlank(stateType, "")
//                + "&host=" + StringUtils.defaultIfBlank(host, "")
//                + "&startDate=" + StringUtils.defaultIfBlank(startDate, "")
//                + "&endDate=" + StringUtils.defaultIfBlank(endDate, "")
//                + "&taskExecuteType=" + StringUtils.defaultIfBlank(taskExecuteType, "")
//                + "&pageNo=" + ++page
//                + "&pageSize=" + pager;
//        Map resultMap = httpRequestFeignService.get4url(url, headers, Map.class);
//        //返回成框架格式
//        if (Integer.parseInt(String.valueOf(resultMap.get("code"))) != 0) {
//            return ResponseFactory.makeError(resultMap.get("msg").toString());
//        }
//        JSONObject jsonObject = JsonUtil.toJSON(resultMap.get("data"));
//        return ResponseFactory.makeSuccess(jsonObject.getJSONArray("totalList"), page-1, pager, jsonObject.getInteger("total"));
//    }

    @Log("强制成功")
    @Auth
    @GetMapping("/taskInstances/{id}/force-success")
    public Response forceTaskSuccess(@PathVariable String id, @RequestParam(value = "projectId") String projectId) {
//        Map<String, Object> headers = Utils.getHeader();
        TaskInstance taskInstance = taskInstanceService.getQuery().eq("id", id).filters("state", "processInstanceId", "taskId").one();
//        String url = dolphinscheduler + Constants.TASK_INSTANCES.replace("{projectCode}", projectId) + "/" + id + "/force-success";
//        Map resultMap = Utils.webClientPost(url, new HashMap<>(), headers.get(Constants.SESSION_ID).toString());
//        Response response = Utils.responseInfo(resultMap);
        //20250707 强制成功直接修改库，不再调用海豚接口
        taskInstanceService.forceTaskSuccess(id, taskInstance, projectId);
        return ResponseFactory.makeSuccess("success");
    }

    @Override
    public Response<List<?>> getList(HttpServletRequest request, List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        //页面传这个值，代表是资源管理的任务监控列表查询的，需要查询taskMonitor表
        if (null != request.getParameter("isMonitor")
                && "true".equals(request.getParameter("isMonitor"))) {
            Response<List<?>> response = taskInstanceService.getTaskInstanceList(request, page, pager);
            List<TaskInstance> result = (List<TaskInstance>) response.getResult();
            if (null != result && result.size() > 0) {
                result.forEach(t -> {
                    //将任务名称的目录id后缀去掉
                    String name = t.getName();
                    if (name.contains("__[") && name.contains("_copy")) {
                        //如果是复制的工作流，要截取中间的一节
                        t.setName(Utils.removeBetween(name, "__", "_copy"));
                    } else if (name.contains("__[")) {
                        t.setName(name.substring(0, name.lastIndexOf("__[")));
                    }
                });
                taskInstanceService.setTaskMonitor(result);
            }
            return ResponseFactory.makeSuccess(result, page, pager, response.getTotal());
        }
        Response<List<?>> response = super.getList(request, conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        List<TaskInstance> result = (List<TaskInstance>) response.getResult();
        if (null != result && result.size() > 0) {
            //获取用户信息
            List<String> executorIds = result.stream().map(TaskInstance::getExecutorId).distinct().collect(Collectors.toList());
            List<User> users = userJoyaFeignService.getQuery().in("id", executorIds).list();
            Map<String, User> userMap = users.stream().collect(Collectors.toMap(User::getId, v -> v));
            result.forEach(t -> {
                //将任务名称的目录id后缀去掉
                String name = t.getName();
                if (name.contains("__[") && name.contains("_copy")) {
                    //如果是复制的工作流，要截取中间的一节
                    t.setName(Utils.removeBetween(name, "__", "_copy"));
                } else if (name.contains("__[")) {
                    t.setName(name.substring(0, name.lastIndexOf("__[")));
                }
                //赋值执行人
                User user = userMap.get(t.getExecutorId());
                if (user != null) {
                    t.setExecutorName(user.getUsername());
                }
            });
        }
        return ResponseFactory.makeSuccess(result, page, pager, response.getTotal());
    }

    @Log("cpu趋势图")
    @Auth
    @GetMapping("/taskInstances/{taskInstanceId}/cpuTendencyChart")
    public Response cpuTendencyChart(@PathVariable String taskInstanceId, @RequestParam String interval,
                                     @RequestParam Integer page, @RequestParam Integer pager) {
        return taskInstanceService.cpuTendencyChart(taskInstanceId, interval, page, pager);
    }

    @Log("内存趋势图")
    @Auth
    @GetMapping("/taskInstances/{taskInstanceId}/memTendencyChart")
    public Response<?> memTendencyChart(@PathVariable String taskInstanceId, @RequestParam String interval,
                                        @RequestParam Integer page, @RequestParam Integer pager) {
        return taskInstanceService.memTendencyChart(taskInstanceId, interval, page, pager);
    }

    @Auth
    @GetMapping({"/taskInstances/processInstanceId/{processDefinitionId}"})
    public Response<List<TaskInstance>> getList(@SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList,
                                                @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList,
                                                @GroupBy GroupByCondition groupByCondition,
                                                @com.joyadata.annotation.request.QueryFilter com.joyadata.model.sql.QueryFilter queryFilter,
                                                @RequestParam Integer page,
                                                @RequestParam Integer pager,
                                                @PathVariable String processDefinitionId,
                                                @RequestParam(required = false) String frame,
                                                @RequestParam(required = false) String keywords,
                                                @RequestParam(required = false) String productId,
                                                @RequestParam(required = false) Integer state) {
        String[] sortby = queryFilter.getSortby();
        //根据字段排序，默认正序
        String sortbyField = "startTime";
        String sortbyType = "asc";
        if (null != sortby) {
            HashSet<String> hashSet = new HashSet(Arrays.asList(sortby));
            // 定义排序字段和排序类型的映射关系
            Map<String, String[]> sortMap = new HashMap<>();
            sortMap.put("submitTime", new String[]{"submitTime_asc", "submitTime_desc"});
            sortMap.put("startTime", new String[]{"startTime_asc", "startTime_desc"});
            sortMap.put("endTime", new String[]{"endTime_asc", "endTime_desc"});
            sortMap.put("duration", new String[]{"duration_asc", "duration_desc"});

            // 遍历映射关系，设置排序字段和排序类型
            for (Map.Entry<String, String[]> entry : sortMap.entrySet()) {
                String field = entry.getKey();
                String[] sortOptions = entry.getValue();

                if (hashSet.contains(sortOptions[0])) {
                    sortbyField = field;
                    sortbyType = "asc";
                    hashSet.remove(sortOptions[0]);
                } else if (hashSet.contains(sortOptions[1])) {
                    sortbyField = field;
                    sortbyType = "desc";
                    hashSet.remove(sortOptions[1]);
                }
            }
            //将剩余的排序条件设置到 queryFilter 中
            queryFilter.setSortby(hashSet.toArray(new String[]{}));
        }
        List<TaskInstance> taskInstances = taskInstanceService.getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter).list();
        List<TaskInstance> result;
        //补全未运行的任务实例
        if (StringUtils.isBlank(frame)) {
            //不根据提交时间筛选时，补充未运行
            result = taskInstanceService.initNotRunningTaskInstance(processDefinitionId, taskInstances, keywords, productId);
        } else {
            result = taskInstances;
        }
        //根据运行状态筛选
        if (null != state) {
            result = result.stream().filter(t -> t.getState().equals(TaskExecutionStatus.of(state).name())).collect(Collectors.toList());
        }
        //分页
        List<TaskInstance> pageData = TaskInstanceService.getPageData(result, page, pager);
        //根据运行时长排序
        if (null != pageData && !pageData.isEmpty()) {
            TaskInstanceService.sortByDuration(pageData, sortbyField, sortbyType);
        }
        return ResponseFactory.makeSuccess(pageData, page, pager, null == result ? 0 : result.size());
    }

    /**
     * 通过工作流实例获取最新的任务实例
     */
    @Auth
    @GetMapping("/taskInstances/{processInstanceId}/endExec")
    public Response<List<TaskInstance>> getEndTaskInstances(@PathVariable String processInstanceId) {
        List<TaskInstance> result = taskInstanceService.getEndTaskInstances(processInstanceId);
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 通过任务实例ID获取最新的任务实例状态
     */
    @Auth
    @GetMapping("/taskInstance/{taskInstanceId}/state")
    public Response<String> getTaskInstanceState(@PathVariable String taskInstanceId) {
        String sql = "SELECT state from business_ds.t_ds_task_instance where id=%s";
        Integer state = taskInstanceService.getSqlExecutor().excuteSelect(String.format(sql, taskInstanceId)).oneValue("state", Integer.class);
        String result = StringUtils.EMPTY;
        if (null != state) {
            result = TaskExecutionStatus.of(state).name();
        }
        return ResponseFactory.makeSuccess(Response.Msg.Success, result);
    }

    /**
     * 任务实例搜索后 统计
     */
    @Auth
    @GetMapping({"/taskInstance/statistics"})
    public Response<JSONObject> getList(@SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList, @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList, @GroupBy GroupByCondition groupByCondition, @com.joyadata.annotation.request.QueryFilter com.joyadata.model.sql.QueryFilter queryFilter,
                                        @RequestParam(required = false) String state) throws Exception {
        JSONObject result = new JSONObject();
        IQueryWrapper<TaskInstance> totalQueryWrapper = taskInstanceService.getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);
        result.put("total", totalQueryWrapper.total());
        //拷贝一个新的，不影响原来的条件获取总数
        List<ConditionGroup> newConditionGroupList = getNewConfitionGroupList(conditionGroupList);
        IQueryWrapper<TaskInstance> runningQueryWrapper = taskInstanceService.getQuery().andConditionGroupList(newConditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);
        IQueryWrapper<TaskInstance> successQueryWrapper = taskInstanceService.getQuery().andConditionGroupList(newConditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);
        IQueryWrapper<TaskInstance> failureQueryWrapper = taskInstanceService.getQuery().andConditionGroupList(newConditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter);

        long runningCount = 0;
        long successCount = 0;
        long failureCount = 0;
        if (StringUtils.isNotBlank(state)) {
            //state=in(2,3) 只需要in条件中的状态
            List<Integer> stateList = getStateList(state);
            if (stateList.contains(TaskExecutionStatus.RUNNING_EXECUTION.getCode())) {
                cleanStateCondition(newConditionGroupList);
                runningCount = runningQueryWrapper.eq("state", TaskExecutionStatus.RUNNING_EXECUTION.getCode()).total();
            }
            if (stateList.contains(TaskExecutionStatus.SUCCESS.getCode())) {
                cleanStateCondition(newConditionGroupList);
                successCount = successQueryWrapper.eq("state", TaskExecutionStatus.SUCCESS.getCode()).total();
            }
            if (stateList.contains(TaskExecutionStatus.FAILURE.getCode())) {
                cleanStateCondition(newConditionGroupList);
                failureCount = failureQueryWrapper.eq("state", TaskExecutionStatus.FAILURE.getCode()).total();
            }
        } else {
            //计算运行中数量
            cleanStateCondition(newConditionGroupList);
            runningCount = runningQueryWrapper.eq("state", TaskExecutionStatus.RUNNING_EXECUTION.getCode()).total();
            cleanStateCondition(newConditionGroupList);
            successCount = successQueryWrapper.eq("state", TaskExecutionStatus.SUCCESS.getCode()).total();
            cleanStateCondition(newConditionGroupList);
            failureCount = failureQueryWrapper.eq("state", TaskExecutionStatus.FAILURE.getCode()).total();
        }

        result.put("runningCount", runningCount);
        result.put("successCount", successCount);
        result.put("failureCount", failureCount);

        return ResponseFactory.makeSuccess(result);
    }

    private List<ConditionGroup> getNewConfitionGroupList(List<ConditionGroup> conditionGroupList) {
        List<ConditionGroup> newList = new ArrayList<>();
        for (ConditionGroup group : conditionGroupList) {
            newList.add(new ConditionGroup(group.getEcs(), group.getIcs(), group.getConditions()));
        }
        return newList;
    }

    private List<Integer> getStateList(String state) {
        // 1. 提取括号内的内容：`2,3`
        String numbersStr = state.replaceAll(".*\\((.*)\\).*", "$1");

        // 2. 按逗号分割成字符串数组 `["2", "3"]`
        String[] numbersStrArray = numbersStr.split(",");

        // 3. 转换为 int 集合 `[2, 3]`
        List<Integer> stateList = new ArrayList<>();
        for (int i = 0; i < numbersStrArray.length; i++) {
            stateList.add(Integer.parseInt(numbersStrArray[i]));
        }
        return stateList;
    }

    private void cleanStateCondition(List<ConditionGroup> conditionGroupList) {
        if (null != conditionGroupList && conditionGroupList.size() > 0) {
            for (ConditionGroup conditionGroup : conditionGroupList) {
                List<WhereCondition> conditions = conditionGroup.getConditions();
                if (null != conditions && !conditions.isEmpty()) {
                    for (int i = 0; i < conditions.size(); i++) {
                        WhereCondition condition = conditions.get(i);
                        String filed = condition.getFiled();
                        if (StringUtils.isNotBlank(filed) && "state".equals(filed)) {
                            conditions.remove(i);
                        }
                    }
                }
            }
        }
    }

    @GetMapping("/taskInstances/initAcDate")
    public Response<?> initAcDate() {
        String sql = "UPDATE `business_scc`.`scc_task_instance`" +
                "SET ac_date = (" +
                "    SELECT JSON_UNQUOTE(item->'$.value')" +
                "    FROM JSON_TABLE(" +
                "        global_params," +
                "        '$[*]' COLUMNS(" +
                "            item JSON PATH '$'" +
                "        )" +
                "    ) AS jt" +
                "    WHERE JSON_UNQUOTE(item->'$.prop') = 'ac_date'" +
                ")" +
                "WHERE JSON_CONTAINS(global_params->'$[*].prop', '\"ac_date\"', '$')";
        Integer update = getService().getSqlExecutor().excuteUpdate(sql);
        return ResponseFactory.makeSuccess(Response.Msg.Success, update);
    }

    /**
     * 统计任务总数和任务实例状态
     * 任务总数是已上线的工作流下的所有子任务
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Log("浙江农信需求，统计任务总数和待运行数")
    @Auth
    @GetMapping("/getTaskCountAndState")
    public Response<?> getTaskCountAndState(@RequestParam(value = "startTime", required = false, defaultValue = "1970-01-01 00:00:00") String startTime,
                                            @RequestParam(value = "endTime", required = false, defaultValue = "2970-01-01 00:00:00") String endTime,
                                            @RequestParam(value = "projectIdCondition") String projectIdCondition) {
        return taskInstanceService.getTaskCountAndState(startTime, endTime, projectIdCondition);
    }
}