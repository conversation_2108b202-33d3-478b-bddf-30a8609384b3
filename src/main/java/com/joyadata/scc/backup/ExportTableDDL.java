package com.joyadata.scc.backup;

import java.io.*;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

public class ExportTableDDL {
    // 数据库连接配置
    private static final String JDBC_URL = "********************************/";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "Cdyanfa_123456";
    private static final String BACKUP_PATH = "G://db//backup_table_view//20250819";
    // 数据库和对应要排除的表配置
    private static final Map<String, DatabaseConfig> DB_CONFIGS = new HashMap<>();

    static {
        // 初始化数据库配置
        DB_CONFIGS.put("business_scc", new DatabaseConfig(
                Arrays.asList("temp_.*", "test_table1", "log_.*"),  // 排除的表
                Arrays.asList("test_view.*")  // 排除的视图
        ));
        /*DB_CONFIGS.put("business_dah", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));*/
        DB_CONFIGS.put("business_damp", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_design", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_dms", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_dr", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_ds", new DatabaseConfig(
                Arrays.asList("qrtz_*"),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_fapi", new DatabaseConfig(
                Arrays.asList("test_0830_554"),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_fis", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_integration", new DatabaseConfig(
                Arrays.asList("di_process_definition_copy", "plugin_origin_0924",
                        "task_catalogue_copy1", "plugin_field_type_bak"),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("business_scc", new DatabaseConfig(
                Arrays.asList("scc_catalog_copy1", "scc_process_definition_copy1", "scc_process_definition_log_bak",
                        "scc_process_task_relation_copy1", "scc_process_task_relation_log_bak", "scc_task_log_bak",
                        "scc_task_type_copy1"),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("joyadata", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));
        DB_CONFIGS.put("nacos", new DatabaseConfig(
                Arrays.asList(""),  // 排除的表
                Arrays.asList("")  // 排除的视图
        ));


    }

    public static void main(String[] args) {
        try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
            for (String dbName : DB_CONFIGS.keySet()) {
                exportDatabase(conn, dbName);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void exportDatabase(Connection conn, String dbName) {
        try {
            // 创建输出目录
            File dir = new File(BACKUP_PATH);
            if (!dir.exists()) {
                if (!dir.mkdirs()) {
                    throw new IOException("无法创建目录: " + BACKUP_PATH);
                }
            }

            // 导出表结构
            String tableFileName = BACKUP_PATH + File.separator + dbName + "_ddl.sql";
            try (PrintWriter writer = new PrintWriter(new FileWriter(tableFileName))) {
                writer.println("-- Database: " + dbName);
                writer.println("-- Export time: " + new java.util.Date());
                writer.println("-- Content: Tables DDL");
                writer.println();

                exportTables(conn, dbName, writer);
                System.out.println("Successfully exported tables DDL for database: " + dbName);
            }

            // 获取视图列表
            List<String> views = getViews(conn, dbName);
            DatabaseConfig dbConfig = DB_CONFIGS.get(dbName);

            // 过滤掉需要排除的视图
            if (dbConfig != null) {
                views = views.stream()
                        .filter(view -> !dbConfig.shouldExcludeView(view))
                        .collect(Collectors.toList());
            }

            // 只有在存在视图时才创建视图文件
            if (!views.isEmpty()) {
                String viewFileName = BACKUP_PATH + File.separator + dbName + "_view.sql";
                try (PrintWriter writer = new PrintWriter(new FileWriter(viewFileName))) {
                    writer.println("-- Database: " + dbName);
                    writer.println("-- Export time: " + new java.util.Date());
                    writer.println("-- Content: Views DDL");
                    writer.println();

                    exportViews(conn, dbName, writer);
                    System.out.println("Successfully exported views DDL for database: " + dbName);
                }
            } else {
                System.out.println("No views found in database: " + dbName);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void exportTables(Connection conn, String dbName, PrintWriter writer) throws SQLException {
        List<String> tables = getTables(conn, dbName);
        DatabaseConfig dbConfig = DB_CONFIGS.get(dbName);

        for (String tableName : tables) {
            if (dbConfig != null && dbConfig.shouldExcludeTable(tableName)) {
                System.out.println("Skipping table: " + tableName + " in database: " + dbName);
                continue;
            }

            String createTable = getTableDDL(conn, dbName, tableName);
            if (createTable != null) {
                writer.println("-- Table structure for " + tableName);
                writer.println(createTable);
                writer.println();
            }
        }
    }

    private static void exportViews(Connection conn, String dbName, PrintWriter writer) throws SQLException {
        List<String> views = getViews(conn, dbName);
        DatabaseConfig dbConfig = DB_CONFIGS.get(dbName);

        for (String viewName : views) {
            if (dbConfig != null && dbConfig.shouldExcludeView(viewName)) {
                System.out.println("Skipping view: " + viewName + " in database: " + dbName);
                continue;
            }

            String createView = getViewDDL(conn, dbName, viewName);
            if (createView != null) {
                writer.println("-- View structure for " + viewName);
                writer.println(createView);
                writer.println();
            }
        }
    }

    private static List<String> getTables(Connection conn, String dbName) throws SQLException {
        List<String> tables = new ArrayList<>();
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(dbName, null, "%", new String[]{"TABLE"});

        while (rs.next()) {
            tables.add(rs.getString("TABLE_NAME"));
        }

        return tables;
    }

    private static List<String> getViews(Connection conn, String dbName) throws SQLException {
        List<String> views = new ArrayList<>();
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(dbName, null, "%", new String[]{"VIEW"});

        while (rs.next()) {
            views.add(rs.getString("TABLE_NAME"));
        }

        return views;
    }

    private static String getTableDDL(Connection conn, String dbName, String tableName) {
        try {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("USE " + dbName);
            }

            try (Statement stmt = conn.createStatement()) {
                ResultSet rs = stmt.executeQuery("SHOW CREATE TABLE " + tableName);
                if (rs.next()) {
                    return rs.getString(2) + ";";
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting DDL for table " + tableName + ": " + e.getMessage());
        }
        return null;
    }

    private static String getViewDDL(Connection conn, String dbName, String viewName) {
        try {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("USE " + dbName);
            }

            try (Statement stmt = conn.createStatement()) {
                ResultSet rs = stmt.executeQuery("SHOW CREATE VIEW " + viewName);
                if (rs.next()) {
                    String viewDDL = rs.getString(2);
                    // 移除 DEFINER 子句
                    viewDDL = viewDDL.replaceAll("(?i)DEFINER=`[^`]+`@`[^`]+` ", "");
                    return viewDDL + ";";
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting DDL for view " + viewName + ": " + e.getMessage());
        }
        return null;
    }
}

class DatabaseConfig {
    private final List<String> excludeTablePatterns;
    private final List<String> excludeViewPatterns;

    public DatabaseConfig(List<String> excludeTablePatterns, List<String> excludeViewPatterns) {
        this.excludeTablePatterns = excludeTablePatterns;
        this.excludeViewPatterns = excludeViewPatterns;
    }

    public boolean shouldExcludeTable(String tableName) {
        return matchesPattern(tableName, excludeTablePatterns);
    }

    public boolean shouldExcludeView(String viewName) {
        return matchesPattern(viewName, excludeViewPatterns);
    }

    private boolean matchesPattern(String name, List<String> patterns) {
        return patterns.stream()
                .anyMatch(pattern -> name.matches(pattern));
    }
}