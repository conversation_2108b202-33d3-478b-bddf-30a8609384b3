package com.joyadata.scc.backup;

import java.io.File;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DatabaseBackup {
    // 数据库连接配置
    private static final String JDBC_URL = "********************************/";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "Cdyanfa_123456";

    // 备份文件保存路径
    private static final String BACKUP_PATH = "G://db//backup//20250819//";

    // 数据库及其对应的查询语句配置
    private static final Map<String, List<QueryConfig>> DATABASE_QUERIES = new HashMap<>();

    static {
        // 共享交换
       /* DATABASE_QUERIES.put("business_damp", Arrays.asList(
                new QueryConfig("asset_alarm_relation", "select * from  asset_alarm_relation where tenant_code=-1;"),
                new QueryConfig("asset_alarm_strategy", "select * from asset_alarm_strategy where tenant_code=-1;"),
                new QueryConfig("asset_datasource_extractor", "select * from asset_datasource_extractor where tenant_code=-1;"),
                new QueryConfig("asset_meta_data_group", "select  * from asset_meta_data_group where tenant_code=-1;"),
                new QueryConfig("asset_meta_data_type", "select  * from asset_meta_data_type where tenant_code=-1;"),
                new QueryConfig("asset_meta_data_config", "select  * from asset_meta_data_config where tenant_code=-1;"),
                new QueryConfig("asset_quartz_job", "select  * from asset_quartz_job where tenant_code=-1;"),
                new QueryConfig("asset_sys_param", "select  * from asset_sys_param where tenant_code=-1;"),
                new QueryConfig("asset_dbps_city_code", "select  * from asset_dbps_city_code where tenant_code=-1;"),
                new QueryConfig("xxl_job_info", "select  * from xxl_job_info where id in(-1,1,2);")
        ));*/
        // 容灾
        /*DATABASE_QUERIES.put("business_dr", Arrays.asList(
                new QueryConfig("dr_script_data", "SELECT * FROM dr_script_data WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_script_variable", "SELECT * FROM dr_script_variable WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_action_step", "SELECT * FROM dr_action_step WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_process_step", "SELECT * FROM dr_process_step WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_scene_template", "SELECT * FROM dr_scene_template WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_sync_template", "SELECT * FROM dr_sync_template WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_process_definition", "SELECT * FROM dr_process_definition WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_task", "SELECT * FROM dr_task WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_process_task_relation", "SELECT * FROM dr_process_task_relation WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';"),
                new QueryConfig("dr_catalog", "SELECT * FROM dr_catalog WHERE tenant_code ='-1' AND create_by='-1' AND built_in ='1';")
        ));*/
        // 海豚
       /* DATABASE_QUERIES.put("business_ds", Arrays.asList(
                new QueryConfig("t_ds_queue", "select  * from t_ds_queue;")
        ));*/
        // 集成
        DATABASE_QUERIES.put("business_integration", Arrays.asList(
                new QueryConfig("function_classify", "select * from function_classify;"),
                new QueryConfig("function_origin", "select * from function_origin;"),
                new QueryConfig("plugin_classify", "select * from plugin_classify;"),
                new QueryConfig("plugin_engine_field_type", "select * from plugin_engine_field_type;"),
                new QueryConfig("plugin_field_type", "select * from plugin_field_type;"),
                new QueryConfig("plugin_filter_functional_info", "select * from plugin_filter_functional_info;"),
                new QueryConfig("plugin_origin", "select * from plugin_origin;")
        ));
        /*DATABASE_QUERIES.put("business_integration", Arrays.asList(
                new QueryConfig("plugin_origin", "select * from plugin_origin;")
        ));*/
        // joyadata
       /* DATABASE_QUERIES.put("joyadata", Arrays.asList(
                new QueryConfig("cms_menu", "select * from cms_menu where tenant_code=-1;"),
                new QueryConfig("cms_dir", "select * from cms_dir where tenant_code=-1;"),
                new QueryConfig("cms_role", "select * from cms_role where tenant_code=-1;"),
                new QueryConfig("cms_role_user_mapping", "select * from cms_role_user_mapping where tenant_code=-1;"),
                new QueryConfig("cms_role_menu_mapping", "select * from cms_role_menu_mapping where tenant_code=-1;"),
                new QueryConfig("cms_role_dir_mapping", "select * from cms_role_dir_mapping where tenant_code=-1;"),
                new QueryConfig("cms_threshold", "select * from cms_threshold WHERE tenant_code=-1;"),
                new QueryConfig("cms_sysdict", "select * from cms_sysdict WHERE tenant_code=-1;"),
                new QueryConfig("tms_product_approval", "select * from tms_product_approval WHERE tenant_code=-1;"),
                new QueryConfig("tms_product_approval_category", "select * from tms_product_approval_category WHERE tenant_code=-1;"),
                new QueryConfig("tms_product_approval_category_variable", "select * from tms_product_approval_category_variable WHERE tenant_code=-1;"),
                new QueryConfig("tms_product_approval_model", "select * from tms_product_approval_model where tenant_code=-1;"),
                new QueryConfig("tms_product_approval_node", "select * from tms_product_approval_node where tenant_code=-1;"),
                new QueryConfig("tms_product_approval_node_variable", "select * from tms_product_approval_node_variable where tenant_code=-1;"),
                new QueryConfig("tms_product_operation", "select * from tms_product_operation where tenant_code=-1;"),
                new QueryConfig("tms_product_role", "select * from tms_product_role where tenant_code=-1;"),
                new QueryConfig("tms_product_role_operation_mapping", "select * from tms_product_role_operation_mapping where tenant_code=-1;"),
                new QueryConfig("tms_product_role_product_dir_mapping", "select * from tms_product_role_product_dir_mapping where tenant_code=-1;"),
                new QueryConfig("tms_product_role_product_menu_mapping", "select * from tms_product_role_product_menu_mapping where tenant_code=-1;"),
                new QueryConfig("tms_product_sysdict", "select * from tms_product_sysdict where tenant_code=-1;"),
                new QueryConfig("tms_product_threshold", "select * from tms_product_threshold where tenant_code=-1;"),
                new QueryConfig("tms_product_event", "select * from tms_product_event where tenant_code=-1;"),
                new QueryConfig("tms_product_event_attr", "select * from tms_product_event_attr where tenant_code=-1;"),
                new QueryConfig("cms_sys_param", "SELECT * FROM cms_sys_param WHERE tenant_code=-1;")
        ));*/
        // nacos
       /* DATABASE_QUERIES.put("nacos", Arrays.asList(
                new QueryConfig("roles", "select * from roles;"),
                new QueryConfig("tenant_info", "select * from tenant_info;"),
                new QueryConfig("users", "select * from users;")
        ));*/

    }

    public static void main(String[] args) {
        // 确保备份目录存在
        createBackupDirectory();

        // 开始备份
        for (String dbName : DATABASE_QUERIES.keySet()) {
            backupDatabase(dbName);
        }
    }

    private static void createBackupDirectory() {
        File backupDir = new File(BACKUP_PATH);
        if (!backupDir.exists()) {
            boolean created = backupDir.mkdirs();
            if (!created) {
                throw new RuntimeException("无法创建备份目录: " + BACKUP_PATH);
            }
        }
    }

    private static void backupDatabase(String dbName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String fileName = BACKUP_PATH + "backup_" + dbName + "_" + timestamp + ".sql";

        try (Connection conn = DriverManager.getConnection(JDBC_URL + dbName, USERNAME, PASSWORD);
             PrintWriter writer = new PrintWriter(new FileWriter(fileName))) {

            // 写入文件头部信息
          /*  writer.println("-- 数据库备份: " + dbName);
            writer.println("-- 备份时间: " + LocalDateTime.now());
            writer.println("-- -------------------------------------");*/

            List<QueryConfig> queries = DATABASE_QUERIES.get(dbName);
            for (QueryConfig queryConfig : queries) {
                generateInsertStatements(dbName, conn, queryConfig, writer);
            }

            System.out.println("数据库 " + dbName + " 备份完成，文件名: " + fileName);

        } catch (Exception e) {
            System.err.println("备份数据库 " + dbName + " 时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void generateInsertStatements(String dbName, Connection conn, QueryConfig queryConfig, PrintWriter writer)
            throws SQLException {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(queryConfig.getQuery())) {

            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();

            /*writer.println("\n-- " + queryConfig.getTableName() + " 表的数据");
            writer.println("-- 查询语句: " + queryConfig.getQuery());*/

            // 构建字段名称列表
            StringBuilder columnNames = new StringBuilder();
            for (int i = 1; i <= columnCount; i++) {
                if (i > 1) {
                    columnNames.append(", ");
                }
                columnNames.append("`").append(rsmd.getColumnName(i)).append("`");
            }

            int rowCount = 0;
            while (rs.next()) {
                StringBuilder insertSql = new StringBuilder()
                        .append("INSERT INTO `")
                        .append(escapeName(dbName))     // 添加数据库名称
                        .append("`.`")
                        .append(escapeName(queryConfig.getTableName()))
                        .append("` (")
                        .append(columnNames.toString())
                        .append(") VALUES (");

                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) {
                        insertSql.append(", ");
                    }
                    insertSql.append(getColumnValue(rs, i, rsmd.getColumnType(i)));
                }

                insertSql.append(");");
                writer.println(insertSql.toString());
                rowCount++;
            }

            writer.println("\n");
        }
    }

    private static String escapeName(String name) {
        return name.replace("`", "``");
    }


    private static String getColumnValue(ResultSet rs, int columnIndex, int sqlType) throws SQLException {
        // 首先检查是否为NULL
        if (rs.getObject(columnIndex) == null) {
            return "NULL";
        }

        switch (sqlType) {
            // 字符串类型
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.LONGVARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
                return escapeString(rs.getString(columnIndex));

            // 数值类型
            case Types.TINYINT:
            case Types.SMALLINT:
                return String.valueOf(rs.getShort(columnIndex));
            case Types.INTEGER:
                return String.valueOf(rs.getInt(columnIndex));
            case Types.BIGINT:
                return String.valueOf(rs.getLong(columnIndex));
            case Types.REAL:
            case Types.FLOAT:
                return String.valueOf(rs.getFloat(columnIndex));
            case Types.DOUBLE:
                return String.valueOf(rs.getDouble(columnIndex));
            case Types.DECIMAL:
            case Types.NUMERIC:
                return rs.getBigDecimal(columnIndex).toString();

            // 日期时间类型
            case Types.DATE:
                return "'" + rs.getDate(columnIndex).toString() + "'";
            case Types.TIME:
                return "'" + rs.getTime(columnIndex).toString() + "'";
            case Types.TIMESTAMP:
                java.sql.Timestamp timestamp = rs.getTimestamp(columnIndex);
                return "'" + timestamp.toString().replace('T', ' ').split("\\.")[0] + "'";

            // 布尔类型
            case Types.BOOLEAN:
            case Types.BIT:
                return rs.getBoolean(columnIndex) ? "1" : "0";

            // 二进制类型
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
            case Types.BLOB:
                byte[] bytes = rs.getBytes(columnIndex);
                return formatBinaryData(bytes);

            // CLOB类型
            case Types.CLOB:
            case Types.NCLOB:
                Clob clob = rs.getClob(columnIndex);
                return escapeString(clob.getSubString(1, (int) clob.length()));

            // 其他类型
            default:
                // 对于未知类型，使用getString作为后备方案
                return escapeString(rs.getString(columnIndex));
        }
    }

    private static String formatTimestamp(java.sql.Timestamp timestamp) {
        return timestamp.toString().replace('T', ' ').split("\\.")[0];
    }

    private static String formatLocalDateTime(java.time.LocalDateTime dateTime) {
        return dateTime.toString().replace('T', ' ').split("\\.")[0];
    }

    private static String escapeString(String str) {
        if (str == null) {
            return "NULL";
        }

        StringBuilder sb = new StringBuilder();
        sb.append('\'');
        for (char c : str.toCharArray()) {
            switch (c) {
                case '\'':
                    sb.append("\\'");
                    break;
                case '\"':
                    sb.append("\\\"");
                    break;
                case '\\':
                    sb.append("\\\\");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                default:
                    sb.append(c);
            }
        }
        sb.append('\'');
        return sb.toString();
    }

    private static String formatBinaryData(byte[] bytes) {
        if (bytes == null) {
            return "NULL";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("X'");
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        sb.append("'");
        return sb.toString();
    }

    private static class QueryConfig {
        private final String tableName;
        private final String query;

        public QueryConfig(String tableName, String query) {
            this.tableName = tableName;
            this.query = query;
        }

        public String getTableName() {
            return tableName;
        }

        public String getQuery() {
            return query;
        }
    }
}