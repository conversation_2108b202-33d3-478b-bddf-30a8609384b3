package com.joyadata.scc.mapper;

import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.TempDatasourceBusiness;
import com.joyadata.scc.model.TempDatasourceInfo;
import com.joyadata.scc.model.TempDatasourceInfoAuthorization;
import com.joyadata.scc.model.TempDatasourceInfoAuthorizationOperation;
import com.joyadata.scc.model.TempDatasourceReference;
import com.joyadata.scc.model.TempDiProcessDefinition;
import com.joyadata.scc.model.TempDiProcessTaskRelation;
import com.joyadata.scc.model.TempDiScheduler;
import com.joyadata.scc.model.TempImport;
import com.joyadata.scc.model.TempTaskCatalogue;
import com.joyadata.scc.model.TempTaskConfigInfo;
import com.joyadata.scc.model.TempTaskConfigLineInfo;
import com.joyadata.scc.model.TempTaskDefinitionInfo;
import com.joyadata.scc.model.TempTaskVersionInfo;
import com.joyadata.scc.model.imports.databases.DatasourceBusinessDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationOperationDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoDTO;
import com.joyadata.scc.model.imports.databases.DatasourceReferenceDTO;
import com.joyadata.scc.model.imports.di.DiProcessDefinitionDTO;
import com.joyadata.scc.model.imports.di.DiProcessTaskRelationDTO;
import com.joyadata.scc.model.imports.di.DiSchedulerDTO;
import com.joyadata.scc.model.imports.integration.TaskCatalogueDTO;
import com.joyadata.scc.model.imports.integration.TaskConfigInfoDTO;
import com.joyadata.scc.model.imports.integration.TaskConfigLineInfoDTO;
import com.joyadata.scc.model.imports.integration.TaskDefinitionInfoDTO;
import com.joyadata.scc.model.imports.integration.TaskVersionInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/16 18:50
 */
@Mapper
public interface ExportTaskMapper {
    List<DatasourceInfoDTO> queryDatasourceInfoById(@Param("datasourceIds") List<String> datasourceIds);

    List<DatasourceInfoAuthorizationDTO> queryDatasourceInfoAuthorizationByDatasourceIds(@Param("productId") String productId,
                                                                                         @Param("projectId") String projectId,
                                                                                         @Param("datasourceIds") List<String> datasourceIds);

    List<DatasourceInfoAuthorizationOperationDTO> queryDatasourceInfoAuthorizationOperationByDatasourceIds(@Param("productId") String productId,
                                                                                                           @Param("projectId") String projectId,
                                                                                                           @Param("datasourceIds") List<String> datasourceIds);

    List<DatasourceReferenceDTO> queryDatasourceReferenceByDatasourceIds(@Param("productId") String productId,
                                                                         @Param("projectId") String projectId,
                                                                         @Param("datasourceIds") List<String> datasourceIds);

    List<DatasourceBusinessDTO> queryDatasourceBusinessByUuid(@Param("uuids") List<String> uuids);

    List<TaskDefinitionInfoDTO> queryTaskDefinitionInfoByTaskId(@Param("taskIds") List<String> taskIds);

    List<TaskCatalogueDTO> queryTaskCatalogueByTaskId(@Param("taskIds") List<String> taskIds);

    List<TaskCatalogueDTO> queryTaskCatalogueByCatalogIds(@Param("catalogs") List<String> catalogs);

    List<TaskVersionInfoDTO> queryTaskVersionInfoByVersionIds(@Param("versionIds") List<String> versionIds);

    List<TaskConfigLineInfoDTO> queryTaskConfigLineInfoByVersionIds(@Param("versionIds") List<String> versionIds);

    List<TaskConfigInfoDTO> queryTaskConfigInfoByVersionIds(@Param("versionIds") List<String> versionIds);

    /**
     * 根据项目id、租户code，数据源名称查询数据源是否存在
     *
     * @param dataName   数据源名称
     * @param tenantCode 租户code
     * @return
     */
    DatasourceInfoDTO queryDatasourceInfoByName(@Param("dataName") String dataName, @Param("tenantCode") String tenantCode);

    DatasourceBusinessDTO queryDatasourceBusinessByName(@Param("businessName") String businessName, @Param("tenantCode") String tenantCode);

    String queryTableExist(@Param("datasourceId") String datasourceId, @Param("tableName") String tableName);

    List<TempTaskCatalogue> queryTaskCatalogueByName(@Param("name") String name, @Param("tenantCode") String tenantCode, @Param("projectId") String projectId, @Param("parentId") String parentId);

    void insertDatasoruceBusiness(@Param("notExistDatasourceBusinessLists") List<TempDatasourceBusiness> notExistDatasourceBusinessLists);

    void insertDatasoruces(@Param("notExistDatasourceLists") List<TempDatasourceInfo> notExistDatasourceLists);

    void insertDatasourceInfoAuthorizations(@Param("newDatasourceAuthorizationLists") List<TempDatasourceInfoAuthorization> newDatasourceAuthorizationLists);

    void insertDatasourceInfoAuthorizationOperations(@Param("newDatasourceInfoAuthorizationOperationLists") List<TempDatasourceInfoAuthorizationOperation> newDatasourceInfoAuthorizationOperationLists);

    void insertDatasourceReferences(@Param("newDatasourceReferenceLists") List<TempDatasourceReference> newDatasourceReferenceLists);

    void insertTaskCatalogues(@Param("notExistTaskCatalogueLists") List<TempTaskCatalogue> notExistTaskCatalogueLists);

    void insertTaskConfigInfos(@Param("taskConfigInfoLists") List<TempTaskConfigInfo> taskConfigInfoLists);

    void insertTaskConfigLineInfos(@Param("taskConfigLineInfoLists") List<TempTaskConfigLineInfo> taskConfigLineInfoLists);


    void insertTaskDefinitionInfos(@Param("taskDefinitionInfoLists") List<TempTaskDefinitionInfo> taskDefinitionInfoLists);

    void insertTaskVersionInfos(@Param("taskVersionInfoLists") List<TempTaskVersionInfo> taskVersionInfoLists);

    List<TempDatasourceBusiness> queryDatasourceBusinessByBatchNoFlag(@Param("batchNo") String batchNo, @Param("existFlag") int existFlag);

    List<TempDatasourceInfo> queryDatasourceInfoByBatchNoFlag(@Param("batchNo") String batchNo, @Param("existFlag") int existFlag);

    List<TempDatasourceInfoAuthorization> queryDatasourceInfoAuthorizationByBatchNoFlag(@Param("batchNo") String batchNo, @Param("existFlag") int existFlag);

    List<TempDatasourceInfoAuthorizationOperation> queryDatasourceInfoAuthorizationOperationByBatchNoFlag(@Param("batchNo") String batchNo, @Param("existFlag") int existFlag);

    List<TempDatasourceReference> queryDatasourceReferenceByBatchNoFlag(@Param("batchNo") String batchNo, @Param("existFlag") int existFlag);

    List<TempTaskCatalogue> queryTaskCatalogueByBatchNoFlag(@Param("batchNo") String batchNo, @Param("existFlag") int existFlag);

    List<TempTaskConfigInfo> queryTaskConfigInfoByBatchNo(@Param("batchNo") String batchNo);

    List<TempTaskConfigLineInfo> queryTaskConfigLineInfoByBatchNo(@Param("batchNo") String batchNo);

    List<TempTaskDefinitionInfo> queryTaskDefinitionInfoByBatchNo(@Param("batchNo") String batchNo);

    List<TempTaskVersionInfo> queryTaskVersionInfoByBatchNo(@Param("batchNo") String batchNo);

    List<TempImport> queryImportInfoByBatchNo(@Param("batchNo") String batchNo);

    DatasourceInfoAuthorizationDTO queryDatasourceInfoAuthorizationByDatasourceId(@Param("datasourceId") String datasourceId,
                                                                                  @Param("productId") String productId,
                                                                                  @Param("projectId") String projectId);

    DatasourceInfoAuthorizationOperationDTO queryDatasourceInfoAuthorizationOperationByDatasourceId(@Param("datasourceId") String datasourceId,
                                                                                                    @Param("productId") String productId,
                                                                                                    @Param("projectId") String projectId);

    DatasourceReferenceDTO queryDatasourceReferenceByDatasourceId(@Param("datasourceId") String datasourceId,
                                                                  @Param("productId") String productId,
                                                                  @Param("projectId") String projectId);

    /**
     * 查询指定名称和父目录下的任务目录
     *
     * @param name       目录名称
     * @param parentId   父目录ID
     * @param tenantCode 租户代码
     * @return 任务目录列表
     */
    TaskCatalogueDTO queryTaskCatalogueByNameAndParent(@Param("name") String name,
                                                       @Param("parentId") String parentId,
                                                       @Param("tenantCode") String tenantCode);

    void deleteTaskDefinitionInfoByTaskId(@Param("taskIds") List<String> taskIds);

    void deleteTaskConfigInfoByVersionIds(@Param("versionIds") List<String> versionIds);

    void deleteTaskConfigLineInfoByVersionIds(@Param("versionIds") List<String> versionIds);

    void deleteTaskVersionInfoByVersionIds(@Param("versionIds") List<String> versionIds);

    TaskVersionInfoDTO queryTaskVersionExist(@Param("versionId") String versionId);

    TaskConfigInfoDTO getTaskConfigInfoExist(@Param("configId") String configId);

    TaskConfigLineInfoDTO getTaskConfigLineInfoExist(@Param("lineInfoId") String lineInfoId);

    TaskDefinitionInfoDTO getTaskDefinitionInfoExist(@Param("taskId") String taskId);

    TaskCatalogueDTO getTaskCatalogExist(@Param("name") String name, @Param("tenantCode") String tenantCode, @Param("projectId") String projectId, @Param("parentId") String parentId);

    List<DiProcessDefinitionDTO> getDiProcessDefinitionByPorcessDefinitionCode(@Param("processDefinitionCode") String processDefinitionCode);

    List<DiProcessTaskRelationDTO> getDiProcessTaskRelationByPorcessDefinitionCode(@Param("processDefinitionCode") String processDefinitionCode);

    List<DiSchedulerDTO> getDiSchedulerByPorcessDefinitionCode(@Param("processDefinitionCode") String processDefinitionCode);

    void deleteDiProcessDefinitionByProcessDefinitionId(@Param("processDefinitionCode") String processDefinitionCode);

    void deleteDiProcessTaskRelationByProcessDefinitionId(@Param("processDefinitionCode") String processDefinitionCode);

    void deleteDiSchedulerByProcessDefinitionId(@Param("processDefinitionCode") String processDefinitionCode);

    List<TempDiProcessDefinition> queryDiProcessDefinitionByBatchNo(@Param("batchNo") String batchNo);

    List<TempDiProcessTaskRelation> queryDiProcessTaskRelationByBatchNo(@Param("batchNo") String batchNo);

    List<TempDiScheduler> queryDiSchedulerByBatchNo(@Param("batchNo") String batchNo);

    void insertDiProcessDefinitions(@Param("diProcessDefinitionLists") List<TempDiProcessDefinition> diProcessDefinitionLists);

    void insertDiProcessTaskRelations(@Param("diProcessTaskRelationLists") List<TempDiProcessTaskRelation> diProcessTaskRelationLists);

    void insertDiSchedulers(@Param("diSchedulerLists") List<TempDiScheduler> diSchedulerLists);

    String queryDsProcessDefinitionByNameCatalogId(@Param("name") String name, @Param("name1") String name1, @Param("name2") String name2,
                                                   @Param("catalogId") String catalogId, @Param("projectId") String projectId);
}
