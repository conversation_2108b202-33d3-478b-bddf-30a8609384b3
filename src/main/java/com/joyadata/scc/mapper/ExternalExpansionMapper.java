package com.joyadata.scc.mapper;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.scc.dto.ningbo.IntegrationCatalogDTO;
import com.joyadata.scc.dto.ningbo.ProcessInstanceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/28 11:08.
 */
@Mapper
public interface ExternalExpansionMapper {
    List<JSONObject> getProcessDefinitionList(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                              @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                              @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                              @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                              @Param("page") Integer page, @Param("pager") Integer pager, @Param("tenantCode") String tenantCode);

    List<JSONObject> getProcessDefinitionListForPg(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                              @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                              @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                              @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                              @Param("page") Integer page, @Param("pager") Integer pager, @Param("tenantCode") String tenantCode);

    List<JSONObject> getProcessDefinitionListAndRunState(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                                         @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                                         @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                                         @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                                         @Param("taskState") String taskState, @Param("taskStartTime") String taskStartTime,
                                                         @Param("page") Integer page, @Param("pager") Integer pager, @Param("tenantCode") String tenantCode);

    List<JSONObject> getProcessDefinitionListAndRunStateForPg(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                                         @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                                         @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                                         @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                                         @Param("taskState") String taskState, @Param("taskStartTime") String taskStartTime,
                                                         @Param("page") Integer page, @Param("pager") Integer pager, @Param("tenantCode") String tenantCode);

    List<JSONObject> getInstanceTime(@Param("processInstanceIds") List<String> processInstanceIds);

    List<JSONObject> getRWCount(@Param("processInstanceIds") List<String> processInstanceIds);

    List<JSONObject> getRWCountForPg(@Param("processInstanceIds") List<String> processInstanceIds);

    JSONObject getProcessDefinitionById(@Param("processDefinitionCode") String processDefinitionCode, @Param("tenantCode") String tenantCode);

    List<JSONObject> getTaskList(@Param("taskIdList") String[] taskIdList, @Param("tenantCode") String tenantCode);

    List<ProcessInstanceDTO> getProcessInstanceById(@Param("processDefinitionCode") String processDefinitionCode, @Param("tenantCode") String tenantCode,
                                                    @Param("page") Integer page, @Param("pager") Integer pager,
                                                    @Param("startTime") String startTime, @Param("endTime") String endTime,
                                                    @Param("state") String state);

    Integer getProcessDefinitionListTotal(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                          @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                          @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                          @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                          @Param("tenantCode") String tenantCode);

    Integer getProcessDefinitionListTotalForPg(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                          @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                          @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                          @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                          @Param("tenantCode") String tenantCode);

    Integer getProcessDefinitionListAndRunStateTotal(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                                     @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                                     @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                                     @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                                     @Param("taskState") String taskState, @Param("taskStartTime") String taskStartTime,
                                                     @Param("tenantCode") String tenantCode);

    Integer getProcessDefinitionListAndRunStateTotalForPg(@Param("catalogId") String catalogId, @Param("catalogName") String catalogName, @Param("processDefinitionName") String processDefinitionName,
                                                     @Param("sourceBusinessName") String sourceBusinessName, @Param("targetBusinessName") String targetBusinessName,
                                                     @Param("sourceSimpleName") String sourceSimpleName, @Param("targetSimpleName") String targetSimpleName,
                                                     @Param("sourceTableName") String sourceTableName, @Param("targetTableName") String targetTableName,
                                                     @Param("taskState") String taskState, @Param("taskStartTime") String taskStartTime,
                                                     @Param("tenantCode") String tenantCode);

    List<IntegrationCatalogDTO> getCatalogList(@Param("tenantCode") String tenantCode);

    Integer getProcessInstanceByIdTotal(@Param("processDefinitionCode") String processDefinitionCode, @Param("tenantCode") String tenantCode,
                                        @Param("startTime") String startTime, @Param("endTime") String endTime,
                                        @Param("state") String state);
}
