/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.joyadata.scc.model;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.Flag;
import com.joyadata.scc.enums.Priority;
import com.joyadata.scc.enums.TaskExecuteType;
import com.joyadata.scc.enums.TaskExecutionStatus;
import com.joyadata.scc.util.DateUtils;
import com.joyadata.scc.util.Utils;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * task instance
 */
@Data
@JoyadataTable(name = "scc_task_instance", label = "scc_task_instance", comment = "任务实例")
@EqualsAndHashCode(callSuper = true)
public class TaskInstance extends BaseBean {

    /**
     * projectId
     */
    @JoyadataColumn(label = "projectId")
    private String projectId;

    @JoyadataColumn(label = "productId")
    private String productId;


    /**
     * task name
     */
    @JoyadataColumn(label = "name")
    private String name;

    /**
     * task type
     */
    @JoyadataColumn(label = "taskType")
    private String taskType;

    /**
     * task execute type
     */
    @JoyadataColumn(label = "taskExecuteType")
    private String taskExecuteType;

    /**
     * task code
     */
    @JoyadataColumn(label = "taskId")
    private Long taskId;

    /**
     * task definition version
     */
    @JoyadataColumn(label = "taskVersion")
    private int taskVersion;

    /**
     * process instance id
     */
    @JoyadataColumn(label = "processInstanceId")
    private String processInstanceId;

    /**
     * state
     */
    @JoyadataColumn(label = "state")
    private String state;


    /**
     * task submit time
     */
    @JoyadataColumn(label = "submitTime")
    private Date submitTime;

    /**
     * task start time
     */
    @JoyadataColumn(label = "startTime")
    private Date startTime;

    /**
     * task end time
     */
    @JoyadataColumn(label = "endTime")
    private Date endTime;

    /**
     * task host
     */
    @JoyadataColumn(label = "host")
    private String host;

    /**
     * task shell execute path and the resource down from hdfs
     * default path: $base_run_dir/processInstanceId/taskInstanceId/retryTimes
     */
    @JoyadataColumn(label = "executePath")
    private String executePath;

    /**
     * task log path
     * default path: $base_run_dir/processInstanceId/taskInstanceId/retryTimes
     */
    @JoyadataColumn(label = "logPath")
    private String logPath;

    /**
     * alert flag
     */
    @JoyadataColumn(label = "alertFlag")
    private String alertFlag;

    /**
     * retry times
     */
    @JoyadataColumn(label = "retryTimes")
    private Integer retryTimes;

    /**
     * process id
     */
    @JoyadataColumn(label = "pid")
    private String pid;

    /**
     * appLink
     */
    @JoyadataColumn(label = "appLink")
    private String appLink;

    @JoyadataColumn(label = "taskParams")
    private JSONObject taskParams;

    /**
     * flag
     */
    @JoyadataColumn(label = "flag")
    private String flag;

    /**
     * task retry interval, unit: minute
     */
    @JoyadataColumn(label = "retryInterval")
    private Integer retryInterval;

    /**
     * max retry times
     */
    @JoyadataColumn(label = "maxRetryTimes")
    private Integer maxRetryTimes;

    /**
     * task intance priority
     */
    @JoyadataColumn(label = "taskInstancePriority")
    private String taskInstancePriority;

    /**
     * workerGroup
     */
    @JoyadataColumn(label = "workerGroup")
    private String workerGroup;

    /**
     * environment code
     */
    @JoyadataColumn(label = "environmentCode")
    private Long environmentCode;

    /**
     * executor id
     */
    @JoyadataColumn(label = "executorId")
    private String executorId;

    /**
     * environment config
     */
    @JoyadataColumn(label = "environmentConfig")
    private String environmentConfig;

    /**
     * task first submit time.
     */
    @JoyadataColumn(label = "firstSubmitTime")
    private Date firstSubmitTime;

    /**
     * delay execution time.
     */
    @JoyadataColumn(label = "delayTime")
    private Integer delayTime;

    /**
     * varPool string
     */
    @JoyadataColumn(label = "varPool")
    private String varPool;

    /**
     * task group id
     */
    @JoyadataColumn(label = "taskGroupId")
    private String taskGroupId;

    /**
     * cpu quota
     */
    @JoyadataColumn(label = "cpuQuota")
    private Integer cpuQuota;

    /**
     * dry run flag
     */
    @JoyadataColumn(label = "dryRun")
    private Integer dryRun;

    /**
     * max memory
     */
    @JoyadataColumn(label = "dryRun")
    private Integer memoryMax;

    /**
     * run time
     */
    @JoyadataColumn(label = "runTimes")
    private int runTimes;

    /**
     * test flag
     */
    @JoyadataColumn(label = "testFlag")
    private Integer testFlag;

    /**
     * 工作流实例名称
     */
//    @JoyadataJoin(label = "工作流实例名称", targetBean = ProcessInstance.class, selfColumn = "processInstanceId", targetColumn = "id", valueColumn = "name")
    @JoyadataColumn(label = "工作流实例名称")
    private String processInstanceName;

    @JoyadataColumn(label = "失败停止策略:stop/continue")
    private String errorStrategy;


    /**
     * user define parameters string
     */
    @JoyadataColumn(label = "globalParams")
    private String globalParams;

    @JoyadataTransient(label = "cpu占比")
    private String cpuProportion;
    @JoyadataTransient(label = "内存占比")
    private String memProportion;

    /**
     * 运行时长
     */
    @JoyadataTransient
    private String duration;
    /**
     * 工作流id
     */
    @JoyadataTransient
    private String processDefinitionId;
    @JoyadataTransient(label = "执行人")
    private String executorName;

    @JoyadataColumn(label = "目录id", length = 512)
    private String catalogId;

    // @JoyadataJoin(label = "目录parentIds", comment = "用来算目录关联子级任务数量", targetBean = Catalog.class, valueColumn = "parentIds")
    @JoyadataColumn(label = "目录parentIds", length = 512)
    private String catalogParentIds;

    @JoyadataColumn(label = "会计日期")
    private String acDate;

    @Override
    public void afterDbInit() {
        //将枚举值转换成字符串
        afterDbInitTaskExecuteType();
        afterDbInitState();
        afterDbInitAlertFlag();
        afterDbInitFlag();
        afterDbInitTaskInstancePriority();
        setDuration(DateUtils.format2Duration(getStartTime(), getEndTime()));
        //将任务名称的目录id后缀去掉
        String name = getName();
        if (StringUtils.isNotBlank(name)) {
            if (name.contains("__[") && name.contains("_copy")) {
                //如果是复制的工作流，要截取中间的一节
                setName(Utils.removeBetween(name, "__", "_copy"));
            } else if (name.contains("__[")) {
                setName(name.substring(0, name.lastIndexOf("__[")));
            }
        }
    }

    private void afterDbInitTaskExecuteType() {
        try {
            setTaskExecuteType(TaskExecuteType.of(Integer.parseInt(getTaskExecuteType())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitState() {
        try {
            setState(TaskExecutionStatus.of(Integer.parseInt(getState())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitAlertFlag() {
        try {
            setAlertFlag(Flag.of(Integer.parseInt(getAlertFlag())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitFlag() {
        try {
            setFlag(Flag.of(Integer.parseInt(getFlag())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitTaskInstancePriority() {
        try {
            setTaskInstancePriority(Priority.of(Integer.parseInt(getTaskInstancePriority())).name());
        } catch (Exception e) {

        }
    }

    // public static final String VIEW_SQL = "CREATE VIEW scc_task_instance AS select `business_ds`.`t_ds_task_instance`.`id` AS `id`,`business_ds`.`t_ds_task_instance`.`name` AS `name`,`business_ds`.`t_ds_task_instance`.`task_type` AS `task_type`,`business_ds`.`t_ds_task_instance`.`task_execute_type` AS `task_execute_type`,`business_ds`.`t_ds_task_instance`.`task_code` AS `task_id`,`business_ds`.`t_ds_task_instance`.`task_definition_version` AS `task_version`,`business_ds`.`t_ds_task_instance`.`process_instance_id` AS `process_instance_id`,`business_ds`.`t_ds_task_instance`.`state` AS `state`,`business_ds`.`t_ds_task_instance`.`submit_time` AS `submit_time`,`business_ds`.`t_ds_task_instance`.`start_time` AS `start_time`,`business_ds`.`t_ds_task_instance`.`end_time` AS `end_time`,`business_ds`.`t_ds_task_instance`.`host` AS `host`,`business_ds`.`t_ds_task_instance`.`execute_path` AS `execute_path`,`business_ds`.`t_ds_task_instance`.`log_path` AS `log_path`,`business_ds`.`t_ds_task_instance`.`alert_flag` AS `alert_flag`,`business_ds`.`t_ds_task_instance`.`retry_times` AS `retry_times`,`business_ds`.`t_ds_task_instance`.`pid` AS `pid`,`business_ds`.`t_ds_task_instance`.`app_link` AS `app_link`,`business_ds`.`t_ds_task_instance`.`task_params` AS `task_params`,`business_ds`.`t_ds_task_instance`.`flag` AS `flag`,`business_ds`.`t_ds_task_instance`.`retry_interval` AS `retry_interval`,`business_ds`.`t_ds_task_instance`.`max_retry_times` AS `max_retry_times`,`business_ds`.`t_ds_task_instance`.`task_instance_priority` AS `task_instance_priority`,`business_ds`.`t_ds_task_instance`.`worker_group` AS `worker_group`,`business_ds`.`t_ds_task_instance`.`environment_code` AS `environment_code`,`business_ds`.`t_ds_task_instance`.`executor_id` AS `executor_id`,`business_ds`.`t_ds_task_instance`.`environment_config` AS `environment_config`,`business_ds`.`t_ds_task_instance`.`first_submit_time` AS `first_submit_time`,`business_ds`.`t_ds_task_instance`.`delay_time` AS `delay_time`,`business_ds`.`t_ds_task_instance`.`var_pool` AS `var_pool`,`business_ds`.`t_ds_task_instance`.`task_group_id` AS `task_group_id`,`business_ds`.`t_ds_task_instance`.`cpu_quota` AS `cpu_quota`,`business_ds`.`t_ds_task_instance`.`dry_run` AS `dry_run`,`business_ds`.`t_ds_task_instance`.`memory_max` AS `memory_max`,`business_ds`.`t_ds_task_instance`.`test_flag` AS `test_flag`,`business_ds`.`t_ds_task_instance`.`start_time` AS `create_time`,`business_ds`.`t_ds_task_instance`.`end_time` AS `last_modification_time`,`business_ds`.`t_ds_task_instance`.`error_strategy` AS `error_strategy`,`scc_task`.`tenant_code` AS `tenant_code`,`scc_task`.`create_by` AS `create_by`,`scc_task`.`create_by_name` AS `create_by_name`,`scc_task`.`is_public` AS `is_public`,`scc_task`.`pos` AS `pos`,`scc_task`.`dbid` AS `dbid`,`scc_task`.`update_by` AS `update_by`,`scc_task`.`del_flag` AS `del_flag`,`scc_task`.`project` AS `project`,`scc_task`.`data_owner_dept_id` AS `data_owner_dept_id`,`scc_task`.`data_owner_dept_name` AS `data_owner_dept_name`,`scc_task`.`data_owner_user_id` AS `data_owner_user_id`,`scc_task`.`data_owner_user_name` AS `data_owner_user_name`,`scc_task`.`readonly` AS `readonly`,`scc_task`.`project_id` AS `project_id`,`scc_task`.`product_id` AS `product_id`,`scc_task`.`catalog_id` AS `catalog_id`,`business_ds`.`t_ds_process_instance`.`run_times` AS `run_times`,`business_ds`.`t_ds_process_instance`.`name` AS `process_instance_name` from ((`business_ds`.`t_ds_task_instance` left join `scc_task` on((concat(`business_ds`.`t_ds_task_instance`.`task_code`,'') = `scc_task`.`id`))) left join `business_ds`.`t_ds_process_instance` on(((`business_ds`.`t_ds_process_instance`.`id` = `business_ds`.`t_ds_task_instance`.`process_instance_id`) and (`scc_task`.`process_definition_code` is not null)))) where (`scc_task`.`product_id` is not null)";
}
