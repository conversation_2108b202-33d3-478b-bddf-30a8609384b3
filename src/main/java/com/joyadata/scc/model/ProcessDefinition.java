package com.joyadata.scc.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinition
 * @date 2023/11/3
 */
@Data
@JoyadataTable(name = "scc_process_definition", label = "scheduler_process_definition", comment = "工作流定义")
@JoyadataIndex(type = "UNIQUE INDEX", name = "project_name", columns = "name,project_id", comment = "同一个项目下，工作流名称不可重复", errorMsg = "同一个项目下，工作流名称不可重复！")
@JoyadataIndex(type = "INDEX", name = "productId", columns = "product_id", comment = "普通索引", errorMsg = "普通索引")
@EqualsAndHashCode(callSuper = true)
public class ProcessDefinition extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流名称", length = 256)
    private String name;
    @JoyadataColumn(label = "工作流版本", generatorValue = "1")
    private Integer version = 1;
    @JoyadataColumn(label = "描述", length = 2048)
    private String description;
    @JoyadataColumn(label = "状态（上线、下线）", generatorValue = "下线")
    private String releaseState = "下线";
    @JoyadataColumn(label = "全局变量", columnDefinition = "text")
    private String globalParamMap;
    @JoyadataColumn(label = "节点位置信息", columnDefinition = "text")
    private String locations;
    @JoyadataColumn(label = "执行策略:默认串行抛弃")
    private String executionType = "SERIAL_DISCARD";
    //    @JoyadataColumn(label = "提交状态（已提交，未提交，已撤回）", generatorValue = "已提交")
//    private String isSubmit;
    @JoyadataColumn(label = "是否是外部产品", generatorValue = "false")
    private Boolean isImport;
    @JoyadataColumn(label = "是否是任务测试生成的工作流", generatorValue = "false")
    private Boolean isTest;
    @JoyadataTransient(label = "工作流与任务关联关系")
    private List<ProcessTaskRelation> taskRelationList;
    @JoyadataTransient(label = "工作流关联的任务")
    private List<Task> taskList;
    @JoyadataTransient(label = "工作流实例用到的任务运行状况")
    private JSONObject tasksResponse;
    @JoyadataTransient(label = "是否升级版本")
    private int updateVersion;
    @JoyadataJoin(label = "定时状态", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "releaseState")
    private String scheduleStatus;
    @JoyadataJoin(label = "cron表达式", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "crontab")
    private String crontab;
    @JoyadataJoin(label = "结束时间", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "endTime")
    private Date scheduleEndTime;
    @JoyadataJoin(label = "开始时间", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "startTime")
    private Date scheduleStartTime;
    @JoyadataJoin(label = "时区", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "timezoneId")
    private String scheduleTimezoneId;
    @JoyadataJoin(label = "执行周期方案:0:推荐频率;1:自定义频率", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "scheduleFrequency")
    private int scheduleFrequency;
    @JoyadataJoin(label = "工作日历ID", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "calendarId")
    private String calendarId;

    //    @JoyadataJoin(label = "下次执行时间",
//            targetBean = ProcessDefinitionExecTime.class,
//            conditions = {"execTime=ge(${system.now})"},
//            page = 0,
//            pager = 1,
//            sortby = "execTime_asc",
//            valueColumn = "execTime")
//@JoyadataJoin(label = "下次执行时间",
//        targetBean = ProcessDefinitionExecTime.class,
//        mappingBean = ProcessDefinitionExecTime.class,
//        selfMappingColumn = "processDefinitionId",
//        targerMappingColumn = "processDefinitionId",
//        selfColumn = "id",
//        targetColumn = "processDefinitionId",
//        conditions = {"execTime=ge(${system.now})"},
//        page = 0,
//        pager = 1,
//        sortby = "execTime_asc",
//        valueColumn = "execTime")
    @JoyadataTransient(label = "下次执行时间")
    private String cronNextExecTime;
    @JoyadataTransient(label = "导入导出工作流全局参数")
    private String globalParams;

    @JoyadataColumn(label = "产品id")
    private String productId;

    @JoyadataColumn(label = "任务ids", length = -1)
    private String taskIds;

    @JoyadataTransient(label = "是否自己创建编排")
    private Boolean originProcess = true;

    @JoyadataAggJoin(targetBean = Task.class, agg = AGG.GROUP_CONCAT, valueColumn = "name", selfColumn = "task_ids", targetColumn = "id", conditionType = ConditionType.SETIN, label = "任务名称")
    private String taskNames;

    @JoyadataTransient(label = "任务来源:产品名称")
    private String productName;
    @JoyadataTransient(label = "任务数量")
    private Integer taskNum;

    @JoyadataColumn(label = "目录id", length = 512)
    private String catalogId;

    @JoyadataColumn(label = "目录parentIds", length = 512)
    private String catalogParentIds;

    @JoyadataJoin(label = "目录ParentNames", comment = "用来算目录关联子级任务数量", targetBean = ProcessDefinitionCatalog.class, selfColumn = "catalogId", valueColumn = "parentNames")
    private String catalogParentNames;

    /**
     * 把目录名称层级传进来
     */
    @JoyadataTransient(label = "目录名称")
    private List<String> catalogNames;

    @JoyadataTransient(label = "最新执行状态")
    private String execState;
    @JoyadataTransient(label = "最新执行时间")
    private Date startTime;
    @JoyadataTransient(label = "code", comment = "导出时调用海豚接口获取的数据，海豚中的code对应scc库的工作流表的id")
    private String code;
    @JoyadataTransient(label = "工作流实例name")
    private String processInstanceName;
    @JoyadataJoin(label = "失败策略", targetBean = ProcessDefinitionSetting.class, valueColumn = "failureStrategy")
    private String failureStrategy;
    @JoyadataJoin(label = "工作组", targetBean = ProcessDefinitionSetting.class, valueColumn = "workerGroup")
    private String workerGroup;
    @JoyadataJoin(label = "流程优先级", targetBean = ProcessDefinitionSetting.class, valueColumn = "processInstancePriority")
    private String processInstancePriority;

    public static class ReleaseState {
        public static final String online = "上线";
        public static final String notOnline = "下线";
    }

    @Override
    public void afterDbInit() {
        String taskIds = getTaskIds();
        if (StringUtils.isNotBlank(taskIds)) {
            setTaskNum(taskIds.split(",").length);
        } else {
            setTaskNum(0);
        }
//        String crontab = getCrontab();
//        String scheduleStatus = getScheduleStatus();
//        try {
//            if (StringUtils.isNotBlank(scheduleStatus) && Scheduler.ReleaseState.online.equals(scheduleStatus) && StringUtils.isNotBlank(crontab)) {
//                setCronNextExecTime(CronUtils.getNextExecTime(crontab, getScheduleStartTime(), getScheduleEndTime(), getScheduleTimezoneId()));
//            }
//        } catch (Exception e) {
//
//        }
    }

    @Override
    public void beforeDbInsert() {
        try {
            String locations1 = getLocations();
            if (StringUtils.isNotBlank(locations1)) {
                List<JSONObject> list = JSONArray.parseArray(locations1, JSONObject.class);
                if (null != list && list.size() > 0) {
                    setTaskIds(String.join(",", list.stream().map(json -> json.getString("taskCode")).collect(Collectors.toSet())));
                }
            }
        } catch (Exception e) {

        }
    }

    @Override
    public void beforeDbUpdate() {
        try {
            String locations1 = getLocations();
            if (StringUtils.isNotBlank(locations1)) {
                List<JSONObject> list = JSONArray.parseArray(locations1, JSONObject.class);
                if (null != list && list.size() > 0) {
                    setTaskIds(String.join(",", list.stream().map(json -> json.getString("taskCode")).collect(Collectors.toSet())));
                }
            }
        } catch (Exception e) {

        }
    }
    //    public static class IsSubmit {
//        public static final String submitted = "已提交";
//        public static final String withdraw = "已撤回";
//        public static final String notSubmitted = "未提交";
//    }
}
