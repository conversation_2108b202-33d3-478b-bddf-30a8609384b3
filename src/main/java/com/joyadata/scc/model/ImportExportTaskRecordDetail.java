package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/8/18 16:57
 */
@Data
@JoyadataTable(name = "scc_import_export_task_record_detail", label = "scc_import_export_task_record_detail", comment = "一体化导入导出记录详情")
public class ImportExportTaskRecordDetail extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "类型，0数据源，1任务，2工作流")
    private int type;
    @JoyadataColumn(label = "名称")
    private String name;
    @JoyadataColumn(label = "状态，0失败，1成功")
    private int state;
    @JoyadataColumn(label = "说明")
    private String remark;

    public ImportExportTaskRecordDetail() {
    }

    public ImportExportTaskRecordDetail(String batchNo, int type, String name, int state, String remark, String projectId) {
        this.batchNo = batchNo;
        this.type = type;
        this.name = name;
        this.state = state;
        this.remark = remark;
        this.projectId = projectId;
    }
}
