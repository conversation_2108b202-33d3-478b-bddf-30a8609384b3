package com.joyadata.scc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/24 20:41.
 */
@Data
@JoyadataTable(name = "scc_worker_group", label = "scc_worker_group", comment = "worker组", isTenant = false, isView = true, viewSql = WorkerGroup.VIEW_SQL)
@EqualsAndHashCode(callSuper = true)
public class WorkerGroup extends BaseBean {
    @JoyadataColumn(label = "worker组名称")
    private String name;
    @JoyadataColumn(label = "addrList")
    private String addrList;
    @JoyadataColumn(label = "说明")
    private String description;
    @JoyadataColumn(label = "是否是系统默认")
    private boolean systemDefault;
    @JoyadataColumn(label = "其他参数")
    private String otherParamsJson;
    @JoyadataColumn(label = "项目id")
    private String projectId;

    public final static String VIEW_SQL = "CREATE VIEW scc_worker_group AS SELECT'1' AS dbid,'dedp' AS project,'1' AS is_public,worker_group.id AS id,worker_group.`name` AS name,worker_group.`addr_list` AS addr_list,worker_group.`create_time` AS create_time,worker_group.`update_time` AS update_time,worker_group.`description` AS description,worker_group.`other_params_json` AS other_params_json FROM`business_ds`.`t_ds_worker_group` worker_group";

}
