package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_task_definition_info", label = "任务统计表", isPublic = true)
public class TempTaskDefinitionInfo extends BaseBean {
    /**
     * 导入批次号
     */
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    /**
     * 任务id
     */
    @JoyadataColumn(label = "任务id")
    private String taskId;
    /**
     * 任务名称
     */
    @JoyadataColumn(label = "任务名称")
    private String taskName;
    /**
     * 运行类型：0-周期任务，1-手动任务
     */
    @JoyadataColumn(label = "运行类型")
    private int runType;
    /**
     * 任务类型：0-离线同步（默认选中），1-CDC同步
     */
    @JoyadataColumn(label = "任务类型")
    private int taskType;
    /**
     * 当前使用的版本的uuid
     */
    @JoyadataColumn(label = "当前使用的版本的uuid")
    private String currentVersionId;
    /**
     * 临时使用版本的uuid(即配置的uuid)
     */
    @JoyadataColumn(label = "临时使用版本的uuid")
    private String tmpVersionId;
    /**
     * 引擎类型：0-seatunnel，1-datax
     */
    @JoyadataColumn(label = "引擎类型")
    private int engineType;
    /**
     * 任务来源：0-页面创建，1-导入
     */
    @JoyadataColumn(label = "任务来源")
    private int sourceType;
    /**
     * 离线类型：0-ETL，1-批量同步
     */
    @JoyadataColumn(label = "离线类型")
    private int offlineType;
    /**
     * 上线状态：0-下线，1-上线
     */
    @JoyadataColumn(label = "上线状态")
    private int onlineStatus;
    /**
     * 任务状态:警告状态:warning;异常状态:failed;正常状态:normal
     */
    @JoyadataColumn(label = "任务状态")
    private String taskStatus;
    @JoyadataColumn(label = "更新者用户Id")
    private String updateBy;
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    @JoyadataColumn(label = "备注")
    private String remark;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    @JoyadataColumn(label = "任务目录id")
    private String taskCatalogueName;
    /**
     * 自动布局：0-否，1-是
     */
    @JoyadataColumn(label = "自动布局")
    private int autoLayout;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
