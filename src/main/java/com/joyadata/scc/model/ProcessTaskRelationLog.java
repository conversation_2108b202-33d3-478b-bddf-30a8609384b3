package com.joyadata.scc.model;

import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessTaskRelationLog
 * @date 2023/11/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_process_task_relation_log", label = "工作流与任务的关联log表",
        comment = "用于控制版本", isView = true, viewSql = ProcessTaskRelationLog.VIEW_SQL, isTenant = false)
public class ProcessTaskRelationLog extends ProcessTaskRelation {


    public static final String VIEW_SQL = "CREATE VIEW scc_process_task_relation_log AS SELECT 'dedp' AS `project`,'1' AS dbid,`business_ds`.`t_ds_process_task_relation_log`.`id` AS `id`,'1' AS `is_public`,`business_ds`.`t_ds_process_task_relation_log`.`name` AS `name`,`business_ds`.`t_ds_process_task_relation_log`.`project_code` AS `project_id`,`business_ds`.`t_ds_process_task_relation_log`.`process_definition_code` AS `process_definition_code`,`business_ds`.`t_ds_process_task_relation_log`.`process_definition_version` AS `process_definition_version`,`business_ds`.`t_ds_process_task_relation_log`.`pre_task_code` AS `pre_task_code`,`business_ds`.`t_ds_process_task_relation_log`.`pre_task_version` AS `pre_task_version`,`business_ds`.`t_ds_process_task_relation_log`.`post_task_code` AS `post_task_code`,`business_ds`.`t_ds_process_task_relation_log`.`post_task_version` AS `post_task_version`,`business_ds`.`t_ds_process_task_relation_log`.`condition_type` AS `condition_type`,`business_ds`.`t_ds_process_task_relation_log`.`condition_params` AS `condition_params`,`business_ds`.`t_ds_process_task_relation_log`.`create_time` AS `create_time`,`business_ds`.`t_ds_process_task_relation_log`.`update_time` AS `last_modification_time` FROM \t`business_ds`.`t_ds_process_task_relation_log`";
}
