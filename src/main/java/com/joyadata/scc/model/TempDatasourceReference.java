package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源引入表
 *
 * <AUTHOR>
 * @Date 2025/8/8 11:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_datasource_reference", label = "数据源授权操作记录表", isPublic = true)
public class TempDatasourceReference extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "数据源信息业务主键（UUID）")
    private String datasourceInfoId;
    @JoyadataColumn(label = "产品（product）或者项目（project）")
    private String dataType;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "项目id")
    private String itemId;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
