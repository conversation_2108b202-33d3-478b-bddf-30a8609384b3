package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskStatistics
 * @date 2023/11/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_task_statistics", label = "任务统计表", isPublic = true)
public class TaskStatistics extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "项目名称")
    private String projectName;
    @JoyadataColumn(label = "日期")
    private Date date;
    @JoyadataColumn(label = "周几")
    private String weekDay;
    @JoyadataColumn(label = "提交数量")
    private Integer submittedNum;
    @JoyadataColumn(label = "正在运行数量")
    private Integer runingNum;
    @JoyadataColumn(label = "成功数量")
    private Integer successNum;
    @JoyadataColumn(label = "失败数量")
    private Integer failNum;
    @JoyadataColumn(label = "暂停数量")
    private Integer pauseNum;
    @JoyadataColumn(label = "容错数量")
    private Integer faultTolerantNum;
    @JoyadataColumn(label = "kill数量")
    private Integer killNum;
    @JoyadataColumn(label = "延时数量")
    private Integer delayedNum;
    @JoyadataColumn(label = "强制成功数量")
    private Integer coerceSuccessNum;
    @JoyadataColumn(label = "派发数量")
    private Integer distributeNum;
}
