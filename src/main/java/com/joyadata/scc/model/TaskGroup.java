package com.joyadata.scc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/21 13:35.
 */
@Data
@JoyadataTable(name = "scc_task_group", label = "scc_task_group", comment = "任务组", isView = true, viewSql = TaskGroup.VIEW_SQL)
@EqualsAndHashCode(callSuper = true)
public class TaskGroup extends BaseBean {
    /**
     * task_group name
     */
    @JoyadataColumn(label = "任务组名称")
    private String name;

    @JoyadataColumn(label = "备注")
    private String description;
    /**
     * 作业组大小
     */
    @JoyadataColumn(label = "作业组大小")
    private int groupSize;
    /**
     * 已使用作业组大小
     */
    @JoyadataColumn(label = "已使用作业组大小")
    private int useSize;
    /**
     * 0 not available, 1 available
     */
    @JoyadataColumn(label = "状态")
    private Integer status;
    /**
     * project Id
     */
    @JoyadataColumn(label = "项目id")
    private String projectId;
    /**
     * 数据源id
     */
    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;
    /**
     * 数据源名称
     */
    @JoyadataColumn(label = "数据源名称")
    private String dataName;

    public final static String VIEW_SQL = "CREATE VIEW scc_task_group AS select '1' AS `dbid`,'dedp' AS `project`,'1' AS `is_public`,`ds_task_group`.`id` AS `id`,`ds_task_group`.`name` AS `name`,`ds_task_group`.`project_code` AS `project_id`,`joyadata`.`tms_project`.`name` AS `project_name`,`joyadata`.`tms_project`.`tenant_code` AS `tenant_code`,`ds_task_group`.`description` AS `description`,`ds_task_group`.`group_size` AS `group_size`,`ds_task_group`.`use_size` AS `use_size`,`ds_task_group`.`user_id` AS `create_by`,`ds_task_group`.`status` AS `status`,`ds_task_group`.`create_time` AS `create_time`,`ds_task_group`.`update_time` AS `last_modification_time`,`ds_task_group`.`datasource_info_id` AS `datasource_info_id`,`ds_task_group`.`data_name` AS `data_name` from (`business_ds`.`t_ds_task_group` `ds_task_group` left join `joyadata`.`tms_project` on((`joyadata`.`tms_project`.`id` = `ds_task_group`.`project_code`)))";

}
