package com.joyadata.scc.model.imports.di;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/20 15:17
 */
@Data
public class DiProcessTaskRelationDTO implements Serializable {
    private String project;
    private String id;
    private int isPublic;
    private String projectId;
    private String processDefinitionCode;
    private int processDefinitionVersion;
    private String preTaskCode;
    private int preTaskVersion;
    private String postTaskCode;
    private int postTaskVersion;
    private String conditionType;
    private String conditionParams;
    private int isRely;
    private String tenantCode;
    private Date createTime;
    private Date lastModificationTime;
    private String createBy;
    private String createByName;
    private String updateBy;
    private Integer delFlag;
    private String dataOwnerDeptId;
    private String dataOwnerDeptName;
    private String dataOwnerUserId;
    private String dataOwnerUserName;
    private int pos;
    private Integer readonly;

}
