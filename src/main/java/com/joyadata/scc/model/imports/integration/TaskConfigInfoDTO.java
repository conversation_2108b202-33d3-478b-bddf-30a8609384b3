package com.joyadata.scc.model.imports.integration;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:39
 */
@Data
public class TaskConfigInfoDTO implements Serializable {
    private int id;
    /**
     * 导入批次号
     */
    private String batchNo;
    /**
     * 任务配置id
     */
    private String configId;
    /**
     * 外键：任务版本id
     */
    private String versionId;
    /**
     * 组件名字(可支持自定义)
     */
    private String pluginName;
    /**
     * 组件编码
     */
    private int pluginCode;
    /**
     * 基础配置中的节点编号
     */
    private String pluginNum;
    /**
     * 连接模式
     */
    private String connectorType;
    /**
     * 场景类型：0-单表，1-全库
     */
    private int sceneMode;
    /**
     * 配置状态：0-临时状态，1-使用中
     */
    private int configStatu;
    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String dataType;
    /**
     * 数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号
     */
    private String dataVersion;
    /**
     * 组件类型：SOURCE/SINK
     */
    private String type;
    /**
     * 分类id
     */
    private int pluginClassifyId;
    /**
     * 组件基础配置，如果是cdc组件则此处存同步来源相关配置
     */
    private String pluginBasicsConfig;
    /**
     * 组件表结构配置，如果是cdc组件则此处存目标表相关的配置
     */
    private String pluginTableStructure;
    /**
     * 组件表结构配置，库表批量部分的表结构参数
     */
    private String batchPluginTableStructures;
    /**
     * 组件高级配置
     */
    private String pluginAdvancedConfig;
    /**
     * 组件高级配置，库表批量部分的高级配置参数
     */
    private String batchPluginAdvancedConfig;
    /**
     * 组件配置状态
     */
    private String pluginFailedStatus;
    /**
     * 保存状态
     */
    private String isValidNodeInfo;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String createUserId;
    private String createDeptId;
    private String remark;
    private String projectId;
    private String tenantId;
}
