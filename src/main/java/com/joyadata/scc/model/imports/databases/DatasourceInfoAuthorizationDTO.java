package com.joyadata.scc.model.imports.databases;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/9 14:10
 */
@Data
public class DatasourceInfoAuthorizationDTO implements Serializable {
    private int id;
    private String datasourceInfoId;
    private String dataType;
    private String productId;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String createUserId;
    private String createDeptId;
    private String remark;
    private String projectId;
    private String tenantId;
    private String authorizationUuid;
    private String itemId;
    private String type;
    private int maximumNumberConnections;
    private int existFlag;
    private String beforeJson;
}
