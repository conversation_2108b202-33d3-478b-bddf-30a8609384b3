package com.joyadata.scc.model.imports.databases;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务系统表
 *
 * <AUTHOR>
 * @Date 2025/7/29 13:51
 */
@Data
public class DatasourceBusinessDTO implements Serializable {
    /**
     * 自增id
     */
    private int id;
    /**
     * 业务系统名称
     */
    private String businessName;
    /**
     * 简称
     */
    private String simpleName;
    /**
     * 所属部门
     */
    private String departName;
    /**
     * 目前状况 0 再用 1停用 2在建 3 拟停用 4其他
     */
    private String businessStatus;
    /**
     * 系统类型
     */
    private String businessType;
    /**
     * 访问地址
     */
    private String interviewAddress;
    /**
     * 上线日期
     */
    private String onlineDate;
    /**
     * 重要程度
     */
    private String importanceDegree;
    /**
     * 机房地址
     */
    private String computerIp;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建者用户Id
     */
    private String createUserId;
    /**
     * 创建者用户单位id
     */
    private String createDeptId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 唯一标识
     */
    private String uuid;
    private String systemLevel;
    private String privateKey;
    private String publicKey;
    /**
     * 负责人（id）
     */
    private String managerId;
    /**
     * 负责人（名称）
     */
    private String managerName;

    /**
     * 是否存在，0-不存在，1-存在
     */
    private int existFlag;
    /**
     * 修改前数据
     */
    private String beforeJson;

}
