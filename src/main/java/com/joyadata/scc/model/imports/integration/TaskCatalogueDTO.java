package com.joyadata.scc.model.imports.integration;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:41
 */
@Data
public class TaskCatalogueDTO implements Serializable {
    private int id;
    /**
     * 导入批次号
     */
    private String batchNo;
    /**
     * 唯一标识
     */
    private String catalogueId;
    /**
     * 父目录ID 根目录为0
     */
    private String parentId;
    /**
     * 父级层级
     */
    private String parentLevel;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 运行类型（针对taskType为离线同步）：0-周期任务，1-手动任务
     */
    private int runType;
    /**
     * 任务类型：0-离线同步（默认选中），1-CDC同步
     */
    private Integer taskType;
    /**
     * 任务状态
     */
    private String taskStatus;
    /**
     * 分类级别
     */
    private int level;
    /**
     * 分类简称
     */
    private String simpleName;
    /**
     * 菜单选项
     */
    private int optionsId;
    /**
     * 是否为文件夹
     */
    private int isLeaf;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    private Date updateTime;
    private String createUserId;
    private String createDeptId;
    private String remark;
    private String projectId;
    private String tenantId;
    private int existFlag;
    private String beforeJson;
}
