package com.joyadata.scc.model.imports.databases;

import lombok.Data;

import java.io.Serializable;

/**
 * 引用表
 *
 * <AUTHOR>
 * @Date 2025/8/9 14:10
 */
@Data
public class DatasourceReferenceDTO implements Serializable {
    private int id;
    private String datasourceInfoId;
    private String dataType;
    private String productId;
    private String itemId;
    private int existFlag;
    private String beforeJson;
}
