package com.joyadata.scc.model.imports.integration;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:40
 */
@Data
public class TaskVersionInfoDTO {
    private int id;
    private String batchNo;
    /**
     * 任务版本id
     */
    private String versionId;
    /**
     * 外键：任务id
     */
    private String taskId;
    /**
     * 版本号，未发布时此字段为空，如果这个字段有值，则发布时是新增数据，版本在此基础上迭代
     */
    private String versionNum;
    /**
     * 环境配置
     */
    private String environmentConfiguration;
    /**
     * 同步策略
     */
    private String synchronizationStrategy;
    /**
     * 调度配置
     */
    private String dsgScheduleConfiguration;
    /**
     * 版本状态：0-未发布，1-已发布，2-临时
     */
    private int versionStatus;
    /**
     * 提交状态：0-未提交，1-已提交，2-临时
     */
    private int submitStatus;
    /**
     * 是否可以提交：0-可以，1-不可以
     */
    private int publishStatus;
    /**
     * 任务配置json
     */
    private String taskStxConfig;
    /**
     * 工作流名字
     */
    private String processName;
    /**
     * 工作流类型
     */
    private String processType;
    /**
     * 试运行调度实例id
     */
    private String debugTaskInstanceId;
    /**
     * 调度实例id
     */
    private String taskInstanceId;
    /**
     * 最后一次运行结果
     */
    private String lastRunResult;
    /**
     * 最后一次运行时间
     */
    private Date laseRunTime;
    /**
     * 工作流id
     */
    private String processDefinitionId;
    /**
     * 创建者
     */
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String createUserId;
    private String createDeptId;
    private String remark;
    private String projectId;
    private String tenantId;
    private String taskName;
    private int taskType;
    private String taskRemark;
    /**
     * 任务目录名字
     */
    private String taskCatalogueName;
    /**
     * 提交时间
     */
    private Date submitTime;
}
