package com.joyadata.scc.model.imports.download;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/8/25 14:10
 */
@Data
public class EngineModel {
    private String projectCatalog;
    private String projectName;
    private String remark;

    private List<TransformDTO> roles;

    private List<WorkerGroupDTO> workerGroups;

    private List<GpfdistInfoDTO> gpfdistInfos;

    private List<GpfdistInfoDTO> gdsInfos;

    private List<DatasourceInfoDTO> datasourceInfos;

    private List<String> taskNames;

    private List<String> workflowNames;
}
