package com.joyadata.scc.model.imports.integration;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:40
 */
@Data
public class TaskConfigLineInfoDTO implements Serializable {
    private int id;
    /**
     * 导入批次号
     */
    private String batchNo;
    /**
     * 业务主键
     */
    private String lineInfoId;
    /**
     * 连线名字
     */
    private String lineName;
    /**
     * 外键：任务版本id
     */
    private String versionId;
    /**
     * 外键：输入组件配置id（对应任务配置表的config_id）
     */
    private String inputConfigPluginId;
    /**
     * 外键：输出组件配置id（对应任务配置表的config_id）
     */
    private String targetConfigPluginId;
    /**
     * 输入组件名字
     */
    private String inputName;
    /**
     * 输出组件名字
     */
    private String outputName;
    /**
     * 组件分类id
     */
    private int inputPluginClassifyId;
    /**
     * 组件分类id
     */
    private int outputPluginClassifyId;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String createUserId;
    private String createDeptId;
    private String remark;
    private String projectId;
    private String tenantId;
}
