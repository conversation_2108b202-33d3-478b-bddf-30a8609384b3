package com.joyadata.scc.model.imports.integration;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/17 16:38
 */
@Data
public class TaskDefinitionInfoDTO implements Serializable {
    private int id;
    /**
     * 导入批次号
     */
    private String batchNo;
    /**
     * 任务id'
     */
    private String taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 运行类型：0-周期任务，1-手动任务
     */
    private int runType;
    /**
     * 任务类型：0-离线同步（默认选中），1-CDC同步
     */
    private int taskType;
    /**
     * 当前使用的版本的uuid
     */
    private String currentVersionId;
    /**
     * 临时使用版本的uuid(即配置的uuid)
     */
    private String tmpVersionId;
    /**
     * 引擎类型：0-seatunnel，1-datax
     */
    private int engineType;
    /**
     * 任务来源：0-页面创建，1-导入
     */
    private int sourceType;
    /**
     * 离线类型：0-ETL，1-批量同步
     */
    private int offlineType;
    /**
     * 上线状态：0-下线，1-上线
     */
    private int onlineStatus;
    /**
     * 任务状态:警告状态:warning;异常状态:failed;正常状态:normal
     */
    private String taskStatus;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String createUserId;
    private String createDeptId;
    private String remark;
    private String projectId;
    private String tenantId;
    private String taskCatalogueName;
    /**
     * 自动布局：0-否，1-是
     */
    private int autoLayout;
}
