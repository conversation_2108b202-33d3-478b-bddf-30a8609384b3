package com.joyadata.scc.model.imports.databases;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/16 18:18
 */
@Data
public class DatasourceInfoDTO implements Serializable {
    private int id;
    private String batchNo;

    private String datasourceInfoId;
    /**
     * 外键：数据源分类目录id
     */
    private int catalogueId;
    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String dataType;
    /**
     * 数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号
     */
    private String dataVersion;
    /**
     * 数据源名称
     */
    private String dataName;
    /**
     * 数据源描述
     */
    private String dataDesc;
    /**
     * 数据源连接信息, 不同数据源展示连接信息不同, 保存为json
     */
    private String linkJson;
    /**
     * 数据源填写的表单信息, 保存为json, key键要与表单的name相同
     */
    private String dataJson;
    /**
     * 连接状态 0-连接失败, 1-正常
     */
    private int status;
    /**
     * 是否有meta标志 0-否 1-是
     */
    private int isMeta;
    /**
     * 数据源类型编码
     */
    private int dataTypeCode;
    /**
     * 数据库名称
     */
    private String dbName;
    /**
     * 是否删除,1删除，0未删除
     */
    private int isDeleted;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建者用户Id
     */
    private String createUserId;
    /**
     * 创建者用户单位id
     */
    private String createDeptId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 最初授权时间
     */
    private Date firstAuthorizationTime;
    /**
     * 最新授权时间
     */
    private Date newAuthorizationTime;
    /**
     * 数据源版本
     */
    private String dbVersion;
    /**
     * 业务系统uuid
     */
    private String businessUuid;
    /**
     * 字符集
     */
    private String characterSet;
    /**
     * 时区
     */
    private String dsTimeZone;
    /**
     * 时区
     */
    private String ip;
    /**
     * 最大连接数
     */
    private long maximumNumberConnections;

    /**
     * 是否存在，0-不存在，1-存在
     */
    private int existFlag;

    private String beforeJson;
}
