package com.joyadata.scc.model.imports.di;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/20 15:17
 */
@Data
public class DiProcessDefinitionDTO implements Serializable {
    private String project;
    private String id;
    private int isPublic;
    private String projectId;
    private String name;
    private int version;
    private String description;
    private String releaseState;
    private String globalParamMap;
    private String locations;
    private String executionType;
    private int isImport;
    private int isTest;
    private String tenantCode;
    private Date createTime;
    private Date lastModificationTime;
    private String createBy;
    private String createByName;
    private String updateBy;
    private int delFlag;
    private String dataOwnerDeptId;
    private String dataOwnerDeptName;
    private String dataOwnerUserId;
    private String dataOwnerUserName;
    private int pos;
    private int readonly;
    private String productId;
    private String taskIds;
    private String submitStatus;
    private String publishStatus;
}
