package com.joyadata.scc.model.imports.integration;

import com.joyadata.scc.dto.ProcessExportDTO;
import com.joyadata.scc.model.imports.databases.DatasourceBusinessDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoAuthorizationDTO;
import com.joyadata.scc.model.imports.databases.DatasourceInfoDTO;
import com.joyadata.scc.model.imports.di.DiProcessDefinitionDTO;
import com.joyadata.scc.model.imports.di.DiProcessTaskRelationDTO;
import com.joyadata.scc.model.imports.di.DiSchedulerDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:06
 */
@Data
public class IntegrationDTO {
    //数据源相关
    private List<DatasourceInfoDTO> datasource = new ArrayList<>();
    private List<DatasourceBusinessDTO> datasourceBusiness = new ArrayList<>();
    //数据源授权相关
    private List<DatasourceInfoAuthorizationDTO> datasourceInfoAuthorizations = new ArrayList<>();
    //下面两个字段不需要，如果目标端不存在，则直接插入即可。无需从源端获取，再导入
    //private List<DatasourceInfoAuthorizationOperationDTO> datasourceInfoAuthorizationOperations = new ArrayList<>();
    // 数据源引入
    //private List<DatasourceReferenceDTO> datasourceReferences = new ArrayList<>();

    //集成相关
    private List<TaskDefinitionInfoDTO> taskDefinitionInfos = new ArrayList<>();
    private List<TaskVersionInfoDTO> taskVersionInfos = new ArrayList<>();
    private List<TaskCatalogueDTO> taskCatalogues = new ArrayList<>();
    private List<TaskCatalogueDTO> taskCatalogueInfos = new ArrayList<>();
    private List<TaskConfigInfoDTO> taskConfigInfos = new ArrayList<>();
    private List<TaskConfigLineInfoDTO> taskConfigLineInfos = new ArrayList<>();
    //调度中心json信息
    private List<ProcessExportDTO> sccInfos = new ArrayList<>();

    private List<DiProcessDefinitionDTO> diProcessDefinition = new ArrayList<>();
    private List<DiProcessTaskRelationDTO> diProcessTaskRelations = new ArrayList<>();
    private List<DiSchedulerDTO> diScheduler = new ArrayList<>();

    private Map<String, List<String>> deleteProcessInfos;
}
