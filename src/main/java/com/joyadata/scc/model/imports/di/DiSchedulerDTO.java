package com.joyadata.scc.model.imports.di;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/20 15:17
 */
@Data
public class DiSchedulerDTO implements Serializable {
    private String project;
    private String id;
    private Integer isPublic;
    private String projectId;
    private String processDefinitionCode;
    private String calendarId;
    private Date startTime;
    private Date endTime;
    private String timezoneId;
    private String warningType;
    private String warningGroupId;
    private String workerGroup;
    private String crontab;
    private Integer scheduleFrequency;
    private String failureStrategy;
    private String releaseState;
    private String processInstancePriority;
    private String environmentCode;
    private String stopStrategy;
    private String tenantCode;
    private Date createTime;
    private Date lastModificationTime;
    private String createBy;
    private String createByName;
    private String updateBy;
    private Integer delFlag;
    private String dataOwnerDeptId;
    private String dataOwnerDeptName;
    private String dataOwnerUserId;
    private String dataOwnerUserName;
    private int pos;
    private Integer readonly;
}
