package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/20 15:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_di_process_task_relation", label = "di关联任务", isPublic = true)
public class TempDiProcessTaskRelation extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionCode;
    @JoyadataColumn(label = "工作流版本")
    private int processDefinitionVersion;
    @JoyadataColumn(label = "前置任务id")
    private String preTaskCode;
    @JoyadataColumn(label = "前置任务版本")
    private int preTaskVersion;
    @JoyadataColumn(label = "当前任务id")
    private String postTaskCode;
    @JoyadataColumn(label = "当前任务版本")
    private int postTaskVersion;
    @JoyadataColumn(label = "海豚表的字段（暂时没用到）")
    private String conditionType;
    @JoyadataColumn(label = "海豚表的字段（暂时没用到）")
    private String conditionParams;
    @JoyadataColumn(label = "前置id是否是依赖节点")
    private int isRely;
    @JoyadataColumn(label = "是否存在")
    private String existFlag;
    @JoyadataColumn(label = "导入前json")
    private String beforeJson;
    @JoyadataColumn(label = "导入后json")
    private String afterJson;
}
