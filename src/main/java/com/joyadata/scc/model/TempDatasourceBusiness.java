package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/29 14:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_datasource_business", label = "数据源业务系统临时表", isPublic = true)
public class TempDatasourceBusiness extends BaseBean {

    @JoyadataColumn(label = "批次号")
    private String batchNo;
    /**
     * 业务系统名称
     */
    @JoyadataColumn(label = "业务系统名称")
    private String businessName;
    /**
     * 简称
     */
    @JoyadataColumn(label = "简称")
    private String simpleName;
    /**
     * 所属部门
     */
    @JoyadataColumn(label = "所属部门")
    private String departName;
    /**
     * 目前状况 0 再用 1停用 2在建 3 拟停用 4其他
     */
    @JoyadataColumn(label = "目前状况")
    private String businessStatus;
    /**
     * 系统类型
     */
    @JoyadataColumn(label = "系统类型")
    private String businessType;
    /**
     * 访问地址
     */
    @JoyadataColumn(label = "访问地址")
    private String interviewAddress;
    /**
     * 上线日期
     */
    @JoyadataColumn(label = "上线日期")
    private String onlineDate;
    /**
     * 重要程度
     */
    @JoyadataColumn(label = "重要程度")
    private String importanceDegree;
    /**
     * 机房地址
     */
    @JoyadataColumn(label = "机房地址")
    private String computerIp;

    /**
     * 创建时间
     */
    @JoyadataColumn(label = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    /**
     * 创建者用户Id
     */
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    /**
     * 创建者用户单位id
     */
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    /**
     * 备注
     */
    @JoyadataColumn(label = "备注")
    private String remark;
    /**
     * 项目id
     */
    @JoyadataColumn(label = "项目id")
    private String projectId;
    /**
     * 租户id
     */
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    /**
     * 唯一标识
     */
    @JoyadataColumn(label = "唯一标识")
    private String uuid;
    @JoyadataColumn(label = "systemLevel")
    private String systemLevel;
    @JoyadataColumn(label = "privateKey")
    private String privateKey;
    @JoyadataColumn(label = "publicKey")
    private String publicKey;
    /**
     * 负责人（id）
     */
    @JoyadataColumn(label = "负责人id")
    private String managerId;
    /**
     * 负责人（名称）
     */
    @JoyadataColumn(label = "负责人名称")
    private String managerName;
    @JoyadataColumn(label = "是否存在")
    private int existFlag;
    @JoyadataColumn(label = "修改前的json")
    private String beforeJson;
    @JoyadataColumn(label = "修改后的json")
    private String afterJson;
}
