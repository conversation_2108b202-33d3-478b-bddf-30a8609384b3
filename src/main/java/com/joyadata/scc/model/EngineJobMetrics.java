package com.joyadata.scc.model;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JoyadataTable(name = "scc_engine_job_metrics", label = "scc_engine_job_meterics", comment = "引擎执行记录")
public class EngineJobMetrics extends BaseBean {

    @JoyadataColumn(label = "实例id")
    private String instanceId;
    @JoyadataColumn(label = "任务id")
    private String jobId;
    @JoyadataColumn(label = "pipelineId")
    private Integer pipelineId;
    @JoyadataColumn(label = "读总数")
    private long readRowCount;
    @JoyadataColumn(label = "写总数")
    private long writeRowCount;
    @JoyadataColumn(label = "上游信息", length = -1)
    private String sourceVertextInfo;
    @JoyadataColumn(label = "下游信息", length = -1)
    private String sinkVertextInfo;
    @JoyadataColumn(label = "读速率（条/s）")
    private long readQps;
    @JoyadataColumn(label = "写速率（条/s）")
    private long writeQps;
    @JoyadataColumn(label = "读延迟")
    private long recordDelay;
    /**
     * "NOTRUNNING": "未运行"
     * "RUNNING": "运行中",
     * "FAILING": "失败中",
     * "FAILED": "失败",
     * "CANCELLING": "取消中",
     * "CANCELED": "取消完成",
     * "FINISHED": "成功"
     */
    @JoyadataColumn(label = "状态")
    private String status;
    @JoyadataColumn(label = "总耗时")
    private long totalCost;
    @JoyadataColumn(label = "开始时间")
    public Date startDate;
    @JoyadataColumn(label = "提交时间")
    private Date submitTime;
    @JoyadataColumn(label = "完成时间")
    private Date finishTime;
    @JoyadataColumn(label = "读取数据大小(/b)")
    private String sourceReceivedBytes;
    @JoyadataColumn(label = "写入数据大小(/b)")
    private String sinkWriteBytes;
    @JoyadataColumn(label = "每秒读取数据大小(/b)")
    private String sourceReceivedBytesPerSeconds;
    @JoyadataColumn(label = "每秒写入数据大小(/b)")
    private String sinkWriteBytesPerSeconds;
    @JoyadataColumn(label = "源端总数")
    private Long sourceCount;
    //    @JoyadataTransient(label = "读取速率(/s)")
//    private long readRowsSpeed;
//    @JoyadataTransient(label = "写入速率(/s)")
//    private long writeRowsSpeed;
    @JoyadataTransient(label = "任务id")
    private String taskId;
    @JoyadataTransient(label = "同步进度")
    private BigDecimal syncProgress;

    public EngineJobMetrics(String instanceId, Integer pipelineId, String status, Date startDate, Date submitTime, Date finishTime, String taskId) {
        this.instanceId = instanceId;
        this.pipelineId = pipelineId;
        this.readRowCount = 0;
        this.writeRowCount = 0;
        this.readQps = 0;
        this.writeQps = 0;
        this.recordDelay = 0;
        this.status = status;
        if (null != startDate && null != finishTime) {
            long totalCost = (finishTime.getTime() - startDate.getTime()) / 1000;
            this.totalCost = totalCost < 0 ? 1L : totalCost;
        }
        this.startDate = startDate;
        this.submitTime = submitTime;
        this.finishTime = finishTime;
        this.taskId = taskId;
    }
}
