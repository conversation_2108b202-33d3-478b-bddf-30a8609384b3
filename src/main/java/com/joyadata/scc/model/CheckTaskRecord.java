package com.joyadata.scc.model;

import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_check_task_record", label = "检查记录表", isPublic = true)
public class CheckTaskRecord extends CheckTask {
    //    private String projectId;
//    @JoyadataColumn(label = "工作流Id")
//    private String processDefinitionId;
//    @JoyadataColumn(label = "任务Id")
//    private String taskId;
//    @JoyadataColumn(label = "计划执行时间")
//    private LocalTime planExecTime;
    @JoyadataColumn(label = "检查时间")
    private Date checkTime;
    @JoyadataColumn(label = "检查状态（0:失败;1:成功）")
    private Integer status;

    //检查状态（0:失败;1:成功）
    public static class Status {
        public static final Integer error = 0;
        public static final Integer success = 1;
    }
}
