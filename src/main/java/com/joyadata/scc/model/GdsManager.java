package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JoyadataTable(name = "scc_gds_manager", label = "scc_gds_manager", comment = "gds管理")
@JoyadataIndex(type = "UNIQUE INDEX",
        name = "uk_scc_gds_manager_gdsAddr",
        columns = "project_id,tenant_code,gds_addr",
        comment = "gds地址不可重复", errorMsg = "同一个项目下，gds地址不可重复！")
@EqualsAndHashCode(callSuper = true)
public class GdsManager extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "gds地址")
    private String gdsAddr;
    @JoyadataColumn(label = "gds路径")
    private String gdsPath;
}
