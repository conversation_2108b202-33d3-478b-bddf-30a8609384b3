package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_task_config_line_info", label = "任务统计表", isPublic = true)
public class TempTaskConfigLineInfo extends BaseBean {
    /**
     * 导入批次号
     */
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    /**
     * 业务主键
     */
    @JoyadataColumn(label = "业务主键")
    private String lineInfoId;
    /**
     * 连线名字
     */
    @JoyadataColumn(label = "连线名字")
    private String lineName;
    /**
     * 外键：任务版本id
     */
    @JoyadataColumn(label = "任务版本id")
    private String versionId;
    /**
     * 外键：输入组件配置id（对应任务配置表的config_id）
     */
    @JoyadataColumn(label = "输入组件配置id")
    private String inputConfigPluginId;
    /**
     * 外键：输出组件配置id（对应任务配置表的config_id）
     */
    @JoyadataColumn(label = "输出组件配置id")
    private String targetConfigPluginId;
    /**
     * 输入组件名字
     */
    @JoyadataColumn(label = "输入组件名字")
    private String inputName;
    /**
     * 输出组件名字
     */
    @JoyadataColumn(label = "输出组件名字")
    private String outputName;
    /**
     * 组件分类id
     */
    @JoyadataColumn(label = "组件分类id")
    private int inputPluginClassifyId;
    /**
     * 组件分类id
     */
    @JoyadataColumn(label = "组件分类id")
    private int outputPluginClassifyId;
    @JoyadataColumn(label = "更新者用户Id")
    private String updateBy;
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    @JoyadataColumn(label = "备注")
    private String remark;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
