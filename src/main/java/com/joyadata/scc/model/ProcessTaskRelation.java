package com.joyadata.scc.model;

import com.alibaba.excel.util.StringUtils;
import com.joyadata.model.BaseBean;
import com.joyadata.scc.util.Utils;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessTaskRelation
 * @date 2023/11/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataIndex(type = "INDEX", name = "idx_scc_process_task_relation_post_task_code", columns = "post_task_code", comment = "任务code")
@JoyadataIndex(type = "INDEX", name = "idx_scc_process_task_relation_process_definition_code", columns = "process_definition_code", comment = "工作流code")
@JoyadataTable(name = "scc_process_task_relation", label = "工作流与任务的关联表", isPublic = true)
public class ProcessTaskRelation extends BaseBean {

    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionCode;
    @JoyadataColumn(label = "工作流版本")
    private Integer processDefinitionVersion;
    @JoyadataColumn(label = "前置任务id")
    private String preTaskCode;
    @JoyadataColumn(label = "前置任务版本")
    private Integer preTaskVersion;
    @JoyadataColumn(label = "当前任务id")
    private String postTaskCode;
    @JoyadataColumn(label = "当前任务版本")
    private Integer postTaskVersion;
    @JoyadataColumn(label = "条件类型", comment = "海豚表的字段（暂时没用到）")
    private String conditionType;
    @JoyadataColumn(label = "条件参数", comment = "海豚表的字段（暂时没用到）")
    private String conditionParams;
    @JoyadataJoin(label = "任务名称", targetBean = Task.class, targetColumn = "id", selfColumn = "postTaskCode", valueColumn = "name")
    private String taskName;
    @JoyadataJoin(label = "任务名称", targetBean = Task.class, targetColumn = "id", selfColumn = "postTaskCode", valueColumn = "productId")
    private String taskProductId;

    @Override
    public void afterDbInit() {
        //将任务名称的目录id后缀去掉
        String name = getTaskName();
        if (StringUtils.isNotBlank(name)) {
            if (name.contains("__[") && name.contains("_copy")) {
                //如果是复制的工作流，要截取中间的一节
                setTaskName(Utils.removeBetween(name, "__", "_copy"));
            } else if (name.contains("__[")) {
                setTaskName(name.substring(0, name.lastIndexOf("__[")));
            }
        }
    }
}