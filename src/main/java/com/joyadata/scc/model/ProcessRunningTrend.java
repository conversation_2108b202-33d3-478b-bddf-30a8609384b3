package com.joyadata.scc.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@JoyadataTable(name = "scc_process_running_trend", label = "scc_process_running_trend", comment = "概览-工作流趋势图预统计")
@JoyadataIndex(type = "INDEX", name = "project_id_time", columns = "projectId,time", comment = "查询索引")
@EqualsAndHashCode(callSuper = true)
public class ProcessRunningTrend extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "数量")
    private Integer num;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JoyadataColumn(label = "统计时间")
    private Date time;
}
