package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: Scheduler
 * @date 2023/11/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_scheduler", label = "定时表", isPublic = true)
@JoyadataIndex(type = "UNIQUE INDEX", name = "processDefinitionCode", columns = "processDefinitionCode", comment = "一条工作流只能有一个定时", errorMsg = "定时表中已有此工作流的数据，不可重复添加！")
public class Scheduler extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionCode;
    @JoyadataColumn(label = "工作日历ID")
    private String calendarId;
    //    @JoyadataOne2One(comment="关联日历表",targetBean= Calendar.class,selfColumn = "calendarId",targetClounm = "id")
//    private Calendar calendar;
    @JoyadataColumn(label = "开始时间")
    private Date startTime;
    @JoyadataColumn(label = "结束时间")
    private Date endTime;
    @JoyadataColumn(label = "时区", comment = "调度中心不需要，海豚需要，默认给Asia/Shanghai", generatorValue = "Asia/Shanghai")
    private String timezoneId = "Asia/Shanghai";
    @JoyadataColumn(label = "告警类型")
    private String warningType;
    @JoyadataColumn(label = "告警组")
    private String warningGroupId;
    @JoyadataColumn(label = "工作组")
    private String workerGroup = "default";
    @JoyadataColumn(label = "cron表达式")
    private String crontab;
    @JoyadataColumn(label = "失败策略(END结束、CONTINUE继续)")
    private String failureStrategy = "CONTINUE";
    @JoyadataColumn(label = "定时状态(上线、下线)", generatorValue = "下线")
    private String releaseState = ReleaseState.notOnline;
    @JoyadataColumn(label = "工作流优先级")
    private String processInstancePriority = "MEDIUM";
    @JoyadataColumn(label = "环境id", generatorValue = "-1")
    private String environmentCode = "-1";
    @JoyadataJoin(label = "工作流名称", targetBean = ProcessDefinition.class, targetColumn = "id",
            selfColumn = "processDefinitionCode", valueColumn = "name")
    private String processDefinitionName;
    @JoyadataColumn(label = "停用策略:0:不停止工作流;1:停止工作流")
    private String stopStrategy;

    @JoyadataColumn(label = "执行频次", comment = "cron表达式")
    private String executionFrequency;
    @JoyadataColumn(label = "最近6次执行时间(前端回显用的)")
    private String[] historyExecutionTimes;
    @JoyadataColumn(label = "执行周期方案:0:推荐频率;1:自定义频率")
    private int scheduleFrequency;

    public static class ReleaseState {
        public static final String online = "上线";
        public static final String notOnline = "下线";
    }

    public static class FailureStrategy {
        public static final String END = "END";
        public static final String CONTINUE = "CONTINUE";
    }

}
