package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/15 13:43.
 */
@Data
@JoyadataTable(name = "scc_accounting_date", label = "scc_accounting_date", comment = "会计日期表（银行场景用到的）", isPublic = true)
@EqualsAndHashCode(callSuper = true)
public class AccountingDate extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "项目名称")
    private String projectName;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "工作流名称", length = 256)
    private String processDefinitionName;
    @JoyadataColumn(label = "会计日期", comment = "使用字符串类型是为了兼容各种格式，例如：项目1用yyyyMMdd，项目2用yyyyMMdd HH:mm:ss")
    private String acDate;
    @JoyadataColumn(label = "是否启动", comment = "0未启动，1已启动")
    private Integer executed;
    @JoyadataColumn(label = "运行状态", comment = "0成功，1失败")
    private String status;
    @JoyadataColumn(label = "启动时间")
    private Date startTime;
    @JoyadataColumn(label = "结束时间")
    private Date endTime;
}
