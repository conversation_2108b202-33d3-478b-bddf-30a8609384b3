package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/20 15:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_di_process_definition", label = "di流程定义表", isPublic = true)
public class TempDiProcessDefinition extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流名称")
    private String name;
    @JoyadataColumn(label = "工作流版本")
    private int version;
    @JoyadataColumn(label = "描述")
    private String description;
    @JoyadataColumn(label = "状态（上线、下线）")
    private String releaseState;
    @JoyadataColumn(label = "全局变量")
    private String globalParamMap;
    @JoyadataColumn(label = "节点位置信息")
    private String locations;
    @JoyadataColumn(label = "执行策略")
    private String executionType;
    @JoyadataColumn(label = "是否是导入进来的")
    private int isImport;
    @JoyadataColumn(label = "是否是任务测试生成的工作流")
    private int isTest;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "任务ids")
    private String taskIds;
    @JoyadataColumn(label = "提交状态：0：未提交 1:已提交")
    private String submitStatus;
    @JoyadataColumn(label = "发布状态：0：未发布 1：已发布")
    private String publishStatus;
    @JoyadataColumn(label = "是否存在")
    private String existFlag;
    @JoyadataColumn(label = "导入前json")
    private String beforeJson;
    @JoyadataColumn(label = "导入后json")
    private String afterJson;
}
