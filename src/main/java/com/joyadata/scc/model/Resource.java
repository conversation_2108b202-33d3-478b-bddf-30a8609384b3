package com.joyadata.scc.model;

import com.joyadata.enums.Check;
import com.joyadata.model.TreeBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_resource", label = "文件、资源管理表", isPublic = true)
@JoyadataIndex(type = "UNIQUE INDEX", name = "un_name", columns = "name,tenant_code,project_id,pid,type", comment = "同目录下名称不能重复", errorMsg = "同目录下名称不能重复")
public class Resource extends TreeBean {

    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "类别", comment = "FILE、UDF")
    private String type;
    @JoyadataColumn(label = "是否文件夹", comment = "是true、否false")
    private Boolean isFolder;
    @JoyadataColumn(label = "文件名称", length = 128)
    private String fileName;
    @JoyadataColumn(label = "全路径名", columnDefinition = "text")
    private String fullName;
    @JoyadataColumn(label = "文件格式")
    private String fileFormat;
    //@JoyadataColumn(label = "文件内容", columnDefinition = "text")
    @JoyadataTransient(label = "文件内容", comment = "不往数据库中存文件内容")
    private String fileContent;
    @JoyadataColumn(label = "大小", comment = "B、KB、MB、GB、TB、PB")
    private String size;
    @JoyadataColumn(label = "文件字节", comment = "用于计算文件夹大小")
    private Long fileByte;
    @JoyadataColumn(label = "描述", length = 256)
    private String description;
    @JoyadataColumn(label = "文件id")
    private String fileKey;
    @JoyadataColumn(label = "是否空文件夹", generatorValue = "true", comment = "空文件夹true,不是空文件夹false")
    private Boolean isEmptyFolder;
    @JoyadataColumn(label = "来源", generatorValue = "创建文件", comment = "创建文件、上传文件")
    private String origin;
    @JoyadataColumn(label = "是否禁用", comment = "用于前端框架判断引用资源时，文件夹是否置灰")
    private Boolean isDisabled;
    @JoyadataTransient(label = "是否关联任务", comment = "是true,否false")
    private Boolean isRelevanceTask;
    @JoyadataTransient(label = "是否关联函数", comment = "是true,否false")
    private Boolean isRelevanceFunction;
    @JoyadataColumn(label = "字符编码")
    private String encoder;

    public class type {
        public static final String FILE = "FILE";
        public static final String UDF = "UDF";
    }

}
