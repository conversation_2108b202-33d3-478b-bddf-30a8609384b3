package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_task_config_info", label = "任务统计表", isPublic = true)
public class TempTaskConfigInfo extends BaseBean {
    /**
     * 导入批次号
     */
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    /**
     * 任务配置id
     */
    @JoyadataColumn(label = "任务配置id")
    private String configId;
    /**
     * 外键：任务版本id
     */
    @JoyadataColumn(label = "任务版本id")
    private String versionId;
    /**
     * 组件名字(可支持自定义)
     */
    @JoyadataColumn(label = "组件名字")
    private String pluginName;
    /**
     * 组件编码
     */
    @JoyadataColumn(label = "组件编码")
    private int pluginCode;
    /**
     * 基础配置中的节点编号
     */
    @JoyadataColumn(label = "基础配置中的节点编号")
    private String pluginNum;
    /**
     * 连接模式
     */
    @JoyadataColumn(label = "连接模式")
    private String connectorType;
    /**
     * 场景类型：0-单表，1-全库
     */
    @JoyadataColumn(label = "场景类型")
    private int sceneMode;
    /**
     * 配置状态：0-临时状态，1-使用中
     */
    @JoyadataColumn(label = "配置状态")
    private int configStatu;
    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    @JoyadataColumn(label = "数据源类型")
    private String dataType;
    /**
     * 数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号
     */
    @JoyadataColumn(label = "数据源版本")
    private String dataVersion;
    /**
     * 组件类型：SOURCE/SINK
     */
    @JoyadataColumn(label = "组件类型")
    private String type;
    /**
     * 分类id
     */
    @JoyadataColumn(label = "分类id")
    private int pluginClassifyId;
    /**
     * 组件基础配置，如果是cdc组件则此处存同步来源相关配置
     */
    @JoyadataColumn(label = "组件基础配置")
    private String pluginBasicsConfig;
    /**
     * 组件表结构配置，如果是cdc组件则此处存目标表相关的配置
     */
    @JoyadataColumn(label = "组件表结构配置")
    private String pluginTableStructure;
    /**
     * 组件表结构配置，库表批量部分的表结构参数
     */
    @JoyadataColumn(label = "组件表结构配置")
    private String batchPluginTableStructures;
    /**
     * 组件高级配置
     */
    @JoyadataColumn(label = "组件高级配置")
    private String pluginAdvancedConfig;
    /**
     * 组件高级配置，库表批量部分的高级配置参数
     */
    @JoyadataColumn(label = "组件高级配置")
    private String batchPluginAdvancedConfig;
    /**
     * 组件配置状态
     */
    @JoyadataColumn(label = "组件配置状态")
    private String pluginFailedStatus;
    /**
     * 保存状态
     */
    @JoyadataColumn(label = "保存状态")
    private String isValidNodeInfo;
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    @JoyadataColumn(label = "备注")
    private String remark;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
