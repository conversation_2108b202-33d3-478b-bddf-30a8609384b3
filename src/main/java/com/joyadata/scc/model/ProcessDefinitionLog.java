package com.joyadata.scc.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.ProcessExecutionTypeEnum;
import com.joyadata.scc.enums.ReleaseState;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinitionLog
 * @date 2023/11/3
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_process_definition_log", label = "工作流log表",
        comment = "用于控制版本", isView = true, viewSql = ProcessDefinitionLog.VIEW_SQL)
public class ProcessDefinitionLog extends BaseBean {
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流名称", length = 128)
    private String name;
    @JoyadataColumn(label = "工作流版本", generatorValue = "1")
    private Integer version;
    @JoyadataColumn(label = "描述", length = 2048)
    private String description;
    @JoyadataColumn(label = "状态（上线、下线）", generatorValue = "下线")
    private String releaseState;
    @JoyadataColumn(label = "全局变量", columnDefinition = "text")
    private String globalParamMap;
    @JoyadataColumn(label = "节点位置信息", columnDefinition = "text")
    private String locations;
    @JoyadataColumn(label = "执行策略:默认串行抛弃")
    private String executionType = "SERIAL_DISCARD";
    //    @JoyadataColumn(label = "提交状态（已提交，未提交，已撤回）", generatorValue = "已提交")
//    private String isSubmit;
//    @JoyadataColumn(label = "是否是导入进来的", generatorValue = "false")
//    private Boolean isImport;
//    @JoyadataColumn(label = "是否是任务测试生成的工作流", generatorValue = "false")
//    private Boolean isTest;
//    @JoyadataTransient(label = "工作流与任务关联关系")
//    private List<ProcessTaskRelation> taskRelationList;
//    @JoyadataTransient(label = "工作流关联的任务")
//    private List<Task> taskList;
//    @JoyadataTransient(label = "工作流关联的任务的依赖")
//    private JSONObject relyTaskList;
//    @JoyadataTransient(label = "工作流实例用到的任务运行状况")
//    private JSONObject tasksResponse;
//    @JoyadataJoin(label = "定时状态", targetBean = Scheduler.class, targetColumn = "processDefinitionCode", selfColumn = "id", valueColumn = "releaseState")
//    private String scheduleStatus;

    @JoyadataColumn(label = "产品id")
    private String productId;

    @JoyadataTransient(label = "任务ids")
    private String taskIds;

    public static final String VIEW_SQL = "CREATE VIEW scc_process_definition_log AS SELECT 'dedp' AS `project`,'1' AS dbid,`business_ds`.`t_ds_process_definition_log`.`id` AS `id`,`business_ds`.`t_ds_process_definition_log`.`code` AS `process_definition_id`,'1' AS `is_public`,`business_ds`.`t_ds_process_definition_log`.`name` AS `name`,`business_ds`.`t_ds_process_definition_log`.`project_code` AS `project_id`,`business_ds`.`t_ds_process_definition_log`.`version` AS `version`,`business_ds`.`t_ds_process_definition_log`.`description` AS `description`,`business_ds`.`t_ds_process_definition_log`.`release_state` AS `release_state`,`business_ds`.`t_ds_process_definition_log`.`global_params` AS `global_param_map`,`business_ds`.`t_ds_process_definition_log`.`locations` AS `locations`,`business_ds`.`t_ds_process_definition_log`.`execution_type` AS `execution_type`,`business_ds`.`t_ds_process_definition_log`.`create_time` AS `create_time`,`business_ds`.`t_ds_process_definition_log`.`update_time` AS `last_modification_time`,`business_ds`.`t_ds_process_definition_log`.`tenant_code` AS `tenant_code`,`scc_process_definition`.`product_id` AS `product_id` FROM\t`business_ds`.`t_ds_process_definition_log`\tLEFT JOIN `scc_process_definition` ON `scc_process_definition`.id = `business_ds`.`t_ds_process_definition_log`.`code`";

    @Override
    public void afterDbInit() {
        try {
            //转换上线状态
            if ("0".equals(getReleaseState())) {
                setReleaseState(ReleaseState.OFFLINE.getCode());
            } else {
                setReleaseState(ReleaseState.ONLINE.getCode());
            }
            //转换执行策略
            switch (getExecutionType()) {
                case "0":
                    setExecutionType(ProcessExecutionTypeEnum.PARALLEL.getDescp());
                    break;
                case "1":
                    setExecutionType(ProcessExecutionTypeEnum.SERIAL_WAIT.getDescp());
                    break;
                case "2":
                    setExecutionType(ProcessExecutionTypeEnum.SERIAL_DISCARD.getDescp());
                    break;
                case "3":
                    setExecutionType(ProcessExecutionTypeEnum.SERIAL_PRIORITY.getDescp());
                    break;
                default:
                    break;
            }
            String locations1 = getLocations();
            if (StringUtils.isNotBlank(locations1)) {
                List<JSONObject> list = JSONArray.parseArray(locations1, JSONObject.class);
                if (null != list && list.size() > 0) {
                    setTaskIds(String.join(",", list.stream().map(json -> json.getString("taskCode")).collect(Collectors.toSet())));
                }
            }
        } catch (Exception e) {

        }
    }
}
