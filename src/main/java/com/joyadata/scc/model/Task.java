package com.joyadata.scc.model;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.TimeUnit;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: Task
 * @date 2023/11/3
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_task", label = "任务表", isPublic = true)
@JoyadataIndex(type = "UNIQUE INDEX", name = "name_project", columns = "name,project_id", comment = "名称唯一索引", errorMsg = "同一项目下，任务名称不可重复！")
@JoyadataIndex(type = "INDEX", name = "productId", columns = "product_id", comment = "普通索引", errorMsg = "普通索引")
@JoyadataIndex(type = "INDEX", name = "catalogId", columns = "catalog_id", comment = "普通索引", errorMsg = "普通索引")
@JoyadataIndex(type = "INDEX", name = "projectId", columns = "project_id", comment = "普通索引", errorMsg = "普通索引")
@JoyadataIndex(type = "INDEX", name = "id_catalogId", columns = "id,catalog_id", comment = "普通索引", errorMsg = "普通索引")
public class Task extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "任务名称", length = 256)
    private String name;
    @JoyadataColumn(label = "目录id")
    private String catalogId;
    @JoyadataColumn(label = "任务版本", generatorValue = "1")
    private Integer version = 1;
    @JoyadataColumn(label = "描述", length = 2048)
    private String description;
    @JoyadataColumn(label = "标签")
    private String title;
    @JoyadataColumn(label = "任务参数（脚本，参数，资源对象）")
    private JSONObject taskParams;
    @JoyadataColumn(label = "任务类型")
    private String taskType;
    @JoyadataColumn(label = "任务优先级", generatorValue = "MEDIUM")
    private String taskPriority = "MEDIUM";
    @JoyadataColumn(label = "环境code", generatorValue = "-1")
    private String environmentCode = "-1";
    @JoyadataColumn(label = "环境名称")
    private String environmentName;
    @JoyadataColumn(label = "资源名称（多个用逗号分割）", length = 512)
    private String resourceNames;
    @JoyadataColumn(label = "资源id（多个用逗号分割）", length = 512)
    private String resourceIds;
    @JoyadataColumn(label = "失败重试次数", generatorValue = "0")
    private Integer failRetryTimes = 0;
    @JoyadataColumn(label = "失败重试间隔", generatorValue = "1")
    private Integer failRetryInterval = 1;
    @JoyadataColumn(label = "cpu配额（-1无穷）", generatorValue = "-1")
    private Integer cpuQuota = -1;
    @JoyadataColumn(label = "最大内存", generatorValue = "-1")
    private Integer memoryMax = -1;
    @JoyadataColumn(label = "延迟执行时间", generatorValue = "0")
    private Integer delayTime = 0;
    @JoyadataColumn(label = "是否是外部产品", generatorValue = "false")
    private Boolean isImport;
    @JoyadataColumn(label = "超时通知策略")
    private String timeoutNotifyStrategy;
    @JoyadataColumn(label = "任务是否有效: YES/NO", generatorValue = "YES")
    private String flag = "YES";
    @JoyadataColumn(label = "任务警告超时。单位：分钟")
    private Long timeout = 0L;
    @JoyadataColumn(label = "任务执行类型", generatorValue = "BATCH")
    private String taskExecuteType = "BATCH";
    @JoyadataColumn(label = "超时标志")
    private String timeoutFlag = "CLOSE";
    @JoyadataColumn(label = "工作组")
    private String workerGroup = "default";
    @JoyadataColumn(label = "工作流code")
    private String processDefinitionCode;
    @JoyadataColumn(label = "")
    private String upstreamCodes;
    @JoyadataColumn(label = "任务组id")
    private String taskGroupId;
    @JoyadataColumn(label = "组内优先级")
    private int taskGroupPriority;
    @JoyadataJoin(label = "工作流状态", targetBean = ProcessDefinition.class, selfColumn = "processDefinitionCode", targetColumn = "id")
    private String releaseState;
    @JoyadataJoin(label = "工作流名称", targetBean = ProcessDefinition.class, selfColumn = "processDefinitionCode", targetColumn = "id", valueColumn = "name")
    private String processDefinitionName;
    @JoyadataAggJoin(label = "工作流中前置任务id", targetBean = ProcessTaskRelation.class, selfColumn = "id", targetColumn = "postTaskCode", valueColumn = "preTaskCode", agg = AGG.JSON_ARRAYAGG)
    private List<String> preTaskCode;
    @JoyadataTransient(label = "工作流实例用到的任务运行状况")
    private JSONObject taskInstance;
    @JoyadataTransient(label = "是否升级版本")
    private int updateVersion;
    @JoyadataTransient(label = "测试任务往海豚发的参数")
    private String code;
    @JoyadataJoin(label = "目录parentIds", comment = "用来算目录关联子级任务数量", targetBean = Catalog.class, valueColumn = "parentIds")
    private String catalogParentIds;
    @JoyadataJoin(label = "目录ParentNames", comment = "用来算目录关联子级任务数量", targetBean = Catalog.class, valueColumn = "parentNames")
    private String catalogParentNames;
    @JoyadataTransient(label = "任务来源:产品名称")
    private String productName;
    @JoyadataJoin(label = "目录名称", targetBean = Catalog.class, selfColumn = "catalogId", targetColumn = "id", valueColumn = "name")
    private String catalogName;
    @JoyadataColumn(label = "失败停止策略:stop/continue")
    private String errorStrategy = "stop";
    @JoyadataColumn(label = "任务超时时间.0为未设置")
    private Long taskTimeout = 0L;
    @JoyadataColumn(label = "1、告警，2、kill，3告警并且kill")
    private Integer taskTimeoutStrategy = 0;
    @JoyadataColumn(label = "任务超时时间单位")
    private TimeUnit timeoutUnit = TimeUnit.M;
    /**
     * 文件路径参数，需要将文件内容放入到任务中
     */
    @JoyadataColumn(label = "文件路径参数", comment = "要读取的文件路径", length = 512)
    private String fileParamsPath;

    @JoyadataColumn(label = "到点未运行检查（0:未开启;1:开启）", generatorValue = "0")
    private Integer notRunCheck;

    @JoyadataColumn(label = "计划执行时间")
    private LocalTime planExecTime;
    @JoyadataColumn(label = "有效范围")
    private Integer effectiveRange;
    @JoyadataColumn(label = "有效范围单位(D:天;H:时;M:分)")
    private TimeUnit effectiveRangeUnit = TimeUnit.M;

    @JoyadataTransient(label = "是否是目录")
    private Boolean isCatalog = false;

    @JoyadataTransient(label = "目录名称")
    private List<String> catalogNames;

    @JoyadataTransient(label = "导出的时候，数据源信息key数据源id，value 数据源名称")
    private Map<String, String> datasourceInfos;
    @JoyadataTransient(label = "导出的时候，存放表key数据源id，value  key是tableId、dbName、schemaName")
    private Map<String, Map<String, String>> tableInfos;
    @JoyadataTransient(label = "最新执行状态")
    private String execState;
    @JoyadataTransient(label = "最新执行时间")
    private Date startTime;

    @Override
    public void afterDbInit() {
        //如果是内置目录，产品名称默认成调度中心
        if (null != getIsImport() && !getIsImport()) {
            setProductName("调度中心");
        }
        super.afterDbInit();
    }

    public Task(String id, Integer version) {
        setId(id);
        this.version = version;
    }

    public static class TaskType {
        public static final String SHELL = "SHELL";
        public static final String HIVECLI = "HIVECLI";
        public static final String DEPENDENT = "DEPENDENT";//依赖类型
        public static final String SQL = "SQL";
        public static final String HTTP = "HTTP";
        public static final String STX = "STX";
    }

    public static long conversionUnit(Long timeOut, TimeUnit timeoutUnit) {
        long timeOutSec = timeOut;
        if (null != timeoutUnit) {
            switch (timeoutUnit) {
                case D:
                    timeOutSec = timeOutSec * 60 * 60 * 24;
                    break;
                case H:
                    timeOutSec = timeOutSec * 60 * 60;
                    break;
                case M:
                    timeOutSec = timeOutSec * 60;
                    break;
            }
        }
        return timeOutSec;
    }

    //检查状态（0:失败;1:成功）
    public static class NotRunCheck {
        public static final Integer off = 0;
        public static final Integer on = 1;
    }
}
