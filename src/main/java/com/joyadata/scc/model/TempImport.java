package com.joyadata.scc.model;


import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.TimeUnit;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_import", label = "scc临时导入表", isPublic = true)
public class TempImport extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "导入批次号")
    private String batchNo;
    @JoyadataColumn(label = "导入json数据")
    private String importJson;
    @JoyadataColumn(label = "最终json数据，替换后内容的json")
    private String afterJson;
}
