package com.joyadata.scc.model;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.Priority;
import com.joyadata.scc.enums.TaskExecuteType;
import com.joyadata.scc.enums.TimeUnit;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskLog
 * @date 2023/12/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_task_log", label = "任务log表",
        comment = "用于控制版本", isView = true, viewSql = TaskLog.VIEW_SQL)
public class TaskLog extends BaseBean {
    @JoyadataColumn(label = "任务id")
    private String taskId;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "任务名称")
    private String name;
    @JoyadataColumn(label = "目录id")
    private String catalogId;
    @JoyadataColumn(label = "任务版本", generatorValue = "1")
    private Integer version;
    @JoyadataColumn(label = "描述", length = 256)
    private String description;
    @JoyadataColumn(label = "标签")
    private String title;
    @JoyadataColumn(label = "任务参数（脚本，参数，资源对象）")
    private JSONObject taskParams;
    @JoyadataColumn(label = "任务类型")
    private String taskType;
    @JoyadataColumn(label = "任务优先级", generatorValue = "MEDIUM")
    private String taskPriority;
    @JoyadataColumn(label = "环境code", generatorValue = "-1")
    private String environmentCode;
    @JoyadataColumn(label = "环境名称")
    private String environmentName;
    @JoyadataColumn(label = "资源名称（多个用逗号分割）", length = 512)
    private String resourceNames;
    @JoyadataColumn(label = "资源id（多个用逗号分割）", length = 512)
    private String resourceIds;
    @JoyadataColumn(label = "失败重试次数", generatorValue = "0")
    private int failRetryTimes;
    @JoyadataColumn(label = "失败重试间隔", generatorValue = "1")
    private int failRetryInterval;
    @JoyadataColumn(label = "cpu配额（-1无穷）", generatorValue = "-1")
    private Integer cpuQuota;
    @JoyadataColumn(label = "最大内存", generatorValue = "-1")
    private Integer memoryMax;
    @JoyadataColumn(label = "延迟执行时间", generatorValue = "0")
    private int delayTime;
    @JoyadataColumn(label = "是否是导入进来的", generatorValue = "false")
    private Boolean isImport;
    @JoyadataColumn(label = "超时通知策略")
    private String timeoutNotifyStrategy;
    @JoyadataColumn(label = "任务是否有效: YES/NO")
    private String flag;
    @JoyadataColumn(label = "任务警告超时。单位：分钟")
    private Integer timeout;
    @JoyadataColumn(label = "任务执行类型")
    private String taskExecuteType;
    @JoyadataColumn(label = "超时标志")
    private String timeoutFlag;
    @JoyadataColumn(label = "工作组")
    private String workerGroup;
    @JoyadataColumn(label = "工作流code")
    private String processDefinitionCode;
    @JoyadataColumn(label = "")
    private String upstreamCodes;
    @JoyadataColumn(label = "任务组id")
    private String taskGroupId;
    @JoyadataJoin(label = "目录名称", targetBean = Catalog.class, selfColumn = "catalogId", targetColumn = "id", valueColumn = "name")
    private String catalogName;
    @JoyadataColumn(label = "失败停止策略:stop/continue")
    private String errorStrategy = "stop";
    @JoyadataColumn(label = "组内优先级")
    private int taskGroupPriority;
    @JoyadataColumn(label = "任务超时时间,单位分钟.0为未设置")
    private Long taskTimeout = 0L;
    @JoyadataColumn(label = "1、告警，2、kill，3告警并且kill")
    private Integer taskTimeoutStrategy = 0;
    @JoyadataColumn(label = "任务超时时间单位")
    private TimeUnit timeoutUnit = TimeUnit.M;
    /**
     * 文件路径参数，需要将文件内容放入到任务中
     */
    @JoyadataColumn(label = "文件路径参数", comment = "要读取的文件路径", length = 512)
    private String fileParamsPath;

    public static final String VIEW_SQL = "CREATE VIEW scc_task_log AS SELECT 'dedp' AS `project`,'1' AS dbid,`business_ds`.`t_ds_task_definition_log`.`id` AS `id`,`business_ds`.`t_ds_task_definition_log`.`code` AS `task_id`,'1' AS `is_public`,`business_ds`.`t_ds_task_definition_log`.`name` AS `name`,`business_ds`.`t_ds_task_definition_log`.`project_code` AS `project_id`,`business_ds`.`t_ds_task_definition_log`.`version` AS `version`,`business_ds`.`t_ds_task_definition_log`.`description` AS `description`,`business_ds`.`t_ds_task_definition_log`.`task_params` AS `task_params`,`business_ds`.`t_ds_task_definition_log`.`task_type` AS `task_type`,`business_ds`.`t_ds_task_definition_log`.`task_priority` AS `task_priority`,`business_ds`.`t_ds_task_definition_log`.`environment_code` AS `environment_code`,`business_ds`.`t_ds_task_definition_log`.`resource_ids` AS `resource_ids`,`business_ds`.`t_ds_task_definition_log`.`fail_retry_times` AS `fail_retry_times`,`business_ds`.`t_ds_task_definition_log`.`fail_retry_interval` AS `fail_retry_interval`,`business_ds`.`t_ds_task_definition_log`.`cpu_quota` AS `cpu_quota`,`business_ds`.`t_ds_task_definition_log`.`memory_max` AS `memory_max`,`business_ds`.`t_ds_task_definition_log`.`delay_time` AS `delay_time`,`business_ds`.`t_ds_task_definition_log`.`flag` AS `flag`,`business_ds`.`t_ds_task_definition_log`.`timeout` AS `timeout`,`business_ds`.`t_ds_task_definition_log`.`timeout_notify_strategy` AS `timeout_notify_strategy`,`business_ds`.`t_ds_task_definition_log`.`task_execute_type` AS `task_execute_type`,`business_ds`.`t_ds_task_definition_log`.`timeout_flag` AS `timeout_flag`,`business_ds`.`t_ds_task_definition_log`.`worker_group` AS `worker_group`,`business_ds`.`t_ds_task_definition_log`.`task_group_id` AS `task_group_id`,`business_ds`.`t_ds_task_definition_log`.`task_group_priority` AS `task_group_priority`,`business_ds`.`t_ds_task_definition_log`.`error_strategy` AS `error_strategy`,`business_ds`.`t_ds_task_definition_log`.`create_time` AS `create_time`,`business_ds`.`t_ds_task_definition_log`.`update_time` AS `last_modification_time`,`business_ds`.`t_ds_task_definition_log`.`file_params_path` AS `file_params_path`,`scc_task`.`tenant_code` AS `tenant_code`,`scc_task`.`catalog_id` AS `catalog_id`,`scc_task`.`product_id` AS `product_id`,`scc_task`.`title` AS `title` FROM\t`business_ds`.`t_ds_task_definition_log`\tLEFT JOIN `scc_task` ON `scc_task`.`id` = `business_ds`.`t_ds_task_definition_log`.`code`";


    @Override
    public void afterDbInit() {
        //转换某个值报错不能影响其他值的转换
        try {
            //转换任务优先级
            String taskPriority = getTaskPriority();
            setTaskPriority(Priority.of(Integer.parseInt(taskPriority)).getDesc().toUpperCase());
        } catch (Exception e) {
        }
        try {
            //转换任务是否有效
            setFlag("0".equals(getFlag()) ? "NO" : "YES");
        } catch (Exception e) {
        }
        try {
            //转换任务执行类型
            String taskExecuteType = getTaskExecuteType();
            setTaskExecuteType(TaskExecuteType.of(Integer.parseInt(taskExecuteType)).getDesc().toUpperCase());
        } catch (Exception e) {
        }
    }


}
