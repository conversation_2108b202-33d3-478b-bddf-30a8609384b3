package com.joyadata.scc.model;

import com.alibaba.excel.util.StringUtils;
import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.*;
import com.joyadata.scc.util.DateUtils;
import com.joyadata.scc.util.Utils;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessInstance
 * @Description: TODO
 * @date 2024/4/17
 */
@Data
@JoyadataTable(name = "scc_process_instance", label = "scc_process_instance", comment = "工作流实例")
@EqualsAndHashCode(callSuper = true)
public class ProcessInstance extends BaseBean {

    /**
     * projectId
     */
    @JoyadataColumn(label = "projectId")
    private String projectId;

    /**
     * name
     */
    @JoyadataColumn(label = "name")
    private String name;

    /**
     * processDefinitionId
     */
    @JoyadataColumn(label = "processDefinitionId")
    private String processDefinitionId;

    /**
     * processDefinitionId
     */
    // @JoyadataJoin(label = "工作流名称", targetBean = ProcessDefinition.class, selfColumn = "processDefinitionId", targetColumn = "id", valueColumn = "name")
    @JoyadataColumn(label = "工作流名称")
    private String processDefinitionName;

    /**
     * process definition version
     */
    @JoyadataColumn(label = "processDefinitionVersion")
    private Integer processDefinitionVersion;

    /**
     * process state
     */
    @JoyadataColumn(label = "state", comment = "对应 WorkflowExecutionStatus 枚举状态")
    private String state;

    /**
     * state history
     */
    @JoyadataColumn(label = "stateHistory")
    private String stateHistory;

    /**
     * recovery flag for failover
     */
    @JoyadataColumn(label = "recovery", comment = "对应 com.joyadata.scc.enums.Flag 枚举状态")
    private String recovery;

    /**
     * start time
     */
    @JoyadataColumn(label = "startTime")
    private Date startTime;

    /**
     * end time
     */
    @JoyadataColumn(label = "endTime")
    private Date endTime;

    /**
     * run time
     */
    @JoyadataColumn(label = "runTimes")
    private int runTimes;

    /**
     * host
     */
    @JoyadataColumn(label = "host")
    private String host;

    /**
     * process command type
     */
    @JoyadataColumn(label = "host", comment = "对应 om.joyadata.scc.enums.CommandType 的枚举值")
    private String commandType;

    /**
     * command parameters
     */
    @JoyadataColumn(label = "commandParam")
    private String commandParam;

    /**
     * node depend type
     */
    @JoyadataColumn(label = "commandParam", comment = "com.joyadata.scc.enums.TaskDependType")
    private String taskDependType;

    /**
     * task max try times
     */
    @JoyadataColumn(label = "commandParam")
    private int maxTryTimes;

    /**
     * failure strategy when task failed.
     */
    @JoyadataColumn(label = "commandParam", comment = "对应的枚举值 FailureStrategy")
    private String failureStrategy;

    /**
     * warning type
     */
    @JoyadataColumn(label = "warningType", comment = "对应的枚举值 WarningType")
    private String warningType;

    /**
     * warning group
     */
    @JoyadataColumn(label = "warningGroupId")
    private String warningGroupId;

    /**
     * schedule time
     */
    @JoyadataColumn(label = "scheduleTime")
    private Date scheduleTime;

    /**
     * command start time
     */
    @JoyadataColumn(label = "commandStartTime")
    private Date commandStartTime;

    /**
     * user define parameters string
     */
    @JoyadataColumn(label = "globalParams")
    private String globalParams;

    /**
     * process is sub process
     */
    @JoyadataColumn(label = "isSubProcess", comment = "对应枚举 Flag")
    private String isSubProcess;

    /**
     * executor id
     */
    @JoyadataColumn(label = "executorId")
    private String executorId;


    /**
     * history command
     */
    @JoyadataColumn(label = "historyCmd")
    private String historyCmd;

    /**
     * process instance priority
     */
    @JoyadataColumn(label = "processInstancePriority", comment = "对应枚举 Priority")
    private String processInstancePriority;

    /**
     * worker group
     */
    @JoyadataColumn(label = "workerGroup")
    private String workerGroup;

    /**
     * environment code
     */
    @JoyadataColumn(label = "environmentCode")
    private Long environmentCode;

    /**
     * process timeout for warning
     */
    @JoyadataColumn(label = "timeout")
    private int timeout;

    /**
     * tenant id
     */
    @JoyadataColumn(label = "tenantId")
    private String tenantId;

    /**
     * varPool string
     */
    @JoyadataColumn(label = "varPool")
    private String varPool;

    /**
     * dry run flag
     */
    @JoyadataColumn(label = "dryRun")
    private int dryRun;


    /**
     * serial queue next processInstanceId
     */
    @JoyadataColumn(label = "nextProcessInstanceId")
    private String nextProcessInstanceId;

    /**
     * re-start time
     */
    @JoyadataColumn(label = "nextProcessInstanceId")
    private Date restartTime;

    /**
     * test flag
     */
    @JoyadataColumn(label = "testFlag")
    private int testFlag;

    @JoyadataColumn(label = "testFlag")
    private String calendarId;

    // @JoyadataAggJoin(label = "任务名称", selfColumn = "task_ids", targetBean = Task.class, targetColumn = "id", agg = AGG.GROUP_CONCAT, valueColumn = "name", conditionType = ConditionType.SETIN)
    @JoyadataColumn(label = "任务名称")
    private String taskNames;

    @JoyadataColumn(label = "产品id")
    private String productId;

    @JoyadataColumn(label = "任务ids")
    private String taskIds;

    @JoyadataColumn(label = "目录id", length = 512)
    private String catalogId;

    @JoyadataColumn(label = "目录parentIds", length = 512)
    private String catalogParentIds;

    @JoyadataTransient
    private String projectCode;
    @JoyadataTransient
    private String taskRelationJson;
    @JoyadataTransient
    private String taskDefinitionJson;
    @JoyadataTransient
    private String syncDefine;
    @JoyadataTransient
    private String locations;
    @JoyadataTransient
    private Integer taskNum;
    /**
     * 运行时长
     */
    @JoyadataTransient
    private String duration;

    @JoyadataTransient(label = "执行人")
    private String executorName;

    @JoyadataColumn(label = "会计日期")
    private String acDate;


    @Override
    public void afterDbInit() {
        //将枚举值转换成字符串
        afterDbInitState();
        afterDbInitRecovery();
        afterDbInitCommandType();
        afterDbInitTaskDependType();
        afterDbInitFailureStrategy();
        afterDbInitIsSubProcess();
        afterDbInitProcessInstancePriority();
        afterDbInitWarningType();

        //计算运行时长
        if (null != startTime && null != endTime && endTime.getTime() - startTime.getTime() < 0) {
            //如果结束时间小于开始时间，说明任务运行中，应取当前时间减去开始时间
            setDuration(DateUtils.format2Duration(getStartTime(), new Date()));
            setEndTime(null);
        } else {
            setDuration(DateUtils.format2Duration(getStartTime(), getEndTime()));
        }
        //将任务名称的目录id后缀去掉
        String name = getName();
        if (StringUtils.isNotBlank(name)) {
            if (name.contains("__[") && name.contains("_copy")) {
                //如果是复制的工作流，要截取中间的一节
                setName(Utils.removeBetween(name, "__", "_copy"));
            } else if (name.contains("__[")) {
                setName(name.substring(0, name.lastIndexOf("__[")));
            }
        }
    }

    private void afterDbInitState() {
        try {
            setState(WorkflowExecutionStatus.of(Integer.parseInt(getState())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitRecovery() {
        try {
            setRecovery(Flag.of(Integer.parseInt(getRecovery())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitCommandType() {
        try {
            setCommandType(CommandType.of(Integer.parseInt(getCommandType())).toString());
        } catch (Exception e) {

        }
    }

    private void afterDbInitTaskDependType() {
        try {
            setTaskDependType(TaskDependType.of(Integer.parseInt(getTaskDependType())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitFailureStrategy() {
        try {
            setFailureStrategy(FailureStrategy.of(Integer.parseInt(getFailureStrategy())).name());
        } catch (Exception e) {

        }
    }


    private void afterDbInitIsSubProcess() {
        try {
            setIsSubProcess(Flag.of(Integer.parseInt(getIsSubProcess())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitProcessInstancePriority() {
        try {
            setProcessInstancePriority(Priority.of(Integer.parseInt(getProcessInstancePriority())).name());
        } catch (Exception e) {

        }
    }

    private void afterDbInitWarningType() {
        try {
            setWarningType(WarningType.of(Integer.parseInt(getWarningType())).name());
        } catch (Exception e) {

        }
    }

    // public final static String VIEW_SQL = "CREATE VIEW scc_process_instance AS select `business_ds`.`t_ds_process_instance`.`id` AS `id`,`business_ds`.`t_ds_process_instance`.`name` AS `name`,`business_ds`.`t_ds_process_instance`.`process_definition_code` AS `process_definition_id`,`business_ds`.`t_ds_process_instance`.`process_definition_version` AS `process_definition_version`,`business_ds`.`t_ds_process_instance`.`state` AS `state`,`business_ds`.`t_ds_process_instance`.`state_history` AS `state_history`,`business_ds`.`t_ds_process_instance`.`recovery` AS `recovery`,`business_ds`.`t_ds_process_instance`.`start_time` AS `start_time`,`business_ds`.`t_ds_process_instance`.`end_time` AS `end_time`,`business_ds`.`t_ds_process_instance`.`run_times` AS `run_times`,`business_ds`.`t_ds_process_instance`.`host` AS `host`,`business_ds`.`t_ds_process_instance`.`command_type` AS `command_type`,`business_ds`.`t_ds_process_instance`.`command_param` AS `command_param`,`business_ds`.`t_ds_process_instance`.`task_depend_type` AS `task_depend_type`,`business_ds`.`t_ds_process_instance`.`max_try_times` AS `max_try_times`,`business_ds`.`t_ds_process_instance`.`failure_strategy` AS `failure_strategy`,`business_ds`.`t_ds_process_instance`.`warning_type` AS `warning_type`,`business_ds`.`t_ds_process_instance`.`warning_group_id` AS `warning_group_id`,`business_ds`.`t_ds_process_instance`.`schedule_time` AS `schedule_time`,`business_ds`.`t_ds_process_instance`.`command_start_time` AS `command_start_time`,`business_ds`.`t_ds_process_instance`.`global_params` AS `global_params`,`business_ds`.`t_ds_process_instance`.`is_sub_process` AS `is_sub_process`,`business_ds`.`t_ds_process_instance`.`executor_id` AS `executor_id`,`business_ds`.`t_ds_process_instance`.`history_cmd` AS `history_cmd`,`business_ds`.`t_ds_process_instance`.`process_instance_priority` AS `process_instance_priority`,`business_ds`.`t_ds_process_instance`.`worker_group` AS `worker_group`,`business_ds`.`t_ds_process_instance`.`environment_code` AS `environment_code`,`business_ds`.`t_ds_process_instance`.`timeout` AS `timeout`,`business_ds`.`t_ds_process_instance`.`tenant_id` AS `tenant_id`,`business_ds`.`t_ds_process_instance`.`var_pool` AS `var_pool`,`business_ds`.`t_ds_process_instance`.`dry_run` AS `dry_run`,`business_ds`.`t_ds_process_instance`.`next_process_instance_id` AS `next_process_instance_id`,`business_ds`.`t_ds_process_instance`.`restart_time` AS `restart_time`,`business_ds`.`t_ds_process_instance`.`test_flag` AS `test_flag`,`business_ds`.`t_ds_process_instance`.`calendar_id` AS `calendar_id`,`business_ds`.`t_ds_process_instance`.`start_time` AS `create_time`,`business_ds`.`t_ds_process_instance`.`update_time` AS `last_modification_time`,`business_ds`.`t_ds_process_instance`.`catalog_id` AS `catalog_id`,`business_ds`.`t_ds_process_instance`.`catalog_parent_ids` AS `catalog_parent_ids`,`scc_process_definition`.`tenant_code` AS `tenant_code`,`scc_process_definition`.`create_by` AS `create_by`,`scc_process_definition`.`create_by_name` AS `create_by_name`,`scc_process_definition`.`is_public` AS `is_public`,`scc_process_definition`.`pos` AS `pos`,`scc_process_definition`.`dbid` AS `dbid`,`scc_process_definition`.`update_by` AS `update_by`,`scc_process_definition`.`del_flag` AS `del_flag`,`scc_process_definition`.`project` AS `project`,`scc_process_definition`.`data_owner_dept_id` AS `data_owner_dept_id`,`scc_process_definition`.`data_owner_dept_name` AS `data_owner_dept_name`,`scc_process_definition`.`data_owner_user_id` AS `data_owner_user_id`,`scc_process_definition`.`data_owner_user_name` AS `data_owner_user_name`,`scc_process_definition`.`readonly` AS `readonly`,`scc_process_definition`.`project_id` AS `project_id`,`scc_process_definition`.`product_id` AS `product_id`,`scc_process_definition`.`task_ids` AS `task_ids` from (`business_ds`.`t_ds_process_instance` left join `scc_process_definition` on((`business_ds`.`t_ds_process_instance`.`process_definition_code` = `scc_process_definition`.`id`))) where (`scc_process_definition`.`product_id` is not null) group by `business_ds`.`t_ds_process_instance`.`id`";
}