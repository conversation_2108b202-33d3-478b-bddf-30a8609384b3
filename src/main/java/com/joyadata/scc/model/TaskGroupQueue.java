package com.joyadata.scc.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/24 12:39.
 */
@Data
@JoyadataTable(name = "scc_task_group_queue", label = "scc_task_group_queue", comment = "任务组队列")
@EqualsAndHashCode(callSuper = true)
public class TaskGroupQueue extends BaseBean {
    /**
     * taskInstanceId
     */
    @JoyadataColumn(label = "任务实例id")
    private String taskInstanceId;
    /**
     * TaskInstance name
     */
    @JoyadataColumn(label = "任务实例名称")
    private String taskInstanceName;
    /**
     * project name
     */
    @JoyadataColumn(label = "项目名称")
    private String projectName;
    /**
     * project code
     */
    @JoyadataColumn(label = "项目id")
    private String projectId;
    /**
     * process instance name
     */
    @JoyadataJoin(label = "工作流实例名称", targetBean = ProcessInstance.class, selfColumn = "processId", targetColumn = "id", valueColumn = "name")
    private String processInstanceName;
    /**
     * taskGroup id
     */
    @JoyadataColumn(label = "工作组id")
    private String taskGroupId;
    /**
     * processInstance id
     */
    @JoyadataColumn(label = "工作流实例id")
    private String processInstanceId;
    /**
     * the priority of task instance
     */
    @JoyadataColumn(label = "优先级")
    private int priority;
    /**
     * 强制启动
     * is force start
     * 0 NO ,1 YES
     */
    @JoyadataColumn(label = "是否是强制启动")
    private int forceStart;
    /**
     * ready to get the queue by other task finish
     * 0 NO ,1 YES
     */
    @JoyadataColumn(label = "inQueue", comment = "准备好排队由其他任务完成")
    private int inQueue;
    /**
     * -1: waiting  1: running  2: finished
     */
    @JoyadataColumn(label = "状态")
    private int status;

}
