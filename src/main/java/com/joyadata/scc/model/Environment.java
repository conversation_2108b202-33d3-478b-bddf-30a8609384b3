package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/24 21:21.
 */
@Data
@JoyadataTable(name = "scc_environment", label = "scc_environment", comment = "环境管理", isTenant = false, isView = true, viewSql = Environment.VIEW_SQL)
@EqualsAndHashCode(callSuper = true)
public class Environment extends BaseBean {
    /**
     * environment code
     */
    @JoyadataColumn(label = "environment code")
    private Long code;
    @JoyadataColumn(label = "项目id")
    private String projectId;

    /**
     * environment name
     */
    @JoyadataColumn(label = "environment name")
    private String name;

    /**
     * config content
     */
    @JoyadataColumn(label = "config")
    private String config;
    @JoyadataColumn(label = "说明")
    private String description;
    /**
     * operator user id
     */
    @JoyadataColumn(label = "operator")
    private String operator;
    @JoyadataColumn(label = "workers")
    private String[] workers;
    public final static String VIEW_SQL = "CREATE VIEW scc_environment AS SELECT'1' AS dbid,'dedp' AS project,'1' AS is_public,environment.id AS id,environment.`name` AS `name`,environment.`code` AS `code`,environment.`create_time` AS create_time,environment.`update_time` AS update_time,environment.`description` AS description,environment.`config` AS config,environment.`operator` AS operator,(select concat('[',group_concat('\"',`environment_worker_group_relation`.`worker_group`,'\"' separator ','),']') AS `ConcatenatedValues` from `business_ds`.`t_ds_environment_worker_group_relation` `environment_worker_group_relation` where (`environment_worker_group_relation`.`environment_code` = `environment`.`code`)) AS `workers`  FROM`business_ds`.`t_ds_environment` environment";

}
