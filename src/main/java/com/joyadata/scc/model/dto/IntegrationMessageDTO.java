package com.joyadata.scc.model.dto;

import java.util.List;
import java.util.Map;

/**
 * 数据集成dto
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
public class IntegrationMessageDTO {
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 租户code
     */
    private String tenantCode;
    /**
     * 工作流包含任务集合
     * map key insert、update、delete
     * map value 任务ids
     */
    private Map<String, List<String>> taskIds;
    /**
     * 工作流定义名称
     */
    private String processDefinitionName;

    /**
     * 工作流上下线状态
     */
    private String releaseState;

    /**
     * 工作流id
     */
    private String processDefinitionId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Map<String, List<String>> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(Map<String, List<String>> taskIds) {
        this.taskIds = taskIds;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public String getReleaseState() {
        return releaseState;
    }

    public void setReleaseState(String releaseState) {
        this.releaseState = releaseState;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }
}
