package com.joyadata.scc.model.dto;

import lombok.Data;
import org.json.JSONObject;
import org.json.JSONString;

import java.util.List;

/**
 * 运行参数
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/8.
 */
@Data
public class StartParam {
    //项目code
    private String projectId;
    //工作流定义code
    private String processDefinitionCode;
    //工作流定义名称
    private String processDefinitionName;
    //失败策略 CONTINUE 继续、END结束
    private String failureStrategy = "CONTINUE";
    //通知策略 NONE 都不发、SUCCESS成功发、FAILURE失败发、ALL成功失败都发
    private String warningType;
    //
    private Integer warningGroupId = 0;
    /**
     * command types
     * START_PROCESS 0 start a new process
     * START_CURRENT_TASK_PROCESS 1 start a new process from current nodes
     * RECOVER_TOLERANCE_FAULT_PROCESS 2 recover tolerance fault process
     * RECOVER_SUSPENDED_PROCESS 3 recover suspended process
     * START_FAILURE_TASK_PROCESS 4 start process from failure task nodes
     * COMPLEMENT_DATA 5 complement data
     * SCHEDULER 6 start a new process from scheduler
     * REPEAT_RUNNING 7 repeat running a process
     * PAUSE 8 pause a process
     * STOP 9 stop a process
     * RECOVER_WAITING_THREAD 10 recover waiting thread
     * RECOVER_SERIAL_WAIT  11  recover serial wait
     */
    private String execType;
    private String startNodeList;
    /**
     * 任务节点依赖类型
     * TASK_ONLY  run current tasks only(仅运行当前任务)
     * TASK_PRE  run current tasks and previous tasks(运行当前任务和以前的任务)
     * TASK_POST  run current tasks and the other tasks that depend on current tasks(运行当前任务和依赖于当前任务的其他任务)
     */
    private String taskDependType;
    /**
     * OFF_MODE(off mode)
     * ALL_DEPENDENT(用所有相关进程运行补码数据 run complement data with all dependent process)
     */
    private String complementDependentMode;
    /**
     * RUN_MODE_SERIAL 串行
     * RUN_MODE_PARALLEL 并行
     */
    private String runMode;
    /**
     * 定义流程和任务优先级
     * HIGHEST
     * HIGH
     * MEDIUM
     * LOW
     * LOWEST
     */
    private String processInstancePriority = "MEDIUM";
    //
    private String workerGroup = "default";
    //环境code
    private Long environmentCode;
    // 启动参数
    private String startParams;
    //
    private String expectedParallelismNumber;


    //是否空跑
    private int dryRun = 0;
    //是否测试，0正常运行、1测试
    private int testFlag;
    //调度时间
    private String scheduleTime;

    private List<String> startParamsList;

}
