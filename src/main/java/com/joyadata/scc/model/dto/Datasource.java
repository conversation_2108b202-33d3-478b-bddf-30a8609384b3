package com.joyadata.scc.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: Datasource
 * @date 2023/11/3
 */
@Data
public class Datasource {
    private String id;
    private String dataName;
    private String dataType;
    private String dbName;
    private String dataJson;
    private String dataDesc;

    public Datasource(String id, String dataName, String dataType, String dbName, String dataJson, String dataDesc) {
        this.id = id;
        this.dataName = dataName;
        this.dataType = dataType;
        this.dbName = dbName;
        this.dataJson = dataJson;
        this.dataDesc = dataDesc;
    }
}
