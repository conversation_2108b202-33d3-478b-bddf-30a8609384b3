package com.joyadata.scc.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: WorkFlowRelation
 * @date 2023/12/26
 */
@Data
public class WorkFlowLineage {
    private long workFlowCode;
    private String workFlowName;
    private String workFlowPublishStatus;
    private Date scheduleStartTime;
    private Date scheduleEndTime;
    private String crontab;
    private int schedulePublishStatus;
    private String sourceWorkFlowCode;
    private Integer isSearch;//是否是搜索的工作流
}
