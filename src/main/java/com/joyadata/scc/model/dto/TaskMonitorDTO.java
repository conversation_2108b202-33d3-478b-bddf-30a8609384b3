package com.joyadata.scc.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import lombok.Data;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/7 16:41.
 */
@Data
public class TaskMonitorDTO {
    private Double cpuUsage = 0.0;

    private Double memoryUsage = 0.0;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
