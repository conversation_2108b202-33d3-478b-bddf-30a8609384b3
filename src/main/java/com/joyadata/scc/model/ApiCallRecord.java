package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/7 14:01.
 */
@Data
@JoyadataTable(name = "scc_api_call_record", label = "scc_api_call_record", comment = "外部调用启动工作流的记录", isPublic = true)
@EqualsAndHashCode(callSuper = true)
public class ApiCallRecord extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "项目名称")
    private String projectName;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "工作流name")
    private String processDefinitionName;
    @JoyadataColumn(label = "工作流实例id")
    private String processInstanceId;
    @JoyadataColumn(label = "调用时间",comment = "同时当做位点使用：浙江农信使用的，相当于增量任务的位点")
    private String callTime;//使用字符串，因为使用date类型时，对应mysql的datetime，往数据库新增后会出现因为毫秒值导致的数据库时间多一秒的问题
    @JoyadataColumn(label = "调用结果")
    private String callResult;
    @JoyadataColumn(label = "调用服务器的主机ip")
    private String callIp;
    @JoyadataColumn(label = "调用用户id")
    private String callUserId;
    @JoyadataColumn(label = "调用用户名称")
    private String callUserName;
    @JoyadataColumn(label = "调用启动参数")
    private String startParams;
    @JoyadataColumn(label = "appId",comment = "记录appId，用于获取推送状态的接口url，url在我的应用中的“访问地址”中")
    private String appId;
}
