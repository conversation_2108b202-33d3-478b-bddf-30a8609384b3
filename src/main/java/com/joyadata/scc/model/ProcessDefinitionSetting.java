package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinitionSetting
 * @date 2024/9/19
 */
@Data
@JoyadataTable(name = "scc_process_definition_setting", label = "scc_process_definition_setting", comment = "工作流定义设置")
@EqualsAndHashCode(callSuper = true)
public class ProcessDefinitionSetting extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "执行策略")
    private String executionType = "SERIAL_DISCARD";
    @JoyadataColumn(label = "END结束、CONTINUE继续")
    private String failureStrategy = "CONTINUE";
    @JoyadataColumn(label = "工作流优先级")
    private String processInstancePriority = "MEDIUM";
    @JoyadataColumn(label = "工作组")
    private String workerGroup = "default";
    @JoyadataColumn(label = "环境id")
    private String environmentCode = "-1";
    @JoyadataColumn(label = "工作流连续失败次数")
    private int failedCount;
}
