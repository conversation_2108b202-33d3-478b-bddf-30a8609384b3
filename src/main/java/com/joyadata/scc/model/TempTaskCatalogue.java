package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_task_catalogue", label = "任务统计表", isPublic = true)
public class TempTaskCatalogue extends BaseBean {
    /**
     * 导入批次号
     */
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    /**
     * 唯一标识
     */
    @JoyadataColumn(label = "唯一标识")
    private String catalogueId;
    /**
     * 父目录ID 根目录为0
     */
    @JoyadataColumn(label = "父目录ID")
    private String parentId;
    /**
     * 父级层级
     */
    @JoyadataColumn(label = "父级层级")
    private String parentLevel;
    /**
     * 分类名称
     */
    @JoyadataColumn(label = "分类名称")
    private String name;
    /**
     * 任务id
     */
    @JoyadataColumn(label = "任务id")
    private String taskId;
    /**
     * 运行类型（针对taskType为离线同步）：0-周期任务，1-手动任务
     */
    @JoyadataColumn(label = "运行类型")
    private int runType;
    /**
     * 任务类型：0-离线同步（默认选中），1-CDC同步
     */
    @JoyadataColumn(label = "任务类型")
    private Integer taskType;
    /**
     * 任务状态
     */
    @JoyadataColumn(label = "任务状态")
    private String taskStatus;
    /**
     * 分类级别
     */
    @JoyadataColumn(label = "分类级别")
    private int level;
    /**
     * 分类简称
     */
    @JoyadataColumn(label = "分类简称")
    private String simpleName;
    /**
     * 菜单选项
     */
    @JoyadataColumn(label = "菜单选项")
    private int optionsId;
    /**
     * 是否为文件夹
     */
    @JoyadataColumn(label = "是否为文件夹")
    private int isLeaf;

    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    @JoyadataColumn(label = "备注")
    private String remark;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
