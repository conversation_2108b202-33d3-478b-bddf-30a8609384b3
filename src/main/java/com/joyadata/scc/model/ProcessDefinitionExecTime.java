package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@JoyadataTable(name = "scc_process_definition_exec_time", label = "scc_process_definition_exec_time", comment = "工作流执行时间")
@JoyadataIndex(type = "UNIQUE INDEX", name = "process_definition_id_exec_time", columns = {"process_definition_id", "exec_time"}, comment = "工作流执行时间不能重复", errorMsg = "工作流执行时间不能重复！")
@JoyadataIndex(type = "INDEX", name = "process_definition_id", columns = {"process_definition_id"}, comment = "process_definition_id")
@EqualsAndHashCode(callSuper = true)
public class ProcessDefinitionExecTime extends BaseBean {
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "执行时间")
    private Date execTime;
    @JoyadataColumn(label = "日历id")
    private String calendarId;

    //日历状态:0:未提交,1:已提交,2:已撤回
    public static class CalendarStatus {
        public static final Integer isRevoke = 2;
        public static final Integer isSubmit = 1;
        public static final Integer noSubmit = 0;
    }
}
