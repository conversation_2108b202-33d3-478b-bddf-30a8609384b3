package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/2 14:29.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_task_monitor", label = "任务监控表",
        comment = "存储任务运行时对资源的占用情况", isView = true, viewSql = TaskMonitor.VIEW_SQL)
public class TaskMonitor extends BaseBean {
    @JoyadataColumn(label = "任务实例id")
    private String taskInstanceId;
    /**
     * cpu使用百分比
     */
    @JoyadataColumn(label = "cpu使用百分比")
    private Double cpuUsage = 0.0;

    /**
     * cpu核心数
     */
    @JoyadataColumn(label = "cpu核心数")
    private Integer totalCpus;
    /**
     * 已使用内存，Kb单位
     */
    @JoyadataColumn(label = "已使用内存，Kb单位")
    private Long memoryUsage = 0L;
    /**
     * 总内存，Kb单位
     */
    @JoyadataColumn(label = "内存总量，Kb单位")
    private Long totalMemory;

    public static final String VIEW_SQL = "CREATE VIEW scc_task_monitor AS select 'dedp' AS `project`,`business_ds`.`t_ds_task_monitor`.`id` AS `dbid`,`business_ds`.`t_ds_task_monitor`.`id` AS `id`,`business_ds`.`t_ds_task_monitor`.`task_instance_id` AS `task_instance_id`,'1' AS `is_public`,`business_ds`.`t_ds_task_monitor`.`cpu_usage` AS `cpu_usage`,`business_ds`.`t_ds_task_monitor`.`total_cpus` AS `total_cpus`,`business_ds`.`t_ds_task_monitor`.`memory_usage` AS `memory_usage`,`business_ds`.`t_ds_task_monitor`.`total_memory` AS `total_memory`,`business_ds`.`t_ds_task_monitor`.`create_time` AS `create_time`,`business_ds`.`t_ds_task_monitor`.`create_time` AS `last_modification_time` from `business_ds`.`t_ds_task_monitor`";

}
