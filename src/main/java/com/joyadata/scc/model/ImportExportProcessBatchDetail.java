package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/8/27 13:42
 */
@Data
@JoyadataTable(name = "scc_import_export_process_batch_detail", label = "scc_import_export_process_batch_detail", comment = "导入导出批次详情")
public class ImportExportProcessBatchDetail extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "工作流code")
    private String processDefinitionCode;
    @JoyadataColumn(label = "导入前json")
    private String beforeJson;

}
