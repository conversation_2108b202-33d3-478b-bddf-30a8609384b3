package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataAggJoin;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import com.joyadata.util.sql.enums.AGG;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskType
 * @date 2023/11/7
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_task_type", label = "任务类型表" , isPublic = true, isTenant = false)
public class TaskType extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "类型名字")
    private String name;
    @JoyadataColumn(label = "图标")
    private String icon;
    @JoyadataColumn(label = "是否支持")
    private Boolean isSupport;
//    @JoyadataAggJoin(label = "关联任务数量", targetBean = Task.class, selfColumn = "name", targetColumn = "taskType",
//            valueColumn = "id", agg = AGG.COUNT)
    @JoyadataTransient(label = "关联任务数量")
    private Integer taskCount;
}
