//package com.joyadata.scc.model;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.joyadata.model.BaseBean;
//import com.joyadata.util.sql.annotation.JoyadataColumn;
//import com.joyadata.util.sql.annotation.JoyadataIndex;
//import com.joyadata.util.sql.annotation.JoyadataTable;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import org.quartz.CronExpression;
//
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//@Data
//@EqualsAndHashCode(callSuper = true)
//@JoyadataTable(name = "scc_scheduler", label = "定时表", isPublic = true)
//@JoyadataIndex(type = "UNIQUE INDEX", name = "processDefinitionCode", columns = "processDefinitionCode", comment = "一条工作流只能有一个定时", errorMsg = "定时表中已有此工作流的数据，不可重复添加！")
//
//public class ProcessScheduler extends BaseBean {
//    @JoyadataColumn(label = "定时策略名称")
//    private String name;
//    @JoyadataColumn(label = "项目id")
//    private String projectId;
//    @JoyadataColumn(label = "工作流id")
//    private String processDefinitionCode;
//    @JoyadataColumn(label = "工作日")
//    private JSONObject workdate;
//    @JoyadataColumn(label = "执行计划")
//    private JSONArray execplans;
//
//    @Data
//    static class WorkDate {
//        // 1: 永久 、 2、 指定范围 ， 3 指定工作日历
//        Integer type;//: 1 |  2 |  3
//        String startDate;//："2025-01-01"
//        String endDate;//:"2025-12-31",
//        String calendarId;//:"34589123975323274",
//        String calendarName;//:"2025年3季度工作日"
//        JSONArray calendars;
//    }
//
//    @Data
//    static class Calendar {
//        // 年份
//        private String year;
//        // 月份
//        private String month;
//        // 节假日
//        private JSONArray holidayData;
//        // 调休日
//        private JSONArray leaveData;
//        // 工作日
//        private JSONArray selectData;
//
//    }
//
//    @Data
//    static class DayRange {
//        // 开始时间
//        String startTime;//："2025-01-01"
//        // 结束时间
//        String endTime;//:"2025-12-31",
//    }
//
//    @Data
//    static class DayHour {
//        // 1、 每小时 、 2、每个多少小时、 指定小时
//        Integer type;
//        String value;
//    }
//
//    @Data
//    static class DayMinute {
//        // 1、 每分钟  、 2、每多少分钟 、 指定分钟
//        Integer type;
//        String value;
//    }
//
//    @Data
//    static class DaySecond {
//        // 1、 每秒  、 2、每隔开多少秒 、 指定秒
//        Integer type;
//        String value;
//    }
//
//    /**
//     * 生成 Cron 表达式
//     *
//     * @param workDate  工作日配置
//     * @param dayRange  时间范围
//     * @param dayHour   小时配置
//     * @param dayMinute 分钟配置
//     * @param daySecond 秒配置
//     * @return Cron 表达式
//     */
//    public static String generateCronExpression(WorkDate workDate, DayRange dayRange, DayHour dayHour, DayMinute dayMinute, DaySecond daySecond) {
//        StringBuilder cronExpression = new StringBuilder();
//
//        // 处理秒
//        if (daySecond.getType() == 1) {
//            cronExpression.append("*"); // 每秒
//        } else if (daySecond.getType() == 2) {
//            cronExpression.append("*/").append(daySecond.getValue()); // 每隔多少秒
//        } else if (daySecond.getType() == 3) {
//            cronExpression.append(daySecond.getValue()); // 指定秒
//        }
//
//        // 处理分钟
//        cronExpression.append(" ");
//       if(dayMinute.getType() == 1) {
//            cronExpression.append("*"); // 每分钟
//        } else if (dayMinute.getType() == 2) {
//            cronExpression.append("*/").append(dayMinute.getValue()); // 每隔多少分钟
//        } else if (dayMinute.getType() == 3) {
//            cronExpression.append(dayMinute.getValue()); // 指定分钟
//        }
//
//        // 处理小时
//        cronExpression.append(" ");
//        if (dayRange != null && dayRange.getStartTime() != null && dayRange.getEndTime() != null) {
//            // 解析开始时间和结束时间的小时部分
//            int startHour = Integer.parseInt(dayRange.getStartTime());
//            int endHour = Integer.parseInt(dayRange.getEndTime());
//            cronExpression.append(startHour).append("-").append(endHour); // 例如 9-11
//        } else if (dayHour.getType() == 1) {
//            cronExpression.append("*"); // 每小时
//        } else if (dayHour.getType() == 2) {
//            cronExpression.append("*/").append(dayHour.getValue()); // 每隔多少小时
//        } else if (dayHour.getType() == 3) {
//            cronExpression.append(dayHour.getValue()); // 指定小时
//        }
//
//        // 处理日期范围
//        cronExpression.append(" ");
//        if (workDate.getType() == 1) {
//            cronExpression.append("*"); // 永久，每天
//        } else if (workDate.getType() == 2) {
//            // 指定日期范围，格式为 startDate-endDate
//            cronExpression.append(workDate.getStartDate()).append("-").append(workDate.getEndDate());
//        } else if (workDate.getType() == 3) {
//            // 指定工作日历，这里假设使用日历ID
//            cronExpression.append("?"); // 通常使用 ? 表示不指定
//        }
//
//        // 处理月份
//        cronExpression.append(" *"); // 每个月
//
//        // 处理周
//        cronExpression.append(" ?"); // 不指定周
//
//        // 处理年（可选）
//        cronExpression.append(" *"); // 每年
//
//        return cronExpression.toString();
//    }
//
//    /**
//     * 生成指定 Cron 表达式从某个时间开始，未来运行指定次数的具体时间值
//     *
//     * @param cronExpression Cron 表达式
//     * @param startTime      开始时间
//     * @param runCount       运行次数
//     * @return 未来运行的具体时间列表
//     */
//    public static List<String> getNextExecutionTimes(String cronExpression, Date startTime, int runCount) {
//        List<String> executionTimes = new ArrayList<>();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        try {
//            // 解析 Cron 表达式
//            CronExpression cron = new CronExpression(cronExpression);
//            // 当前时间
//            Date currentTime = startTime;
//            // 计算未来运行时间
//            int count = 0;
//            while (count < runCount) {
//                Date nextTime = cron.getNextValidTimeAfter(currentTime);
//                if (nextTime == null) {
//                    break; // 如果没有下一次触发时间，退出循环
//                }
//                executionTimes.add(sdf.format(nextTime));
//                count++;
//                // 更新当前时间
//                currentTime = nextTime;
//            }
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        return executionTimes;
//    }
//
//
//
//    public static void main(String[] args) throws ParseException {
//        // 场景: 永久运行， 每个1个小时运行一次  永久， 每个小时运行于此
//        WorkDate date = new WorkDate();
//        date.setType(2);
//        date.setStartDate("2023-01-01");
//        date.setEndDate("2023-01-02");
//        // 指定时间范围 09:00:00 - 11:00:00
//        DayRange dayRange = new DayRange();
//        dayRange.setStartTime("09");
//        dayRange.setEndTime("11");
//        // 每小时
//        DayHour dayHour = new DayHour();
//        dayHour.setType(1);
//        // 指定00 分
//        DayMinute dayMinute = new DayMinute();
//        dayMinute.setType(3);
//        dayMinute.setValue("0");
//        // 指定0秒
//        DaySecond daySecond = new DaySecond();
//        daySecond.setType(3);
//        daySecond.setValue("0");
//        // 生成 Cron 表达式
//        String cronExpression = generateCronExpression(date, dayRange, dayHour, dayMinute, daySecond);
//        System.out.println("Generated Cron Expression: " + cronExpression);
//        // 开始时间
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Date startTime = sdf.parse("2023-10-01 00:00:00");
//        // 运行次数
//        int runCount = 20;
//        // 获取未来运行时间
//        List<String> executionTimes = getNextExecutionTimes(cronExpression, startTime, runCount);
//        // 输出结果
//        System.out.println("Cron Expression: " + cronExpression);
//        System.out.println("Start Time: " + sdf.format(startTime));
//        System.out.println("Next " + runCount + " Execution Times:");
//        for (String time : executionTimes) {
//            System.out.println(time);
//        }
//    }
//}
//
//
//
