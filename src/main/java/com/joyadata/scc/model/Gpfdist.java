package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/8/12 14:33
 */
@Data
@JoyadataTable(name = "scc_gpfdist", label = "scc_gpfdist", comment = "gpfdist管理")
@JoyadataIndex(type = "UNIQUE INDEX",
        name = "uk_scc_gpfdist_gpfdistAddr",
        columns = "project_id,tenant_code,gpfdistAddr",
        comment = "gpfdist地址不可重复", errorMsg = "同一个项目下，gpfdist地址不可重复！")
@EqualsAndHashCode(callSuper = true)
public class Gpfdist extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "gpfdist地址", length = 256)
    private String gpfdistAddr;
    @JoyadataColumn(label = "gpfdist路径", length = 256)
    private String gpfdistPath;
}
