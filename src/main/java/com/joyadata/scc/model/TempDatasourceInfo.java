package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_datasource_info", label = "数据源临时表", isPublic = true)
public class TempDatasourceInfo extends BaseBean {

    @JoyadataColumn(label = "批次号")
    private String batchNo;

    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;
    /**
     * 外键：数据源分类目录id
     */
    @JoyadataColumn(label = "外键：数据源分类目录id")
    private int catalogueId;
    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    @JoyadataColumn(label = "数据源类型唯一 如Mysql, Oracle, Hive")
    private String dataType;
    /**
     * 数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号
     */
    @JoyadataColumn(label = "数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号")
    private String dataVersion;
    /**
     * 数据源名称
     */
    @JoyadataColumn(label = "数据源名称")
    private String dataName;
    /**
     * 数据源描述
     */
    @JoyadataColumn(label = "数据源描述")
    private String dataDesc;
    /**
     * 数据源连接信息, 不同数据源展示连接信息不同, 保存为json
     */
    @JoyadataColumn(label = "数据源连接信息, 不同数据源展示连接信息不同, 保存为json")
    private String linkJson;
    /**
     * 数据源填写的表单信息, 保存为json, key键要与表单的name相同
     */
    @JoyadataColumn(label = "数据源填写的表单信息, 保存为json, key键要与表单的name相同")
    private String dataJson;
    /**
     * 连接状态 0-连接失败, 1-正常
     */
    @JoyadataColumn(label = "连接状态 0-连接失败, 1-正常")
    private int status;
    /**
     * 是否有meta标志 0-否 1-是
     */
    @JoyadataColumn(label = "是否有meta标志 0-否 1-是")
    private int isMeta;
    /**
     * 数据源类型编码
     */
    @JoyadataColumn(label = "数据源类型编码")
    private int dataTypeCode;
    /**
     * 数据库名称
     */
    @JoyadataColumn(label = "数据库名称")
    private String dbName;
    /**
     * 是否删除,1删除，0未删除
     */
    @JoyadataColumn(label = "是否删除,1删除，0未删除")
    private int isDeleted;
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    /**
     * 创建者用户Id
     */
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    /**
     * 创建者用户单位id
     */
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    /**
     * 备注
     */
    @JoyadataColumn(label = "备注")
    private String remark;
    /**
     * 项目id
     */
    @JoyadataColumn(label = "项目id")
    private String projectId;
    /**
     * 租户id
     */
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    /**
     * 最初授权时间
     */
    @JoyadataColumn(label = "最初授权时间")
    private Date firstAuthorizationTime = null;
    /**
     * 最新授权时间
     */
    @JoyadataColumn(label = "最新授权时间")
    private Date newAuthorizationTime = null;
    /**
     * 数据源版本
     */
    @JoyadataColumn(label = "数据源版本")
    private String dbVersion;
    /**
     * 业务系统uuid
     */
    @JoyadataColumn(label = "业务系统uuid")
    private String businessUuid;
    /**
     * 字符集
     */
    @JoyadataColumn(label = "字符集")
    private String characterSet;
    /**
     * 时区
     */
    @JoyadataColumn(label = "时区")
    private String dsTimeZone;
    /**
     * 时区
     */
    @JoyadataColumn(label = "时区")
    private String ip;
    /**
     * 最大连接数
     */
    @JoyadataColumn(label = "最大连接数")
    private long maximumNumberConnections;

    @JoyadataColumn(label = "是否存在")
    private int existFlag;
    @JoyadataColumn(label = "修改后的json")
    private String afterJson;
    @JoyadataColumn(label = "修改前的json")
    private String beforeJson;
}
