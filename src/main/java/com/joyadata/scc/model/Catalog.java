package com.joyadata.scc.model;

import com.joyadata.enums.Check;
import com.joyadata.model.TreeBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: Catalog
 * @date 2023/11/3
 */
@Data
@JoyadataTable(name = "scc_catalog", label = "scheduler_catalog", comment = "目录表", isPublic = true)
@JoyadataIndex(type = "UNIQUE INDEX",
        name = "project_id_name_tenant_code_type_product_id",
        columns = "name,project_id,product_id,tenant_code,type,pid",
        comment = "同一个租户，同一个产品，同一个项目下，同一种类型,目录名称不可重复", errorMsg = "同一个项目下，目录名称不可重复！")
@EqualsAndHashCode(callSuper = true)
public class Catalog extends TreeBean {
    @JoyadataColumn(label = "名称", check = {Check.NotEmpty}, length = 256)
    private String name;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "目录简称", length = 256)
    private String nickName;
    @JoyadataColumn(label = "备注", length = 2048)
    private String remark;
    @JoyadataColumn(label = "目录类型", comment = "0:自定义目录;1:默认目录", generatorValue = "0")
    private Integer type;
    @JoyadataAggJoin(label = "任务总数", selfColumn = "id", targetBean = Task.class, targetColumn = "catalogId", agg = AGG.COUNT, valueColumn = "name")
    private Integer taskCount;
}
