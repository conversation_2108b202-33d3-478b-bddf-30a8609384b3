package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/8/20 15:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_di_scheduler", label = "di表", isPublic = true)
public class TempDiScheduler extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionCode;
    @JoyadataColumn(label = "工作日历ID")
    private String calendarId;
    @JoyadataColumn(label = "开始时间")
    private Date startTime;
    @JoyadataColumn(label = "结束时间")
    private Date endTime;
    @JoyadataColumn(label = "时区")
    private String timezoneId;
    @JoyadataColumn(label = "告警类型")
    private String warningType;
    @JoyadataColumn(label = "告警组")
    private String warningGroupId;
    @JoyadataColumn(label = "工作组")
    private String workerGroup;
    @JoyadataColumn(label = "表达式")
    private String crontab;
    @JoyadataColumn(label = "执行周期方案:0:推荐频率;1:自定义频率")
    private Integer scheduleFrequency;
    @JoyadataColumn(label = "失败策略(END结束、CONTINUE继续)")
    private String failureStrategy;
    @JoyadataColumn(label = "定时状态(上线、下线)")
    private String releaseState;
    @JoyadataColumn(label = "工作流优先级")
    private String processInstancePriority;
    @JoyadataColumn(label = "环境id")
    private String environmentCode;
    @JoyadataColumn(label = "停用策略:0:不停止工作流;1:停止工作流")
    private String stopStrategy;
    @JoyadataColumn(label = "是否存在")
    private String existFlag;
    @JoyadataColumn(label = "导入前json")
    private String beforeJson;
    @JoyadataColumn(label = "导入后json")
    private String afterJson;
}
