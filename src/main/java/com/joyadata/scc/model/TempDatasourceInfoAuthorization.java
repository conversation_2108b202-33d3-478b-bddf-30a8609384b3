package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据源授权表
 *
 * <AUTHOR>
 * @Date 2025/8/8 11:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_datasource_info_authorization", label = "数据源授权表", isPublic = true)
public class TempDatasourceInfoAuthorization extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    @JoyadataColumn(label = "数据源信息业务主键（UUID）")
    private String datasourceInfoId;
    @JoyadataColumn(label = "产品 或者项目 product  project")
    private String dataType;
    @JoyadataColumn(label = "产品id")
    private String productId;
    @JoyadataColumn(label = "创建者")
    private String createBy;
    @JoyadataColumn(label = "创建时间")
    private Date createTime;
    @JoyadataColumn(label = "更新者")
    private String updateBy;
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    @JoyadataColumn(label = "备注")
    private String remark;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    @JoyadataColumn(label = "唯一标识 随机生成")
    private String authorizationUuid;
    @JoyadataColumn(label = "自己用的项目id")
    private String itemId;
    @JoyadataColumn(label = "类型：前端使用")
    private String type;
    @JoyadataColumn(label = "最大连接数")
    private int maximumNumberConnections;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
