package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/29 11:26.
 */
@Data
@JoyadataTable(label = "scc_force_success_record", name = "scc_force_success_record", comment = "强制成功记录表")
public class ForceSuccessRecord extends BaseBean {
    @JoyadataColumn(label = "类型", comment = "用于区分这条记录是修改的什么实例：processInstance，taskInstance")
    private String type;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "任务实例id")
    private String taskInstanceId;
    @JoyadataColumn(label = "任务id")
    private String taskId;
    @JoyadataColumn(label = "工作流实例id")
    private String processInstanceId;
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "源状态")
    private Integer sourceState;
    @JoyadataColumn(label = "目标状态")
    private Integer targetState;
    @JoyadataColumn(label = "错误信息", comment = "强制成功时海豚可能会报错", length = -1)
    private String errorMessage;
    @JoyadataColumn(label = "强制成功ids", comment = "强制成功工作流实例时，会影响多个任务实例，将每个影响的任务实例都存一条数据，id放在此字段", length = -1)
    private String forceSuccessRecordIds;

    public static class Type {
        public static final String taskInstance = "taskInstance";
        public static final String processInstance = "processInstance";
    }
}
