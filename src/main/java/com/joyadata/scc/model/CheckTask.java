package com.joyadata.scc.model;


import com.joyadata.model.BaseBean;
import com.joyadata.scc.enums.TimeUnit;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_check_task", label = "检查任务表", isPublic = true)
public class CheckTask extends BaseBean {
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "工作流Id")
    private String processDefinitionId;
    @JoyadataColumn(label = "任务Id")
    private String taskId;
    @JoyadataColumn(label = "计划执行时间")
    private LocalTime planExecTime;
    @JoyadataColumn(label = "有效范围")
    private Integer effectiveRange;
    @JoyadataColumn(label = "有效范围单位(D:天;H:时;M:分)")
    private TimeUnit effectiveRangeUnit = TimeUnit.M;
    @JoyadataJoin(label = "工作流名称", targetBean = ProcessDefinition.class, targetColumn = "id",
            selfColumn = "processDefinitionId", valueColumn = "name")
    private String processDefinitionName;
    @JoyadataJoin(label = "任务名称", targetBean = Task.class, targetColumn = "id",
            selfColumn = "taskId", valueColumn = "name")
    private String taskName;
}
