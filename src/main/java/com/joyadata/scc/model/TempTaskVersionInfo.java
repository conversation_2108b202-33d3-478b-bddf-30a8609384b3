package com.joyadata.scc.model;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/18 11:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "scc_temp_task_version_info", label = "任务统计表", isPublic = true)
public class TempTaskVersionInfo extends BaseBean {
    @JoyadataColumn(label = "批次号")
    private String batchNo;
    /**
     * 任务版本id
     */
    @JoyadataColumn(label = "任务版本id")
    private String versionId;
    /**
     * 外键：任务id
     */
    @JoyadataColumn(label = "任务id")
    private String taskId;
    /**
     * 版本号，未发布时此字段为空，如果这个字段有值，则发布时是新增数据，版本在此基础上迭代
     */
    @JoyadataColumn(label = "版本号")
    private String versionNum;
    /**
     * 环境配置
     */
    @JoyadataColumn(label = "环境配置")
    private String environmentConfiguration;
    /**
     * 同步策略
     */
    @JoyadataColumn(label = "同步策略")
    private String synchronizationStrategy;
    /**
     * 调度配置
     */
    @JoyadataColumn(label = "调度配置")
    private String dsgScheduleConfiguration;
    /**
     * 版本状态：0-未发布，1-已发布，2-临时
     */
    @JoyadataColumn(label = "版本状态")
    private int versionStatus;
    /**
     * 提交状态：0-未提交，1-已提交，2-临时
     */
    @JoyadataColumn(label = "提交状态")
    private int submitStatus;
    /**
     * 是否可以提交：0-可以，1-不可以
     */
    @JoyadataColumn(label = "是否可以提交")
    private int publishStatus;
    /**
     * 任务配置json
     */
    @JoyadataColumn(label = "任务配置json")
    private String taskStxConfig;
    /**
     * 工作流名字
     */
    @JoyadataColumn(label = "工作流名字")
    private String processName;
    /**
     * 工作流类型
     */
    @JoyadataColumn(label = "工作流类型")
    private String processType;
    /**
     * 试运行调度实例id
     */
    private String debugTaskInstanceId;
    /**
     * 调度实例id
     */
    @JoyadataColumn(label = "调度实例id")
    private String taskInstanceId;
    /**
     * 最后一次运行结果
     */
    @JoyadataColumn(label = "最后一次运行结果")
    private String lastRunResult;
    /**
     * 最后一次运行时间
     */
    @JoyadataColumn(label = "最后一次运行时间")
    private Date laseRunTime;
    /**
     * 工作流id
     */
    @JoyadataColumn(label = "工作流id")
    private String processDefinitionId;
    @JoyadataColumn(label = "更新者用户Id")
    private String updateBy;
    @JoyadataColumn(label = "更新时间")
    private Date updateTime;
    @JoyadataColumn(label = "创建者用户Id")
    private String createUserId;
    @JoyadataColumn(label = "创建者用户单位id")
    private String createDeptId;
    @JoyadataColumn(label = "备注")
    private String remark;
    @JoyadataColumn(label = "项目id")
    private String projectId;
    @JoyadataColumn(label = "租户id")
    private String tenantId;
    @JoyadataColumn(label = "任务名称")
    private String taskName;
    @JoyadataColumn(label = "任务类型")
    private int taskType;
    @JoyadataColumn(label = "任务描述")
    private String taskRemark;
    /**
     * 任务目录名字
     */
    @JoyadataColumn(label = "任务目录名字")
    private String taskCatalogueName;
    /**
     * 提交时间
     */
    @JoyadataColumn(label = "提交时间")
    private Date submitTime;
    @JoyadataColumn(label = "之前原始数据库的json值，备份数据")
    private String beforeJson;
    @JoyadataColumn(label = "是否存在目标端，0 不存在，1存在")
    private int existFlag;
    @JoyadataColumn(label = "创建后json")
    private String afterJson;
}
