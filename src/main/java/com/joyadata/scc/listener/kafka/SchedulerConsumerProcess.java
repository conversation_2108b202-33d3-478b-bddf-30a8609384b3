package com.joyadata.scc.listener.kafka;

import com.joyadata.cms.model.User;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.scc.listener.kafka.factory.AbstractConsumerHandler;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.model.dto.Release;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.scc.service.SchedulerService;
import com.joyadata.scc.util.RedisUtils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * Created by lihongjun on 2023/11/2.
 */
@Slf4j
@Component
public class SchedulerConsumerProcess extends AbstractConsumerHandler {
    LinkedBlockingQueue<Runnable> queue = new LinkedBlockingQueue<>(10);
    ThreadFactory threadFactory = Executors.defaultThreadFactory();
    ThreadPoolExecutor executor = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, queue, threadFactory);

    private final HttpRequestFeignService httpRequestFeignService;
    private final ProcessDefinitionService processDefinitionService;
    private final SchedulerService schedulerService;
    @Value("${dolphinscheduler:http://dscheduler.dsg.com:12345/dolphinscheduler}")
    private String dolphinscheduler;
    private final JoyaFeignService<User> userService = FeignFactory.make(User.class);

    public SchedulerConsumerProcess(HttpRequestFeignService httpRequestFeignService, ProcessDefinitionService processDefinitionService, SchedulerService schedulerService) {
        this.httpRequestFeignService = httpRequestFeignService;
        this.processDefinitionService = processDefinitionService;
        this.schedulerService = schedulerService;
    }


    @Override
    public void consumer(ConsumerRecord<String, String> consumerRecord) {
        executor.submit(() -> {
            if (null != consumerRecord.value()) {
                //消费kafka数据，下线工作流
                String processDefinitionId = consumerRecord.value();
                // 工作流下线时 定时任务也要下线
                Scheduler schedule = schedulerService.getQuery().eq("processDefinitionCode", processDefinitionId).one();
                if (null != schedule && schedule.getReleaseState().equals(Scheduler.ReleaseState.online)) {
                    schedulerService.offline(schedule);
                }

                ProcessDefinition processDefinition = processDefinitionService.getById(processDefinitionId);
                Release release = new Release();
                release.setReleaseState(Release.ReleaseState.OFFLINE);
                release.setProjectCode(processDefinition.getProjectId());

                Map<String, Object> headers = getHeader(processDefinition.getTenantCode());
                String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", release.getProjectCode());
                String uri = Constants.PROCESS_RELEASE.replace("{code}", processDefinitionId) + "?releaseState=" + release.getReleaseState();
                Map<?,?> resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
                if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
                    log.error("工作流下线失败！请联系管理员，报错信息是 {}", resultMap);
                    throw new AppErrorException("工作流下线失败！请联系管理员，报错信息：" + resultMap.get("msg"));
                }
            }
        });
    }

    private  Map<String, Object> getHeader(String tenantCode) {
        String systemToken = AuthUtil.getSystemToken(null);
        ThreadLocalUserUtil.setCode("token", systemToken);
        //加上超管的租户code
        ThreadLocalUserUtil.setCode("tenantCode", tenantCode);

        String username = tenantCode + "_manager";
        String password = userService.getQuery().eq("username", username).fixeds("password").oneValue("password", String.class);

        String dsToken = RedisUtils.login(username, password);
        Map<String, Object> headers = new HashMap<>();
        headers.put(Constants.SESSION_ID, dsToken);
        return headers;
    }
}