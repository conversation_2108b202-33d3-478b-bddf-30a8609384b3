package com.joyadata.scc.listener.kafka.factory;

import com.google.common.collect.Maps;
import com.joyadata.scc.listener.kafka.SchedulerConsumerProcess;
import com.joyadata.scc.util.constant.Constants;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/2.
 */
@Component
public class ConsumerFactory {
    private static final Map<String, AbstractConsumerHandler> handlerMap = Maps.newHashMap();

    public ConsumerFactory(SchedulerConsumerProcess schedulerConsumerProcess) {
        handlerMap.put(Constants.STOP_PROCESS, schedulerConsumerProcess);
    }

    public AbstractConsumerHandler getHandler(String topic) {
        return handlerMap.get(topic);
    }

}