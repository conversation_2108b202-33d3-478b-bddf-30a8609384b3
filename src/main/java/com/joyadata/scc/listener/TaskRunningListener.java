package com.joyadata.scc.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.cores.kafka.start.annotaion.JoyaKafkaListener;
import com.joyadata.cores.kafka.start.annotaion.JoyaKafkaListenerComponent;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.cores.kafka.start.util.constant.GroupIds;
import com.joyadata.cores.kafka.start.util.constant.Topics;
import com.joyadata.scc.enums.TimeUnit;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.service.TaskService;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/22
 */
@Slf4j
@Data
@JoyaKafkaListenerComponent
public class TaskRunningListener {

    private IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);

    @Autowired
    private TaskService taskService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @JoyaKafkaListener(topics = Topics.APP_EVENTS_V1_R2P1, groupId = GroupIds.dedp_tenant_csc)
    public void receive(ConsumerRecord<String, String> record) {
        String value = record.value();
        log.info("收到告警信息:{}", value);
        try {
            Map<String, Object> msg = JSON.parseObject(value, Map.class);
            ThreadLocalUserUtil.setCode("token", AuthUtil.getSystemToken(null));
            if (null != msg.get("data") && "B301006".equals(msg.get("eventCode"))) {
                Map<String, String> data = JSON.parseObject(msg.get("data").toString(), Map.class);
                if (null != data.get("taskId")) {
                    String taskName = data.get("taskName");
                    Task task;
                    if (null != taskName && taskName.contains("试运行-") && taskName.contains("-[temp]")) {
                        int startIndex = taskName.indexOf("-[temp]");
                        int endIndex = taskName.lastIndexOf("-", startIndex - 1);
                        String taskId = taskName.substring(endIndex + 1, startIndex);
                        task = taskService.setIgnoreTenantCode().getById(taskId);
                    } else {
                        task = taskService.setIgnoreTenantCode().getById(data.get("taskId"));
                    }
                    if (null != task && task.getTaskTimeout() > 0) {//大于0 说明任务有需要告警的。开始计时
                        switch (task.getTaskTimeoutStrategy()) {
                            case 0:
                                log.info("2024-07-26111当前配置不需要告警和kill任务");
                                break;
                            case 1:
                                log.info("2024-07-26111准备告警！！");
                                sendAlarm(msg, task, redisTemplate);
                                //告警
                                break;
                            case 2:
//                                log.info("2024-07-2611准备kill！！");
                                //killTask(msg, task.getDataOwnerUserId(), task.getTaskTimeout(), redisTemplate);
                                //kill任务
                                break;
                            case 3:
                                log.info("2024-07-2611告警&kill:准备告警！！");
                                //告警
                                sendAlarm(msg, task, redisTemplate);
//                                log.info("2024-07-2611告警&kill:准备kill！！");
                                //kill任务
                                //killTask(msg, task.getDataOwnerUserId(), task.getTaskTimeout(), redisTemplate);
                                break;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("task运行告警消费信息错误={}", e.getMessage(), e);
        }
    }

    private void sendAlarm(Map<String, Object> map, Task task, StringRedisTemplate redisTemplate) {
        String taskInstanceId = map.get("businessId").toString();//任务实例id
        Long timeOut = task.getTaskTimeout();
        TimeUnit timeoutUnit = task.getTimeoutUnit();
        long futureTimeMillis = timeOut * 1000;
        if (null != timeoutUnit) {
            switch (timeoutUnit) {
                case D:
                    futureTimeMillis = futureTimeMillis * 60 * 60 * 24;
                    break;
                case H:
                    futureTimeMillis = futureTimeMillis * 60 * 60;
                    break;
                case M:
                    futureTimeMillis = futureTimeMillis * 60;
                    break;
            }
        }
        futureTimeMillis = System.currentTimeMillis() + futureTimeMillis;
        log.info("2024-07-26111发送告警准备放入redis，taskInstanceId={},futureTimeMillis={},map={}", taskInstanceId,
                futureTimeMillis, JSONObject.toJSONString(map));
        //放入redis
        redisTemplate.opsForZSet().add("dedp:scc:task:alarm:alarm", taskInstanceId, futureTimeMillis);
        redisTemplate.opsForValue().set("dedp:scc:task:alarm:alarm:" + taskInstanceId, JSONObject.toJSONString(map));
        log.info("2024-07-26111发送告警放入redis，taskInstanceId={},futureTimeMillis={},map={}", taskInstanceId,
                futureTimeMillis, JSONObject.toJSONString(map));
    }


    private void killTask(Map<String, Object> map, String userId, long timeOut, StringRedisTemplate redisTemplate) {
        String taskInstanceId = map.get("businessId").toString();//任务实例id
        long futureTimeMillis = System.currentTimeMillis() + timeOut * 1000 * 60;
        log.info("2024-07-26111发送kill放入redis，taskInstanceId={},futureTimeMillis={}", taskInstanceId, futureTimeMillis);
        //放入redis
        redisTemplate.opsForZSet().add("dedp:scc:task:alarm:kill", taskInstanceId, futureTimeMillis);
    }

    public static void main(String[] args) {
        System.out.println(new Date(1723627092829L));
    }
}
