package com.joyadata.scc.listener;


import com.joyadata.cores.kafka.start.annotaion.JoyaKafkaListener;
import com.joyadata.cores.kafka.start.annotaion.JoyaKafkaListenerComponent;
import com.joyadata.cores.kafka.start.util.constant.GroupIds;
import com.joyadata.cores.kafka.start.util.constant.Topics;
import com.joyadata.scc.listener.kafka.factory.AbstractConsumerHandler;
import com.joyadata.scc.listener.kafka.factory.ConsumerFactory;
import com.joyadata.scc.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * Created by l<PERSON><PERSON>jun on 2023/11/2.
 */
@Slf4j
@JoyaKafkaListenerComponent
public class SchedulerConsumerMessageListener {
    private final ConsumerFactory consumerFactory;

    public SchedulerConsumerMessageListener(ConsumerFactory consumerFactory) {
        this.consumerFactory = consumerFactory;
    }


    @JoyaKafkaListener(groupId = GroupIds.dedp_tenant_csc, topics = Topics.DS_STOP_PROCESS_V1_R2P1)
    public void actionAdd(ConsumerRecord<String, String> record) {
        AbstractConsumerHandler handler = consumerFactory.getHandler(Constants.STOP_PROCESS);
        handler.consumer(record);
    }


}