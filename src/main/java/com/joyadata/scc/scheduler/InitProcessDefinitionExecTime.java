package com.joyadata.scc.scheduler;

import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.model.sql.LeCondition;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.service.ProcessDefinitionExecTimeService;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@JoyaScheduledComponent
public class InitProcessDefinitionExecTime {

    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private ProcessDefinitionExecTimeService processDefinitionExecTimeService;


    @JoyaScheduled(fixedRate = 30 * 60 * 1000L, initialDelay = 30 * 1000)
    public void scheduler() {
        Date now = new Date();
        List<ProcessDefinition> processDefinitionList = processDefinitionService.setIgnoreTenantCode().setIgnorePermissions().getQuery()
                .eq("scheduleStatus", Scheduler.ReleaseState.online)
                .withs("scheduleStatus", "crontab", "calendarId", "scheduleEndTime", "scheduleStartTime", "scheduleTimezoneId")
                .filters("id", "tenantCode", "crontab", "calendarId", "scheduleEndTime", "scheduleStartTime", "scheduleTimezoneId").list();
        if (null != processDefinitionList && !processDefinitionList.isEmpty()) {
            Map<String, List<ProcessDefinition>> tenantProcessList = processDefinitionList.stream()
                    .collect(Collectors.groupingBy(ProcessDefinition::getTenantCode));
            if (!tenantProcessList.isEmpty()) {
                for (String tenantCode : tenantProcessList.keySet()) {
                    List<ProcessDefinition> processDefinitions = tenantProcessList.get(tenantCode);
                    if (null != processDefinitions && !processDefinitions.isEmpty()) {
                        //加上超管的租户code
                        ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
                        processDefinitionExecTimeService.calculate30Times(processDefinitions, tenantCode);
                    }
                }
            }
        }

        //删除所有过期时间
        Integer delCount = processDefinitionExecTimeService.setIgnorePermissions().setIgnoreTenantCode().deleteBy(new LeCondition("execTime", now));
        log.info("删除过期执行时间数量={}", delCount);
    }
}
