package com.joyadata.scc.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.cores.kafka.start.util.constant.Topics;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.scc.model.CheckTask;
import com.joyadata.scc.model.CheckTaskRecord;
import com.joyadata.scc.service.CheckTaskRecordService;
import com.joyadata.scc.service.CheckTaskService;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@JoyaScheduledComponent
public class CheckTaskIsExecScheduler {

    @Autowired
    private CheckTaskService checkTaskService;
    @Autowired
    private CheckTaskRecordService checkTaskRecordService;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    private final IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);

    @JoyaScheduled(cron = "0 0/1 * * * ?", initialDelay = 30 * 1000)
    public void scheduler() {
        //查询需要检查的taskId
        LocalTime now = LocalTime.now();
        LocalTime oneMinuteAgo = now.minusMinutes(1);

        List<CheckTask> checkTaskList = checkTaskService.setIgnoreTenantCode().setIgnorePermissions().getQuery().ge("planExecTime", oneMinuteAgo).lt("planExecTime", now).withs("processDefinitionName", "taskName").list();
        if (null != checkTaskList && !checkTaskList.isEmpty()) {
            Map<String, List<CheckTask>> tenantTaskList = checkTaskList.stream()
                    .collect(Collectors.groupingBy(CheckTask::getTenantCode));
            if (!tenantTaskList.isEmpty()) {
                tenantTaskList.keySet().forEach(tenantCode -> {
                    //加上超管的租户code
                    ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
                    List<CheckTask> checkTasks = tenantTaskList.get(tenantCode);
                    checkTasks.forEach(checkTask -> {
                        LocalTime planExecTime = checkTask.getPlanExecTime();
                        Runnable check = () -> {
                            //检查任务是否执行
                            if (!hasExecutionRecord(checkTask)) {
                                //任务未执行，需要往事件中心发送事件
                                Date checkTime = Date.from(LocalDateTime.of(LocalDate.now(), planExecTime.minusMinutes(-1)).atZone(ZoneId.systemDefault()).toInstant());
                                JSONObject json = new JSONObject();
                                json.put("eventCode", "B301012");
                                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                json.put("date", df.format(new Date()));
                                JSONObject data = new JSONObject();
                                data.put("processDefinitionName", checkTask.getProcessDefinitionName());
                                data.put("taskName", checkTask.getTaskName());
                                data.put("checkTime", df.format(checkTime));
                                json.put("data", data);
                                json.put("tenantCode", tenantCode);
                                json.put("projectId", checkTask.getProjectId());
                                log.info("发送告警=B301012, json={}", json);
                                kafkaTemplate.send(Topics.APP_EVENTS_V1_R2P1, json.toJSONString());
                            }
                        };
                        scheduler.schedule(check, planExecTime.getSecond(), TimeUnit.SECONDS);
                    });
                });
            }
        }
    }

    //检查是否有记录
    private boolean hasExecutionRecord(CheckTask checkTask) {

        // 计算有效范围，格式化Date为字符串
        Date planExecDate = Date.from(LocalDateTime.of(LocalDate.now(), checkTask.getPlanExecTime()).atZone(ZoneId.systemDefault()).toInstant());

        Date effectiveRangeTime = getEffectiveRangeTime(checkTask.getEffectiveRange(), checkTask.getEffectiveRangeUnit(), planExecDate);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String effectiveRangeTimeStr = sdf.format(effectiveRangeTime);

        String sql = "SELECT id FROM `business_ds`.`t_ds_task_instance` WHERE task_code='%s' AND start_time >= '%s' LIMIT 1";
        String taskInstanceId = checkTaskService.getSqlExecutor().excuteSelect(String.format(sql, checkTask.getTaskId(), effectiveRangeTimeStr)).oneValue("id", String.class);
        //添加检查记录
        if (StringUtils.isBlank(taskInstanceId)) {
            createCheckTaskRecord(checkTask, CheckTaskRecord.Status.error);
            return false;
        }
        createCheckTaskRecord(checkTask, CheckTaskRecord.Status.success);
        return true;
    }


    private static Date getEffectiveRangeTime(Integer effectiveRange, com.joyadata.scc.enums.TimeUnit effectiveRangeUnit, Date planExecDate) {
        if (null != effectiveRange && null != effectiveRangeUnit && null != planExecDate) {
            long futureTimeMillis = effectiveRange * 1000;
            switch (effectiveRangeUnit) {
                case D:
                    futureTimeMillis = futureTimeMillis * 60 * 60 * 24;
                    break;
                case H:
                    futureTimeMillis = futureTimeMillis * 60 * 60;
                    break;
                case M:
                    futureTimeMillis = futureTimeMillis * 60;
                    break;
            }
            return new Date(planExecDate.getTime() - futureTimeMillis);
        } else {
            throw new RuntimeException("参数异常！");
        }
    }

    private void createCheckTaskRecord(CheckTask checkTask, Integer status) {
        CheckTaskRecord checkTaskRecord = new CheckTaskRecord();
        checkTaskRecord.setProjectId(checkTask.getProjectId());
        checkTaskRecord.setProcessDefinitionId(checkTask.getProcessDefinitionId());
        checkTaskRecord.setTaskId(checkTask.getTaskId());
        LocalTime planExecTime = checkTask.getPlanExecTime();
        checkTaskRecord.setPlanExecTime(planExecTime);

        //计划检查时间后1分钟
        checkTaskRecord.setCheckTime(Date.from(LocalDateTime.of(LocalDate.now(), planExecTime.minusMinutes(-1)).atZone(ZoneId.systemDefault()).toInstant()));
        checkTaskRecord.setStatus(status);
        checkTaskRecord.setTenantCode(checkTask.getTenantCode());
        checkTaskRecordService.add(checkTaskRecord);
    }
}
