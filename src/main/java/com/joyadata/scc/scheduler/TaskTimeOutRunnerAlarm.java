package com.joyadata.scc.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.cores.kafka.start.util.constant.Topics;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/7/22
 */
@Slf4j
@Component
@JoyaScheduledComponent
public class TaskTimeOutRunnerAlarm {
    @Autowired
    private StringRedisTemplate redisTemplate;

    private final IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);

    @JoyaScheduled(initialDelay = 30 * 1000, fixedRate = 60 * 1000)
    public void run() {
        long sdate = System.currentTimeMillis();
        //放入redis
        Set<String> values = redisTemplate.opsForZSet().rangeByScore("dedp:scc:task:alarm:alarm", 0, sdate);
        if (null != values && !values.isEmpty()) {
            List<String> delKeys = new ArrayList<>();
            values.forEach(value -> {
                String obj = redisTemplate.opsForValue().get("dedp:scc:task:alarm:alarm:" + value);
                Map<String, Object> map = JSONObject.parseObject(obj, Map.class);
                map.put("eventCode", "B301007");
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                map.put("date", df.format(new Date()));
                try {
                    log.info("发送告警=B301007, map={}", map);
                    kafkaTemplate.send(Topics.APP_EVENTS_V1_R2P1, JSONObject.toJSONString(map));
                } catch (Exception e) {
                    log.error("工作流超时告警,发送消息出现错误 {}", e.getMessage());
                }
                delKeys.add("dedp:scc:task:alarm:alarm:" + value);
            });
            redisTemplate.opsForZSet().removeRange("dedp:scc:task:alarm:alarm", 0, sdate);
            log.info("删除rediskeys={}", delKeys);
            redisTemplate.delete(delKeys);
        }
    }
}
