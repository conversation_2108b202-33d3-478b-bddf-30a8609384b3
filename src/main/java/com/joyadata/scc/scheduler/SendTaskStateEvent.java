package com.joyadata.scc.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.cores.kafka.start.util.constant.Topics;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.scc.service.TaskInstanceService;
import com.joyadata.tms.model.Tenant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/6/26 14:35.
 * 统计任务实例状态发送事件，统计整个平台，不区分项目
 */
@Slf4j
@Component
@JoyaScheduledComponent
public class SendTaskStateEvent {

    @Autowired
    private TaskInstanceService taskInstanceService;
    private final IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);
    private final JoyaFeignService<Tenant> tenantJoyaFeignService = FeignFactory.make(Tenant.class);
    private static final String superAdminTenantCode = "-1";


    /**
     * 每天早上9点统计昨天的任务状态发送一次事件
     */
//    @JoyaScheduled(cron = "0 0/1 * * * ? ", fixedRate = 60 * 1000)
    @JoyaScheduled(cron = "0 0 9 * * ? ")//TODO 1、后面需要将框架定时任务的定时设置改为可配置的。2、统计sql也要改为可配置的
    public void yesterdayTask() {
        List<String> tenantCodeList = tenantJoyaFeignService.setIgnoreTenantCode().setIgnorePermissions().getQuery().listValue("code", String.class);
//        List<String> tenantCodeList = Arrays.asList("dsg");
        for (String tenantCode : tenantCodeList) {
            //跳过超级管理员，因为cms发送告警时如果是超级管理员，则会给所有配置告警的租户发送告警
            if (superAdminTenantCode.equals(tenantCode)) {
                continue;
            }
            JSONObject data = taskInstanceService.getSqlExecutor().excuteSelect(getYesterdaySelectSql(tenantCode)).json();
            System.out.println(data.toJSONString());
            Map<String, Object> map = new HashMap<>();
            map.put("eventCode", "B301013");
            map.put("data", data);
            map.put("tenantCode", tenantCode);
            try {
                log.info("发送事件=B301013, map={}", map);
                kafkaTemplate.send(Topics.APP_EVENTS_V1_R2P1, JSONObject.toJSONString(map));
            } catch (Exception e) {
                log.error("昨日任务状态统计,发送消息出现错误 {}", e.getMessage());
            }
        }
    }

    /**
     * 每天下午6点统计今天的任务状态发送一次事件
     */
    @JoyaScheduled(cron = "0 0 18 * * ? ")
    public void todayTask() {
        List<String> tenantCodeList = tenantJoyaFeignService.setIgnoreTenantCode().setIgnorePermissions().getQuery().listValue("code", String.class);
        for (String tenantCode : tenantCodeList) {
            //跳过超级管理员，因为cms发送告警时如果是超级管理员，则会给所有配置告警的租户发送告警
            if (superAdminTenantCode.equals(tenantCode)) {
                continue;
            }
            JSONObject data = taskInstanceService.getSqlExecutor().excuteSelect(getTodaySelectSql(tenantCode)).json();
            Map<String, Object> map = new HashMap<>();
            map.put("eventCode", "B301014");
            map.put("data", data);
            map.put("tenantCode", tenantCode);
            try {
                log.info("发送事件=B301014, map={}", map);
                kafkaTemplate.send(Topics.APP_EVENTS_V1_R2P1, JSONObject.toJSONString(map));
            } catch (Exception e) {
                log.error("昨日任务状态统计,发送消息出现错误 {}", e.getMessage());
            }
        }
    }

    public static void main(String[] args) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 昨天的开始时间 (00:00:00)
        LocalDateTime startOfYesterday = yesterday.atStartOfDay();
        String startStr = startOfYesterday.format(formatter);

        // 昨天的结束时间 (23:59:59)
        LocalDateTime endOfYesterday = yesterday.atTime(23, 59, 59);
        String endStr = endOfYesterday.format(formatter);

        System.out.println("昨天的开始时间: " + startStr);
        System.out.println("昨天的结束时间: " + endStr);
        System.out.println(getYesterdaySelectSql("dsg"));

        //--------------------------今天---------------------
        // 定义日期时间格式
        DateTimeFormatter formatterToday = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取今天的日期
        LocalDate today = LocalDate.now();

        // 今天的开始时间 (00:00:00)
        LocalDateTime startOfToday = today.atStartOfDay();
        String startStrToday = startOfToday.format(formatter);

        // 今天的下午六点 (18:00:00)
        LocalDateTime sixPmToday = today.atTime(18, 0, 0);
        String sixPmStr = sixPmToday.format(formatter);

        System.out.println("今天的开始时间: " + startStr);
        System.out.println("今天的下午六点: " + sixPmStr);
        System.out.println(getTodaySelectSql("dsg"));
    }

    public static String getYesterdaySelectSql(String tenantCode) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 昨天的开始时间 (00:00:00)
        LocalDateTime startOfYesterday = yesterday.atStartOfDay();
        String startStr = startOfYesterday.format(formatter);

        // 昨天的结束时间 (23:59:59)
        LocalDateTime endOfYesterday = yesterday.atTime(23, 59, 59);
        String endStr = endOfYesterday.format(formatter);
        return String.format("select\n" +
                "\tSUM(case when latest_sti.state = 0 then 1 else 0 end) as `submit_success`,\n" +
                "\tSUM(case when latest_sti.state = 1 then 1 else 0 end) as `running`,\n" +
                "\tSUM(case when latest_sti.state = 3 then 1 else 0 end) as `pause`,\n" +
                "\tSUM(case when latest_sti.state = 6 then 1 else 0 end) as `failure`,\n" +
                "\tSUM(case when latest_sti.state = 7 then 1 else 0 end) as `success`,\n" +
                "\tSUM(case when latest_sti.state = 9 then 1 else 0 end) as `kill`,\n" +
                "\tSUM(case when latest_sti.state = 12 then 1 else 0 end) as `delay_execution`,\n" +
                "\tSUM(case when latest_sti.state = 13 then 1 else 0 end) as `forced_success`,\n" +
                "\tSUM(case when latest_sti.state = 17 then 1 else 0 end) as `dispatch`\n" +
                "from\n" +
                "\tscc_process_definition spd\n" +
                "inner join scc_process_task_relation sptr on\n" +
                "\tspd.id = sptr.process_definition_code\n" +
                "inner join joyadata.tms_project tp on\n" +
                "\tspd.project_id = tp.id\n" +
                "left join (\n" +
                "\tselect\n" +
                "\t\tsti.task_id,\n" +
                "\t\tsti.state\n" +
                "\tfrom\n" +
                "\t\tscc_task_instance sti\n" +
                "\tinner join (\n" +
                "\t\tselect\n" +
                "\t\t\ttask_id,\n" +
                "\t\t\tMAX(submit_time) as latest_submit_time\n" +
                "\t\tfrom\n" +
                "\t\t\tscc_task_instance\n" +
                "\t\twhere\n" +
                "\t\t\tsubmit_time >= '%s'\n" +
                "\t\t\tand submit_time <= '%s'\n" +
                "\t\t\tand tenant_code = '%s'\n" +
                "\t\tgroup by\n" +
                "\t\t\ttask_id ) latest on\n" +
                "\t\tsti.task_id = latest.task_id\n" +
                "\t\tand sti.submit_time = latest.latest_submit_time ) latest_sti on\n" +
                "\tsptr.post_task_code = latest_sti.task_id\n" +
                "where\n" +
                "\tspd.release_state = '上线';", startStr, endStr, tenantCode);
    }


    public static String getTodaySelectSql(String tenantCode) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取今天的日期
        LocalDate today = LocalDate.now();

        // 今天的开始时间 (00:00:00)
        LocalDateTime startOfToday = today.atStartOfDay();
        String startStr = startOfToday.format(formatter);

        // 今天的下午六点 (18:00:00)
        LocalDateTime sixPmToday = today.atTime(18, 0, 0);
        String endStr = sixPmToday.format(formatter);
        return String.format("select\n" +
                "\tSUM(case when latest_sti.state = 0 then 1 else 0 end) as `submit_success`,\n" +
                "\tSUM(case when latest_sti.state = 1 then 1 else 0 end) as `running`,\n" +
                "\tSUM(case when latest_sti.state = 3 then 1 else 0 end) as `pause`,\n" +
                "\tSUM(case when latest_sti.state = 6 then 1 else 0 end) as `failure`,\n" +
                "\tSUM(case when latest_sti.state = 7 then 1 else 0 end) as `success`,\n" +
                "\tSUM(case when latest_sti.state = 9 then 1 else 0 end) as `kill`,\n" +
                "\tSUM(case when latest_sti.state = 12 then 1 else 0 end) as `delay_execution`,\n" +
                "\tSUM(case when latest_sti.state = 13 then 1 else 0 end) as `forced_success`,\n" +
                "\tSUM(case when latest_sti.state = 17 then 1 else 0 end) as `dispatch`\n" +
                "from\n" +
                "\tscc_process_definition spd\n" +
                "inner join scc_process_task_relation sptr on\n" +
                "\tspd.id = sptr.process_definition_code\n" +
                "inner join joyadata.tms_project tp on\n" +
                "\tspd.project_id = tp.id\n" +
                "left join (\n" +
                "\tselect\n" +
                "\t\tsti.task_id,\n" +
                "\t\tsti.state\n" +
                "\tfrom\n" +
                "\t\tscc_task_instance sti\n" +
                "\tinner join (\n" +
                "\t\tselect\n" +
                "\t\t\ttask_id,\n" +
                "\t\t\tMAX(submit_time) as latest_submit_time\n" +
                "\t\tfrom\n" +
                "\t\t\tscc_task_instance\n" +
                "\t\twhere\n" +
                "\t\t\tsubmit_time >= '%s'\n" +
                "\t\t\tand submit_time <= '%s'\n" +
                "\t\t\tand tenant_code = '%s'\n" +
                "\t\tgroup by\n" +
                "\t\t\ttask_id ) latest on\n" +
                "\t\tsti.task_id = latest.task_id\n" +
                "\t\tand sti.submit_time = latest.latest_submit_time ) latest_sti on\n" +
                "\tsptr.post_task_code = latest_sti.task_id\n" +
                "where\n" +
                "\tspd.release_state = '上线';", startStr, endStr, tenantCode);
    }
}
