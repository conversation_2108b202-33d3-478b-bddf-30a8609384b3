package com.joyadata.scc.scheduler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.scc.model.EngineJobMetrics;
import com.joyadata.scc.model.TaskInstance;
import com.joyadata.scc.service.EngineJobMetericsService;
import com.joyadata.scc.service.TaskInstanceService;
import com.joyadata.scc.util.EngineUtils;
import com.joyadata.util.ApplicationContextHelp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Slf4j
@JoyaScheduledComponent
public class MetericsScheduler2 {
    //    @Value("${engine.hazelcast.ips}")
//    private List<String> engineIps;
    @Autowired
    private HttpRequestFeignService httpRequestFeignService;

    private final EngineJobMetericsService jobMetericsService;
    private final StringRedisTemplate redisTemplate;
    //2025-06-23，资产中心现在改为用接口，不使用kafka消息
//    private static final String KAFKATOPIC = "SCC_ENGINE_JOB_METRICS_V1_R2P1";
//
//    private final IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);

    public MetericsScheduler2(StringRedisTemplate redisTemplate, EngineJobMetericsService jobMetericsService) {
        this.redisTemplate = redisTemplate;
        this.jobMetericsService = jobMetericsService;
    }


    @JoyaScheduled(fixedRate = 10 * 1000L, initialDelay = 30 * 1000)
    public void scheduler() {
        String sccKey = "DEDP:DS:TASK:SCHEDULER";
        Set<Object> keys = redisTemplate.opsForHash().keys(sccKey);
        if (!keys.isEmpty()) {
            Map<String, Long> taskInstaceMap = getTaskIdMap(keys);
            for (Object key : keys) {
                String value = (String) redisTemplate.opsForHash().get(sccKey, key);
                String tenantCode = key.toString().split(":")[0];
                String instanceId = key.toString().split(":")[1];
                Map<String, String> map = JSON.parseObject(value, Map.class);
                if (null != map) {
                    String[] appids = map.get("appid").split(",");
                    String host = map.get("host");
                    String sourceCountMapStr = map.get("sourceCountMap");
                    JSONObject sourceCountJson = new JSONObject();
                    if (StringUtils.isNotBlank(sourceCountMapStr)) {
                        sourceCountJson = JSONObject.parseObject(map.get("sourceCountMap"));
                    }
                    JSONObject finalSourceCountJson = sourceCountJson;
                    Arrays.stream(appids).forEach(appid -> saveMeterics(appid, tenantCode, instanceId, taskInstaceMap, host, finalSourceCountJson));
                }
            }
            log.info("运行中的任务:{}", keys);
        } else {
            log.info("无任务在运行");
        }
    }

    private Map<String, Long> getTaskIdMap(Set<Object> keys) {
        Map<String, Long> resultMap = new HashMap<>();
        if (keys != null && !keys.isEmpty()) {
            List<String> list = new ArrayList<>();
            for (Object key : keys) {
                String instanceId = key.toString().split(":")[1];
                list.add(instanceId);
            }
            List<TaskInstance> taskInstanceList = getApiBasicStandards(list);
            if (CollUtil.isNotEmpty(taskInstanceList)) {
                resultMap = taskInstanceList.stream().collect(Collectors.toMap(TaskInstance::getId, TaskInstance::getTaskId));
            }
        }
        return resultMap;
    }

    private List<TaskInstance> getApiBasicStandards(List<String> apiIds) {
        List<TaskInstance> apiBasicStandards = new ArrayList<>();
        List<String> pagedData = null;
        List<TaskInstance> apiBasicStandardPageList = null;
        int pageNumber = 1;
        int pageSize = 1000;
        do {
            pagedData = getPagedData(apiIds, pageNumber, pageSize);
            if (CollUtil.isNotEmpty(pagedData)) {
                apiBasicStandardPageList = ApplicationContextHelp.getSpringBean(TaskInstanceService.class).getQuery().in("id", pagedData).list();
                if (null != apiBasicStandardPageList) {
                    apiBasicStandards.addAll(apiBasicStandardPageList);
                }
                pageNumber++;
            }
        } while (CollUtil.isNotEmpty(pagedData));
        return apiBasicStandards;
    }

    public static List<String> getPagedData(List<String> data, int pageNumber, int pageSize) {
        if (data == null || data.isEmpty() || pageNumber < 1 || pageSize < 1) {
            return new ArrayList<>(); // 返回空列表
        }

        int totalSize = data.size();
        int fromIndex = (pageNumber - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalSize);

        // 检查索引有效性
        if (fromIndex >= totalSize) {
            return new ArrayList<>(); // 返回空列表
        }

        return data.subList(fromIndex, toIndex);
    }

    /**
     * 该作业已被调度程序接收，并正在等待作业管理器接收领导权并创建。
     * INITIALIZING(EndState.NOT_END),
     * 作业是新创建的，尚未开始运行任何任务。
     * CREATED(EndState.NOT_END),
     * 作业已开始调度，但一些任务尚未完成部署。
     * SCHEDULED(EndState.NOT_END),
     * 一些任务已经被调度或正在运行，其中一些可能是挂起的，一些可能已经完成。
     * RUNNING(EndState.NOT_END),
     * 作业已失败，并目前正在等待清理完成。
     * FAILING(EndState.NOT_END),
     * 作业已因不可恢复的任务失败而失败。
     * FAILED(EndState.GLOBALLY),
     * 作业正在取消。
     * CANCELLING(EndState.NOT_END),
     * 作业已被取消
     * CANCELED(EndState.GLOBALLY),
     * 作业的所有任务都已成功完成。
     * FINISHED(EndState.GLOBALLY),
     * 作业当前正在进行重置和完全重启。
     * RESTARTING(EndState.NOT_END),该作业已被挂起，这意味着它已经停止，但尚未从潜在的HA（高可用性）作业存储中删除。
     * SUSPENDED(EndState.LOCALLY),作业当前正在协调，并等待任务执行报告以恢复状态。
     * RECONCILING(EndState.NOT_END);
     **/
    private void saveMeterics(String appid, String tenantCode, String instanceId, Map<String, Long> instanceIdMap, String host, JSONObject sourceCountJson) {
        Map<String, Object> result = EngineUtils.getMetericsMap(appid, host, httpRequestFeignService);
        if (!result.isEmpty()) {
            String jobStatus = result.get("jobStatus").toString();
            try {
                JSONObject read = (JSONObject) result.get("metrics");
                JSONObject tableSourceReceivedCount = read.getJSONObject("TableSourceReceivedCount");
                JSONObject tableSinkWriteCount = read.getJSONObject("TableSinkWriteCount");
                Set<String> keys = tableSourceReceivedCount.keySet();
                if (tableSourceReceivedCount.isEmpty() || tableSinkWriteCount.isEmpty() || tableSourceReceivedCount.containsKey("default.default") || tableSinkWriteCount.containsKey("default.default")) {
                    //是文件同步
                    initFileIobMetrics(result, tenantCode, appid, jobStatus, instanceId, instanceIdMap);
                } else {
                    //表同步
                    initTableIobMetrics(keys, result, tenantCode, appid, jobStatus, instanceId, instanceIdMap, sourceCountJson);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            //直到最后一条运行中的结束 再删除rediskey
            if (Arrays.asList("FAILING", "FAILED", "CANCELLING", "CANCELED", "FINISHED").contains(jobStatus)) {
                redisTemplate.opsForHash().delete("DEDP:DS:TASK:SCHEDULER", tenantCode + ":" + instanceId);
            }
        }

    }

    private void initFileIobMetrics(Map<String, Object> result, String tenantCode, String appid, String jobStatus, String instanceId, Map<String, Long> instanceIdMap) {
        String createTime = result.get("createTime").toString();
        String finishTime = null != result.get("finishTime") ? result.get("finishTime").toString() : null;
        JSONObject read = (JSONObject) result.get("metrics");
        String sourceReceivedQPS = read.get("SourceReceivedQPS").toString().split("\\.")[0];
        String sinkWriteQPS = read.get("SinkWriteQPS").toString().split("\\.")[0];
        String sourceReceivedCount = read.get("SourceReceivedCount").toString();
        String sinkWriteCount = read.get("SinkWriteCount").toString();
        String sourceReceivedBytesPerSeconds = read.getString("SourceReceivedBytesPerSeconds");
        String sinkWriteBytesPerSeconds = read.getString("SinkWriteBytesPerSeconds");
        EngineJobMetrics jobMetrics = new EngineJobMetrics();
        long end = getTime(finishTime);
        long start = getTime(createTime);
        jobMetrics.setReadQps(Long.parseLong(sourceReceivedQPS));
        jobMetrics.setReadRowCount(Long.parseLong(sourceReceivedCount));
        jobMetrics.setWriteQps(Long.parseLong(sinkWriteQPS));
        jobMetrics.setWriteRowCount(Long.parseLong(sinkWriteCount));
        long totalCost = (end - start) / 1000;
        jobMetrics.setTotalCost(totalCost < 0 ? 1L : totalCost);
        jobMetrics.setTenantCode(tenantCode);
        jobMetrics.setSubmitTime(new Date(start));
        jobMetrics.setStatus(jobStatus);
        jobMetrics.setInstanceId(instanceId);
        jobMetrics.setJobId(appid);
        jobMetrics.setPipelineId(1);
        jobMetrics.setStartDate(new Date(start));
        jobMetrics.setSourceVertextInfo("");
        jobMetrics.setSinkVertextInfo("");
        jobMetrics.setSourceReceivedBytesPerSeconds(sourceReceivedBytesPerSeconds);
        jobMetrics.setSinkWriteBytesPerSeconds(sinkWriteBytesPerSeconds);
        if (Arrays.asList("FAILING", "FAILED", "CANCELLING", "CANCELED", "FINISHED").contains(jobStatus)) {
            jobMetrics.setFinishTime(new Date(end));
        }
//        if (CollUtil.isNotEmpty(instanceIdMap)) {
//            //sendToKafka(t, instanceIdMap.get(instanceId));
//            CompletableFuture.runAsync(() -> sendToKafka(jobMetrics, instanceIdMap.get(instanceId)));
//        }
        jobMetrics.setSourceReceivedBytes(null != read.get("SourceReceivedBytes") ? read.getString("SourceReceivedBytes") : "0");
        jobMetrics.setSinkWriteBytes(null != read.get("SinkWriteBytes") ? read.getString("SinkWriteBytes") : "0");
        jobMetericsService.add(jobMetrics);
    }

    private void initTableIobMetrics(Set<String> keys,
                                     Map<String, Object> result,
                                     String tenantCode,
                                     String appid,
                                     String jobStatus,
                                     String instanceId,
                                     Map<String, Long> instanceIdMap,
                                     JSONObject sourceCountJson) {
        String createTime = result.get("createTime").toString();
        String finishTime = null != result.get("finishTime") ? result.get("finishTime").toString() : null;
        JSONObject read = (JSONObject) result.get("metrics");
        JSONObject tableSourceReceivedCount = read.getJSONObject("TableSourceReceivedCount");
        JSONObject tableSourceReceivedQPS = read.getJSONObject("TableSourceReceivedQPS");
        JSONObject tableSinkWriteQPS = read.getJSONObject("TableSinkWriteQPS");
        JSONObject tableSinkWriteCount = read.getJSONObject("TableSinkWriteCount");
        JSONObject tableSourceReceivedBytes = read.getJSONObject("TableSourceReceivedBytes");
        JSONObject tableSinkWriteBytes = read.getJSONObject("TableSinkWriteBytes");
        JSONObject tableSourceReceivedBytesPerSeconds = read.getJSONObject("TableSourceReceivedBytesPerSeconds");
        JSONObject tableSinkWriteBytesPerSeconds = read.getJSONObject("TableSinkWriteBytesPerSeconds");
        int pipelineId = 1;
        for (String key : keys) {
            String sourceReceivedBytesPerSeconds = null != tableSourceReceivedBytesPerSeconds.get(key) ? tableSourceReceivedBytesPerSeconds.get(key).toString().split("\\.")[0] : "0";
            String sinkWriteBytesPerSeconds = null != tableSinkWriteBytesPerSeconds.get(key) ? tableSinkWriteBytesPerSeconds.get(key).toString().split("\\.")[0] : "0";
            String sourceReceivedQPS = null != tableSourceReceivedQPS.get(key) ? tableSourceReceivedQPS.get(key).toString().split("\\.")[0] : "0";
            String sinkWriteQPS = null != tableSinkWriteQPS.get(key) ? tableSinkWriteQPS.get(key).toString().split("\\.")[0] : "0";
            long sourceReceivedCount = tableSourceReceivedCount.getLong(key);
            long sinkWriteCount = tableSinkWriteCount.getLong(key);
            EngineJobMetrics jobMetrics = new EngineJobMetrics();
            long end = getTime(finishTime);
            long start = getTime(createTime);
            jobMetrics.setReadQps(Long.parseLong(sourceReceivedQPS));
            jobMetrics.setReadRowCount(sourceReceivedCount);
            jobMetrics.setWriteQps(Long.parseLong(sinkWriteQPS));
            jobMetrics.setWriteRowCount(sinkWriteCount);
            long totalCost = (end - start) / 1000;
            jobMetrics.setTotalCost(totalCost < 0 ? 1L : totalCost);
            jobMetrics.setTenantCode(tenantCode);
            jobMetrics.setSubmitTime(new Date(start));
            jobMetrics.setStatus(jobStatus);
            jobMetrics.setInstanceId(instanceId);
            jobMetrics.setJobId(appid);
            //pipelineId在海豚中已经修改
            try {
                jobMetrics.setPipelineId(Integer.valueOf(key));
            } catch (Exception e) {
                jobMetrics.setPipelineId(pipelineId);
            }
            jobMetrics.setStartDate(new Date(start));
            jobMetrics.setSourceVertextInfo("");
            jobMetrics.setSinkVertextInfo("");
            jobMetrics.setSourceReceivedBytesPerSeconds(sourceReceivedBytesPerSeconds);
            jobMetrics.setSinkWriteBytesPerSeconds(sinkWriteBytesPerSeconds);
            jobMetrics.setSourceCount(sourceCountJson.getLong(key));
            if (Arrays.asList("FAILING", "FAILED", "CANCELLING", "CANCELED", "FINISHED").contains(jobStatus)) {
                jobMetrics.setFinishTime(new Date(end));
            }
//            if (CollUtil.isNotEmpty(instanceIdMap)) {
//                //sendToKafka(t, instanceIdMap.get(instanceId));
//                CompletableFuture.runAsync(() -> sendToKafka(jobMetrics, instanceIdMap.get(instanceId)));
//            }
            Object sourceReceivedBytes = tableSourceReceivedBytes.get(key);
            Object sinkWriteBytes = tableSinkWriteBytes.get(key);
            jobMetrics.setSourceReceivedBytes(null != sourceReceivedBytes ? sourceReceivedBytes.toString() : "0");
            jobMetrics.setSinkWriteBytes(null != sinkWriteBytes ? sinkWriteBytes.toString() : "0");
            jobMetericsService.add(jobMetrics);
            pipelineId += 1;
        }
    }

//    private void saveMeterics(String appid, String tenantCode, String instanceId, Map<String, Long> instanceIdMap, String host) {
//
//        Map<String, Object> result = EngineUtils.getMetericsMap(appid, host, httpRequestFeignService);
//        if (result.size() > 0) {
//            String createTime = result.get("createTime").toString();
//            String finishTime = null != result.get("finishTime") ? result.get("finishTime").toString() : null;
//            String jobStatus = result.get("jobStatus").toString();
//            JSONObject read = (JSONObject) result.get("metrics");
//            String sourceReceivedQPS = read.get("SourceReceivedQPS").toString().split("\\.")[0];
//            String sinkWriteQPS = read.get("SinkWriteQPS").toString().split("\\.")[0];
//            String sourceReceivedCount = read.get("SourceReceivedCount").toString();
//            String sinkWriteCount = read.get("SinkWriteCount").toString();
//            EngineJobMetrics jobMetrics = new EngineJobMetrics();
//            long end = getTime(finishTime);
//            long start = getTime(createTime);
//            jobMetrics.setReadQps(Long.valueOf(sourceReceivedQPS));
//            jobMetrics.setReadRowCount(Long.valueOf(sourceReceivedCount));
//            jobMetrics.setWriteQps(Long.valueOf(sinkWriteQPS));
//            jobMetrics.setWriteRowCount(Long.valueOf(sinkWriteCount));
//            jobMetrics.setTotalCost((end - start) / 1000);
//            jobMetrics.setTenantCode(tenantCode);
//            jobMetrics.setSubmitTime(new Date(start));
//            jobMetrics.setStatus(jobStatus);
//            jobMetrics.setInstanceId(instanceId);
//            jobMetrics.setJobId(appid);
//            jobMetrics.setPipelineId(1);
//            jobMetrics.setStartDate(new Date(Long.valueOf(start)));
//            jobMetrics.setSourceVertextInfo("");
//            jobMetrics.setSinkVertextInfo("");
//            if (Arrays.asList("FAILING", "FAILED", "CANCELLING", "CANCELED", "FINISHED").contains(jobStatus)) {
//                jobMetrics.setFinishTime(new Date(end));
//            }
//            if (CollUtil.isNotEmpty(instanceIdMap)) {
//                //sendToKafka(t, instanceIdMap.get(instanceId));
//                CompletableFuture.runAsync(() -> sendToKafka(jobMetrics, instanceIdMap.get(instanceId)));
//            }
//            jobMetrics.setSourceReceivedBytes(null != read.get("SourceReceivedBytes") ? read.getString("SourceReceivedBytes") : "0");
//            jobMetrics.setSinkWriteBytes(null != read.get("SinkWriteBytes") ? read.getString("SinkWriteBytes") : "0");
//            jobMetericsService.add(jobMetrics);
//            //直到最后一条运行中的结束 再删除rediskey
//            if (Arrays.asList("FAILING", "FAILED", "CANCELLING", "CANCELED", "FINISHED").contains(jobStatus)) {
//                redisTemplate.opsForHash().delete("DEDP:DS:TASK:SCHEDULER", tenantCode + ":" + instanceId);
//            }
//        }
//
//    }

    private long getTime(String date) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (null == date) {
            return System.currentTimeMillis();
        }
        try {
            return df.parse(date).getTime();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

//    private void sendToKafka(EngineJobMetrics engineJobMetrics, Long taskId) {
//        HashMap<Object, Object> map = new HashMap<>();
//        map.put("taskId", taskId);
//        map.put("data", engineJobMetrics);
//        kafkaTemplate.send(KAFKATOPIC, UUID.randomUUID().toString(), JSON.toJSONString(map));
//    }
}
