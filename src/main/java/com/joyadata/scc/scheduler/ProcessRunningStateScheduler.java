package com.joyadata.scc.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.scc.model.ProcessInstance;
import com.joyadata.scc.model.ProcessRunningTrend;
import com.joyadata.service.BaseServiceImpl;
import com.joyadata.tms.model.Project;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * 概览页面工作流运行趋势图预统计
 */
@Slf4j
@Component
@JoyaScheduledComponent
public class ProcessRunningStateScheduler {

    private final BaseServiceImpl baseServiceImpl;

    public ProcessRunningStateScheduler(BaseServiceImpl baseServiceImpl) {
        this.baseServiceImpl = baseServiceImpl;
    }

    private final JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);

    /**
     * 每半个小时统计一次
     */
    @JoyaScheduled(cron = "0 0/30 * * * ?", initialDelay = 30 * 1000)
    public void scheduler() throws ParseException {
        String systemToken = AuthUtil.getSystemToken(null);
        ThreadLocalUserUtil.setCode("token", systemToken);
        List<Project> projects = projectJoyaFeignService.getQuery().ignoreTenantCode().list();
        if (null != projects && !projects.isEmpty()) {
            //开始时间把秒设置为00
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
            Date date = new Date();
            Date nodeDate = dateFormat.parse(dateFormat.format(date));
            //计算30分钟前时间
            LocalDateTime lastStatTimeLocalTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().minus(30, ChronoUnit.MINUTES);
            Date lastStatTime = Date.from(lastStatTimeLocalTime.atZone(ZoneId.systemDefault()).toInstant());
            String time = dateFormat.format(lastStatTime);
            projects.forEach(project -> {
                String projectId = project.getId();
                String tenantCode = project.getTenantCode();
                String sql = "SELECT COUNT(*) AS num FROM scc_process_instance " +
                        "WHERE project_id = '%s' " +
                        "AND start_time < DATE_ADD('%s', INTERVAL 30 MINUTE)" +
                        "AND (end_time > '%s' OR end_time IS NULL) " +
                        "AND NOT (state IN(3,5,6,7,16)  AND end_time IS NULL)";
                JSONObject json = baseServiceImpl.getService(ProcessInstance.class).getSqlExecutor().excuteSelect(String.format(sql, projectId, time, time)).json();
                ProcessRunningTrend processRunningTrend = new ProcessRunningTrend();
                processRunningTrend.setNum(json.getInteger("num"));
                processRunningTrend.setTime(nodeDate);
                processRunningTrend.setProjectId(projectId);
                processRunningTrend.setTenantCode(tenantCode);
                baseServiceImpl.getService(ProcessRunningTrend.class).add(processRunningTrend);
            });
        }
    }
}
