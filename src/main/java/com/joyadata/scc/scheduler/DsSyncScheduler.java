package com.joyadata.scc.scheduler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Table;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.scc.redis.ProcessInstanceSaveStrategyRedisService;
import com.joyadata.service.mybatis.MybatisSqlService;
import com.joyadata.util.ApplicationContextHelp;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.TypeHandler;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 海豚数据同步
 */
@Slf4j
@Component
@JoyaScheduledComponent
public class DsSyncScheduler {
    private static final int BATCH_SIZE = 1000;
    private Timestamp ds_task_last = new Timestamp(1);
    private Timestamp scc_task_last = new Timestamp(1);
    private Timestamp ds_process_last = new Timestamp(1);
    private Timestamp scc_process_last = new Timestamp(1);
    private final Long min_last = new Timestamp(1).getTime();
    private List<String> processColumns;
    private List<TypeHandler<?>> processTypeHandlers;

    private List<String> taskColumns;
    private List<TypeHandler<?>> taskTypeHandlers;

    private final MybatisSqlService mybatisSqlService;
    private final ProcessInstanceSaveStrategyRedisService processInstanceSaveStrategyRedisService;
    private Connection connection = null;
    private Statement statement = null;
    private int times = -1;

    public DsSyncScheduler(MybatisSqlService mybatisSqlService, ProcessInstanceSaveStrategyRedisService processInstanceSaveStrategyRedisService) throws SQLException {
        this.mybatisSqlService = mybatisSqlService;
        this.processInstanceSaveStrategyRedisService = processInstanceSaveStrategyRedisService;
    }

    @JoyaScheduled(initialDelay = 10 * 1000, fixedRate = 5 * 1000)
    public void run() throws SQLException, ClassNotFoundException, JsonProcessingException {
        times++;
        Connection connection = getConnection();
        String s1 = getTimeTimestampStr(ds_task_last);
        String s2 = getTimeTimestampStr(scc_task_last);
        String s3 = getTimeTimestampStr(ds_process_last);
        String s4 = getTimeTimestampStr(scc_process_last);
        //1 获取数据最后更新时间
        refreshLastTime(connection, s1, s2, s3, s4);
        log.info(" ds_task_last:{}", ds_task_last);
        log.info("scc_task_last:{}", scc_task_last);
        log.info(" ds_process_last:{}", ds_process_last);
        log.info("scc_process_last:{}", scc_process_last);


        //2、 更新工作流
        if (ds_process_last.getTime() != scc_process_last.getTime() || min_last.equals(scc_process_last.getTime())) {
            syncProcess(getConnection(), scc_process_last);
        }

        //3 、更新任务实例
        if (ds_task_last.getTime() != scc_task_last.getTime() || min_last.equals(scc_task_last.getTime())) {
            syncTask(getConnection(), scc_task_last);
        }
        // 4 删除过期数据
        if (times % 720 == 0) {
            // 每个小时执行一次
            clearProcess(connection);
            times = 0;
        }

    }

    private void syncProcess(Connection connection, Timestamp timesmtap) throws SQLException, JsonProcessingException, ClassNotFoundException {
        try {
            Statement queryStatement = getStatement(connection);
            ResultSet queryResultSet = queryStatement.executeQuery(getQuerySql(DsSyncScheduler.PROCESS_QUERY_SQL, timesmtap));
            //元信息
            List<String> columns = getProcessQueryColumns(queryResultSet);
            List<TypeHandler<?>> typeHandlers = getProcessQueryTypeHandlers(queryResultSet);
            // 插入的对象
            int rowNum = addBatch(connection, queryResultSet, DsSyncScheduler.PROCESS_INSERT_SQL, columns, typeHandlers);
            log.info("sync process 同步数据{}条", rowNum);
            queryResultSet.close();
        } catch (Exception e) {
            log.error("同步工作流实例失败{}", e.getMessage(), e);
        }

    }

    private void syncTask(Connection connection, Timestamp timesmtap) throws SQLException, JsonProcessingException, ClassNotFoundException {
        try {
            Statement queryStatement = getStatement(connection);
            ResultSet queryResultSet = queryStatement.executeQuery(getQuerySql(DsSyncScheduler.TASK_QUERY_SQL, timesmtap));
            //元信息
            List<String> columns = getTaskQueryColumns(queryResultSet);
            List<TypeHandler<?>> typeHandlers = getTaskQueryTypeHandlers(queryResultSet);
            // 插入的对象
            int rowNum = addBatch(connection, queryResultSet, DsSyncScheduler.TASK_INSERT_SQL, columns, typeHandlers);
            log.info("sync task 同步数据{}条", rowNum);
            queryResultSet.close();
        } catch (Exception e) {
            log.error("同步任务实例流失败{}", e.getMessage(), e);
        }

    }

    private int addBatch(Connection connection, ResultSet queryResultSet, String insertSql,
                         List<String> columns, List<TypeHandler<?>> typeHandlers) throws SQLException, JsonProcessingException {
        // 插入的对象
        PreparedStatement insertPreparedStatement = connection.prepareStatement(insertSql);
        // 边读边写
        int rowNum = 0;
        while (queryResultSet.next()) {
            JSONObject resultOneRow = mybatisSqlService.getResultOneRow(queryResultSet, columns, typeHandlers);
            //2025-08-15任务实例加ac_date
            // 解析并添加 ac_date
            String acDate = parseAcDateFromGlobalParams(resultOneRow.getString("global_params"));
            resultOneRow.put("ac_date", acDate);
            Collection<Object> values = resultOneRow.values();
            mybatisSqlService.setParameters(insertPreparedStatement, values);
            insertPreparedStatement.addBatch();
            rowNum++;
            if (rowNum > 0 & rowNum % BATCH_SIZE == 0) {
                insertPreparedStatement.executeBatch();
            }
        }
        insertPreparedStatement.executeBatch();
        insertPreparedStatement.close();
        return rowNum;
    }

    private String parseAcDateFromGlobalParams(String globalParams) {
        if (globalParams == null || globalParams.trim().isEmpty()) {
            return null;
        }

        try {
            JSONArray arr = JSONArray.parseArray(globalParams);
            for (int i = 0; i < arr.size(); i++) {
                JSONObject obj = arr.getJSONObject(i);
                String prop = obj.getString("prop");
                if ("ac_date".equalsIgnoreCase(prop)) {
                    return obj.getString("value");
                }
            }
        } catch (Exception e) {
            log.error("解析 global_params 中的 ac_date 失败: {}, globalParams: {}", e.getMessage(), globalParams);
        }
        return null;
    }

    private List<String> getProcessQueryColumns(ResultSet queryResultSet) throws SQLException {
        if (null == this.processColumns) {
            this.processColumns = mybatisSqlService.getResultSetColumns(queryResultSet);
        }
        return this.processColumns;
    }

    private List<TypeHandler<?>> getProcessQueryTypeHandlers(ResultSet queryResultSet) throws SQLException {
        if (null == this.processTypeHandlers) {
            this.processTypeHandlers = mybatisSqlService.getTypeHandlers(queryResultSet);
        }
        return this.processTypeHandlers;
    }

    private List<String> getTaskQueryColumns(ResultSet queryResultSet) throws SQLException {
        if (null == this.taskColumns) {
            this.taskColumns = mybatisSqlService.getResultSetColumns(queryResultSet);
        }
        return this.taskColumns;
    }

    private List<TypeHandler<?>> getTaskQueryTypeHandlers(ResultSet queryResultSet) throws SQLException {
        if (null == this.taskTypeHandlers) {
            this.taskTypeHandlers = mybatisSqlService.getTypeHandlers(queryResultSet);
        }
        return this.taskTypeHandlers;
    }


    private String getQuerySql(String sql, Timestamp timestamp) {
        return String.format(sql, getTimeTimestampStr(timestamp));
    }

    private String getTimeTimestampStr(Timestamp timestamp) {
        // 定义日期时间格式  mysql对应的是 '%Y-%m-%d %H:%i:%s.%f'
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");
        return timestamp.toLocalDateTime().format(formatter);
    }

    private void refreshLastTime(Connection connection, String s1, String s2, String s3, String s4) throws SQLException, ClassNotFoundException {
        ResultSet resultSet;
        String sql;
        if (s1.startsWith("1970-01-01")) {
            sql = getLastSql(s1, s2, s3, s4, false);
        } else {
            sql = getLastSql(s1, s2, s3, s4, true);
        }
        resultSet = getStatement(connection).executeQuery(sql);
        if (resultSet.next()) {
            try {
                ds_task_last = resultSet.getTimestamp("ds_task_last");
                scc_task_last = resultSet.getTimestamp("scc_task_last");
                ds_process_last = resultSet.getTimestamp("ds_process_last");
                scc_process_last = resultSet.getTimestamp("scc_process_last");
            } catch (Exception ignored) {
            }
        }
        if (null == ds_task_last) {
            ds_task_last = new Timestamp(1);
        }
        if (null == scc_task_last) {
            scc_task_last = new Timestamp(0);
        }
        if (null == ds_process_last) {
            ds_process_last = new Timestamp(1);
        }
        if (null == scc_process_last) {
            scc_process_last = new Timestamp(0);
        }
        resultSet.close();
    }

    private Connection getConnection() throws SQLException, ClassNotFoundException {
        if (null != connection && !connection.isClosed()) {
            return connection;
        } else {
            connection = ApplicationContextHelp.getOrgJdbcConnection();
        }
        return connection;
    }

    private Statement getStatement(Connection connection) throws SQLException, ClassNotFoundException {
        if (null == statement || statement.isClosed()) {
            statement = connection.createStatement();
        }
        return statement;
    }

    /**
     * 删除过期的工作流
     */
    private void clearProcess(Connection connection) throws SQLException, ClassNotFoundException {
        Table<String, String, Integer> table = this.processInstanceSaveStrategyRedisService.getProjectIdDayMap();

        Set<String> projectIds = table.rowKeySet();
        if (!projectIds.isEmpty()) {
            for (String projectId : projectIds) {
                try {
                    Integer day = table.get(projectId, "day");
                    if (null != day) {
                        //Integer strip = table.get(projectId, "strip");
                        String startDate = LocalDateTime.now().minusDays(day).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        boolean execute = getStatement(connection).execute(String.format(DELETE_BY_DAY_T_DS_PROCESS_INSTANCE, projectId, startDate));
                        log.info("projectId:{} 数据清理：{}, 清理数据日期：{}", projectId, execute, startDate);
                    }
                    // TODO 查询实例条数，删除超出条数的记录
                } catch (Exception e) {
                    log.error("删除T_DS_PROCESS_INSTANCE失败:{}", e.getMessage(), e);
                }
            }
            Boolean execute = null;
            Boolean execute1 = null;
            Boolean execute2 = null;
            Boolean execute3 = null;
//            try {
//                execute = getStatement(connection).execute(DELETE_T_DS_TASK_MONITOR);
//            } catch (Exception e) {
//                log.error("删除T_DS_TASK_MONITOR失败:{}", e.getMessage(), e);
//            }
            try {
                execute1 = getStatement(connection).execute(DELETE_T_DS_TASK_INSTANCE);
            } catch (Exception e) {
                log.error("删除T_DS_TASK_INSTANCE失败:{}", e.getMessage(), e);
            }

            try {
                execute2 = getStatement(connection).execute(DELETE_SCC_PROCESS_INSTANCE);
            } catch (Exception e) {
                log.error("删除SCC_PROCESS_INSTANCE失败:{}", e.getMessage(), e);
            }

            try {
                execute3 = getStatement(connection).execute(DELETE_SCC_TASK_INSTANCE);
            } catch (Exception e) {
                log.error("删除SCC_TASK_INSTANCE失败:{}", e.getMessage(), e);
            }
            log.info("清理完成: T_DS_TASK_MONITOR:{}, T_DS_TASK_INSTANCE：{} ,SCC_PROCESS_INSTANCE:{}, SCC_TASK_INSTANCE:{}", execute, execute1, execute2, execute3);
        }
    }


    private static final String delete_by_strip_t_ds_process_instance = "delete  from  business_ds.t_ds_process_instance where process_definition_code = '%s'  order by command_start_time  limit %d";

    private static final String DELETE_BY_DAY_T_DS_PROCESS_INSTANCE = "delete " +
            "from " +
            "  business_ds.t_ds_process_instance " +
            "where " +
            "  process_definition_code in ( " +
            "  select " +
            "  code " +
            " from" +
            "  business_ds.t_ds_process_definition " +
            "   where" +
            "  business_ds.t_ds_process_definition.project_code = '%s') " +
            "and business_ds.t_ds_process_instance.command_start_time <= '%s' " +
            "limit 5000";


    private static final String DELETE_T_DS_TASK_MONITOR = "DELETE FROM business_ds.t_ds_task_monitor " +
            "WHERE NOT EXISTS ( " +
            "    SELECT 1 FROM business_ds.t_ds_task_instance " +
            "    WHERE id = business_ds.t_ds_task_monitor.task_instance_id " +
            ") " +
            "LIMIT 5000 ";

    private static final String DELETE_T_DS_TASK_INSTANCE = "DELETE FROM business_ds.t_ds_task_instance " +
            "WHERE NOT EXISTS ( " +
            "    SELECT 1 FROM business_ds.t_ds_process_instance " +
            "    WHERE business_ds.t_ds_process_instance .id = business_ds.t_ds_task_instance.process_instance_id " +
            ") " +
            "LIMIT 5000 ";

    private static final String DELETE_SCC_PROCESS_INSTANCE = "DELETE FROM business_scc.scc_process_instance " +
            "WHERE NOT EXISTS ( " +
            "    SELECT 1 FROM business_ds.t_ds_process_instance " +
            "    WHERE id = business_scc.scc_process_instance.id " +
            ") " +
            "LIMIT 5000 ";

    private static final String DELETE_SCC_TASK_INSTANCE = "DELETE FROM business_scc.scc_task_instance " +
            "WHERE NOT EXISTS ( " +
            "    SELECT 1 FROM business_ds.t_ds_process_instance " +
            "    WHERE id = business_scc.scc_task_instance.process_instance_id " +
            ") " +
            "LIMIT 5000 ";


    private static String getLastSql(String s1, String s2, String s3, String s4, boolean falg) {
        if (falg) {
            return "SELECT " +
                    "    (SELECT MAX(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM `business_ds`.`t_ds_task_instance`   " +
                    "     WHERE update_time >= '" + s1 + "') AS ds_task_last,  " +
                    "      " +
                    "    (SELECT MAX(DATE_FORMAT(last_modification_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM business_scc.scc_task_instance   " +
                    "     WHERE last_modification_time >= '" + s2 + "') AS scc_task_last,  " +
                    "      " +
                    "    (SELECT MAX(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM business_ds.t_ds_process_instance   " +
                    "     WHERE update_time >= '" + s3 + "') AS ds_process_last,  " +
                    "      " +
                    "    (SELECT MAX(DATE_FORMAT(last_modification_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM business_scc.scc_process_instance   " +
                    "     WHERE last_modification_time >= '" + s4 + "') AS scc_process_last";
        } else {
            return "SELECT " +
                    "    (SELECT MAX(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM `business_ds`.`t_ds_task_instance`   " +
                    "     ) AS ds_task_last,  " +
                    "      " +
                    "    (SELECT MAX(DATE_FORMAT(last_modification_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM business_scc.scc_task_instance   " +
                    "     ) AS scc_task_last,  " +
                    "      " +
                    "    (SELECT MAX(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM business_ds.t_ds_process_instance   " +
                    "     ) AS ds_process_last,  " +
                    "      " +
                    "    (SELECT MAX(DATE_FORMAT(last_modification_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
                    "     FROM business_scc.scc_process_instance   " +
                    "    ) AS scc_process_last";
        }

    }

//    private static final String LAST_SQL = "SELECT " +
//            "    (SELECT MAX(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
//            "     FROM `business_ds`.`t_ds_task_instance`   " +
//            "     WHERE update_time >= '%s') AS ds_task_last,  " +
//            "      " +
//            "    (SELECT MAX(DATE_FORMAT(last_modification_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
//            "     FROM business_scc.scc_task_instance   " +
//            "     WHERE last_modification_time >= '%s') AS scc_task_last,  " +
//            "      " +
//            "    (SELECT MAX(DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
//            "     FROM business_ds.t_ds_process_instance   " +
//            "     WHERE update_time >= '%s') AS ds_process_last,  " +
//            "      " +
//            "    (SELECT MAX(DATE_FORMAT(last_modification_time, '%Y-%m-%d %H:%i:%s.%f'))   " +
//            "     FROM business_scc.scc_process_instance   " +
//            "     WHERE last_modification_time >= '%s') AS scc_process_last";

    private static final String TASK_NAME_SQL = "(" +
            "        SELECT GROUP_CONCAT(st.name SEPARATOR ',')" +
            "        FROM `business_scc`.scc_task st" +
            "        where st.tenant_code = `business_scc`.`scc_process_definition`.tenant_code and  st.project=`business_scc`.`scc_process_definition`.project and  FIND_IN_SET(st.id, `business_scc`.`scc_process_definition`.task_ids) > 0" +
            "    ) AS task_names, ";

    private static final String PROCESS_QUERY_SQL = "select   " +
            "    `business_ds`.`t_ds_process_instance`.`id` as `id`,   " +
            "    `business_ds`.`t_ds_process_instance`.`name` as `name`, " +
            "    `business_ds`.`t_ds_process_instance`.`process_definition_code` as `process_definition_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`process_definition_version` as `process_definition_version`, " +
            "    `business_ds`.`t_ds_process_instance`.`state` as `state`, " +
            "    `business_ds`.`t_ds_process_instance`.`state_history` as `state_history`, " +
            "    `business_ds`.`t_ds_process_instance`.`recovery` as `recovery`, " +
            "    `business_ds`.`t_ds_process_instance`.`start_time` as `start_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`end_time` as `end_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`run_times` as `run_times`, " +
            "    `business_ds`.`t_ds_process_instance`.`host` as `host`, " +
            "    `business_ds`.`t_ds_process_instance`.`command_type` as `command_type`, " +
            "    `business_ds`.`t_ds_process_instance`.`command_param` as `command_param`, " +
            "    `business_ds`.`t_ds_process_instance`.`task_depend_type` as `task_depend_type`, " +
            "    `business_ds`.`t_ds_process_instance`.`max_try_times` as `max_try_times`, " +
            "    `business_ds`.`t_ds_process_instance`.`failure_strategy` as `failure_strategy`, " +
            "    `business_ds`.`t_ds_process_instance`.`warning_type` as `warning_type`, " +
            "    `business_ds`.`t_ds_process_instance`.`warning_group_id` as `warning_group_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`schedule_time` as `schedule_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`command_start_time` as `command_start_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`global_params` as `global_params`, " +
            "    `business_ds`.`t_ds_process_instance`.`is_sub_process` as `is_sub_process`, " +
            "    `business_ds`.`t_ds_process_instance`.`executor_id` as `executor_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`history_cmd` as `history_cmd`, " +
            "    `business_ds`.`t_ds_process_instance`.`process_instance_priority` as `process_instance_priority`, " +
            "    `business_ds`.`t_ds_process_instance`.`worker_group` as `worker_group`, " +
            "    `business_ds`.`t_ds_process_instance`.`environment_code` as `environment_code`, " +
            "    `business_ds`.`t_ds_process_instance`.`timeout` as `timeout`, " +
            "    `business_ds`.`t_ds_process_instance`.`tenant_id` as `tenant_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`var_pool` as `var_pool`, " +
            "    `business_ds`.`t_ds_process_instance`.`dry_run` as `dry_run`, " +
            "    `business_ds`.`t_ds_process_instance`.`next_process_instance_id` as `next_process_instance_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`restart_time` as `restart_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`test_flag` as `test_flag`, " +
            "    `business_ds`.`t_ds_process_instance`.`calendar_id` as `calendar_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`start_time` as `create_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`update_time` as `last_modification_time`, " +
            "    `business_ds`.`t_ds_process_instance`.`catalog_id` as `catalog_id`, " +
            "    `business_ds`.`t_ds_process_instance`.`catalog_parent_ids` as `catalog_parent_ids`, " +
            "    `business_scc`.`scc_process_definition`.`tenant_code` as `tenant_code`, " +
            "    `business_scc`.`scc_process_definition`.`create_by` as `create_by`, " +
            "    `business_scc`.`scc_process_definition`.`create_by_name` as `create_by_name`, " +
            "    `business_scc`.`scc_process_definition`.`is_public` as `is_public`, " +
            "    `business_scc`.`scc_process_definition`.`pos` as `pos`, " +
            "    `business_ds`.`t_ds_process_instance`.`id` as `dbid`, " +
            "    `business_scc`.`scc_process_definition`.`update_by` as `update_by`, " +
            "    `business_scc`.`scc_process_definition`.`del_flag` as `del_flag`, " +
            "    `business_scc`.`scc_process_definition`.`project` as `project`, " +
            "    `business_scc`.`scc_process_definition`.`data_owner_dept_id` as `data_owner_dept_id`, " +
            "    `business_scc`.`scc_process_definition`.`data_owner_dept_name` as `data_owner_dept_name`, " +
            "    `business_scc`.`scc_process_definition`.`data_owner_user_id` as `data_owner_user_id`, " +
            "    `business_scc`.`scc_process_definition`.`data_owner_user_name` as `data_owner_user_name`, " +
            "    `business_scc`.`scc_process_definition`.`readonly` as `readonly`, " +
            "    `business_scc`.`scc_process_definition`.`project_id` as `project_id`, " +
            "    `business_scc`.`scc_process_definition`.`product_id` as `product_id`, " +
            "    `business_scc`.`scc_process_definition`.`task_ids` as `task_ids`, " +
            "    `business_scc`.`scc_process_definition`.`name` as `process_definition_name`, " +
            TASK_NAME_SQL +
            " '' AS `ac_date` " +
            "from " +
            "    (`business_ds`.`t_ds_process_instance` " +
            "left join `business_scc`.`scc_process_definition` on " +
            "    ((`business_ds`.`t_ds_process_instance`.`process_definition_code` = `business_scc`.`scc_process_definition`.`id`))) " +
            "where " +
            "    (`business_scc`.`scc_process_definition`.`product_id` is not null) and  `business_ds`.`t_ds_process_instance`.`update_time` > '%s' " +
            "group by " +
            "    `business_ds`.`t_ds_process_instance`.`id` ORDER BY `business_ds`.`t_ds_process_instance`.`update_time` LIMIT 0," + BATCH_SIZE;


    private static final String PROCESS_INSERT_SQL = "INSERT INTO business_scc.scc_process_instance (id, name, process_definition_id, process_definition_version, state, state_history, recovery, start_time, end_time, run_times, host, command_type, command_param, task_depend_type, max_try_times, failure_strategy, warning_type, warning_group_id, schedule_time, command_start_time, global_params, is_sub_process, executor_id, history_cmd, process_instance_priority, worker_group, environment_code, timeout, tenant_id, var_pool, dry_run, next_process_instance_id, restart_time, test_flag, calendar_id, create_time, last_modification_time, catalog_id, catalog_parent_ids, tenant_code, create_by, create_by_name, is_public, pos, dbid, update_by, del_flag, project, data_owner_dept_id, data_owner_dept_name, data_owner_user_id, data_owner_user_name, readonly, project_id, product_id, task_ids, process_definition_name, task_names, ac_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE " +
            "name = VALUES(name), " +
            "process_definition_id = VALUES(process_definition_id), " +
            "process_definition_version = VALUES(process_definition_version), " +
            "state = VALUES(state), " +
            "state_history = VALUES(state_history), " +
            "recovery = VALUES(recovery), " +
            "start_time = VALUES(start_time), " +
            "end_time = VALUES(end_time), " +
            "run_times = VALUES(run_times), " +
            "host = VALUES(host), " +
            "command_type = VALUES(command_type), " +
            "command_param = VALUES(command_param), " +
            "task_depend_type = VALUES(task_depend_type), " +
            "max_try_times = VALUES(max_try_times), " +
            "failure_strategy = VALUES(failure_strategy), " +
            "warning_type = VALUES(warning_type), " +
            "warning_group_id = VALUES(warning_group_id), " +
            "schedule_time = VALUES(schedule_time), " +
            "command_start_time = VALUES(command_start_time), " +
            "global_params = VALUES(global_params), " +
            "is_sub_process = VALUES(is_sub_process), " +
            "executor_id = VALUES(executor_id), " +
            "history_cmd = VALUES(history_cmd), " +
            "process_instance_priority = VALUES(process_instance_priority), " +
            "worker_group = VALUES(worker_group), " +
            "environment_code = VALUES(environment_code), " +
            "timeout = VALUES(timeout), " +
            "tenant_id = VALUES(tenant_id), " +
            "var_pool = VALUES(var_pool), " +
            "dry_run = VALUES(dry_run), " +
            "next_process_instance_id = VALUES(next_process_instance_id), " +
            "restart_time = VALUES(restart_time), " +
            "test_flag = VALUES(test_flag), " +
            "calendar_id = VALUES(calendar_id), " +
            "create_time = VALUES(create_time), " +
            "last_modification_time = VALUES(last_modification_time), " +
            "catalog_id = VALUES(catalog_id), " +
            "catalog_parent_ids = VALUES(catalog_parent_ids), " +
            "tenant_code = VALUES(tenant_code), " +
            "create_by = VALUES(create_by), " +
            "create_by_name = VALUES(create_by_name), " +
            "is_public = VALUES(is_public), " +
            "pos = VALUES(pos), " +
            "update_by = VALUES(update_by), " +
            "del_flag = VALUES(del_flag), " +
            "project = VALUES(project), " +
            "data_owner_dept_id = VALUES(data_owner_dept_id), " +
            "data_owner_dept_name = VALUES(data_owner_dept_name), " +
            "data_owner_user_id = VALUES(data_owner_user_id), " +
            "data_owner_user_name = VALUES(data_owner_user_name), " +
            "readonly = VALUES(readonly), " +
            "project_id = VALUES(project_id), " +
            "product_id = VALUES(product_id), " +
            "task_ids = VALUES(task_ids), " +
            "process_definition_name = VALUES(process_definition_name)," +
            "task_names = VALUES(task_names)," +
            "ac_date = VALUES(ac_date);";

    private static final String CATALOG_PARENT_IDS_SQL = "(select sc.parent_ids from`business_scc`.scc_catalog sc where sc.tenant_code = `business_scc`.`scc_task`.tenant_code and sc.project = `business_scc`.`scc_task`.project and sc.id = `business_scc`.`scc_task`.catalog_id ) as catalog_parent_ids, ";

    private static final String TASK_QUERY_SQL = "select " +
            "    `business_ds`.`t_ds_task_instance`.`id` as `id`, " +
            "    `business_ds`.`t_ds_task_instance`.`name` as `name`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_type` as `task_type`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_execute_type` as `task_execute_type`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_code` as `task_id`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_definition_version` as `task_version`, " +
            "    `business_ds`.`t_ds_task_instance`.`process_instance_id` as `process_instance_id`, " +
            "    `business_ds`.`t_ds_task_instance`.`state` as `state`, " +
            "    `business_ds`.`t_ds_task_instance`.`submit_time` as `submit_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`start_time` as `start_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`end_time` as `end_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`host` as `host`, " +
            "    `business_ds`.`t_ds_task_instance`.`execute_path` as `execute_path`, " +
            "    `business_ds`.`t_ds_task_instance`.`log_path` as `log_path`, " +
            "    `business_ds`.`t_ds_task_instance`.`alert_flag` as `alert_flag`, " +
            "    `business_ds`.`t_ds_task_instance`.`retry_times` as `retry_times`, " +
            "    `business_ds`.`t_ds_task_instance`.`pid` as `pid`, " +
            "    `business_ds`.`t_ds_task_instance`.`app_link` as `app_link`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_params` as `task_params`, " +
            "    `business_ds`.`t_ds_task_instance`.`flag` as `flag`, " +
            "    `business_ds`.`t_ds_task_instance`.`retry_interval` as `retry_interval`, " +
            "    `business_ds`.`t_ds_task_instance`.`max_retry_times` as `max_retry_times`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_instance_priority` as `task_instance_priority`, " +
            "    `business_ds`.`t_ds_task_instance`.`worker_group` as `worker_group`, " +
            "    `business_ds`.`t_ds_task_instance`.`environment_code` as `environment_code`, " +
            "    `business_ds`.`t_ds_task_instance`.`executor_id` as `executor_id`, " +
            "    `business_ds`.`t_ds_task_instance`.`environment_config` as `environment_config`, " +
            "    `business_ds`.`t_ds_task_instance`.`first_submit_time` as `first_submit_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`delay_time` as `delay_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`var_pool` as `var_pool`, " +
            "    `business_ds`.`t_ds_task_instance`.`task_group_id` as `task_group_id`, " +
            "    `business_ds`.`t_ds_task_instance`.`cpu_quota` as `cpu_quota`, " +
            "    `business_ds`.`t_ds_task_instance`.`dry_run` as `dry_run`, " +
            "    `business_ds`.`t_ds_task_instance`.`memory_max` as `memory_max`, " +
            "    `business_ds`.`t_ds_task_instance`.`test_flag` as `test_flag`, " +
            "    `business_ds`.`t_ds_task_instance`.`start_time` as `create_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`update_time` as `last_modification_time`, " +
            "    `business_ds`.`t_ds_task_instance`.`error_strategy` as `error_strategy`, " +
            "    `business_ds`.`t_ds_process_instance`.`run_times` as `run_times`, " +
            "    `business_ds`.`t_ds_process_instance`.`name` as `process_instance_name`, " +
            "    `business_ds`.`t_ds_process_instance`.`global_params` as `global_params`, " +
            "    `business_scc`.`scc_task`.`tenant_code` as `tenant_code`, " +
            "    `business_scc`.`scc_task`.`create_by` as `create_by`, " +
            "    `business_scc`.`scc_task`.`create_by_name` as `create_by_name`, " +
            "    `business_scc`.`scc_task`.`is_public` as `is_public`, " +
            "    `business_scc`.`scc_task`.`pos` as `pos`, " +
            "    `business_ds`.`t_ds_task_instance`.`id` as `dbid`, " +
            "    `business_scc`.`scc_task`.`update_by` as `update_by`, " +
            "    `business_scc`.`scc_task`.`del_flag` as `del_flag`, " +
            "    `business_scc`.`scc_task`.`project` as `project`, " +
            "    `business_scc`.`scc_task`.`data_owner_dept_id` as `data_owner_dept_id`, " +
            "    `business_scc`.`scc_task`.`data_owner_dept_name` as `data_owner_dept_name`, " +
            "    `business_scc`.`scc_task`.`data_owner_user_id` as `data_owner_user_id`, " +
            "    `business_scc`.`scc_task`.`data_owner_user_name` as `data_owner_user_name`, " +
            "    `business_scc`.`scc_task`.`readonly` as `readonly`, " +
            "    `business_scc`.`scc_task`.`project_id` as `project_id`, " +
            "    `business_scc`.`scc_task`.`product_id` as `product_id`, " +
            "    `business_scc`.`scc_task`.`catalog_id` as `catalog_id`, " +
            CATALOG_PARENT_IDS_SQL +
            " '' AS `ac_date` " +
            "from " +
            "    ((`business_ds`.`t_ds_task_instance` " +
            "        left join `business_scc`.`scc_task` on " +
            "            ((concat(`business_ds`.`t_ds_task_instance`.`task_code`, '') = `business_scc`.`scc_task`.`id`))) " +
            "        left join `business_ds`.`t_ds_process_instance` on " +
            "        (((`business_ds`.`t_ds_process_instance`.`id` = `business_ds`.`t_ds_task_instance`.`process_instance_id`) " +
            "            and (`business_scc`.`scc_task`.`process_definition_code` is not null)))) " +
            "where " +
            "    (`business_scc`.`scc_task`.`product_id` is not null) " +
            " AND  business_ds.t_ds_task_instance.update_time > '%s' ";


    private static final String TASK_INSERT_SQL = "INSERT INTO `business_scc`.`scc_task_instance` ( " +
            "`id`, " +
            "`name`, " +
            "`task_type`, " +
            "`task_execute_type`, " +
            "`task_id`, " +
            "`task_version`, " +
            "`process_instance_id`, " +
            "`state`, " +
            "`submit_time`, " +
            "`start_time`, " +
            "`end_time`, " +
            "`host`, " +
            "`execute_path`, " +
            "`log_path`, " +
            "`alert_flag`, " +
            "`retry_times`, " +
            "`pid`, " +
            "`app_link`, " +
            "`task_params`, " +
            "`flag`, " +
            "`retry_interval`, " +
            "`max_retry_times`, " +
            "`task_instance_priority`, " +
            "`worker_group`, " +
            "`environment_code`, " +
            "`executor_id`, " +
            "`environment_config`, " +
            "`first_submit_time`, " +
            "`delay_time`, " +
            "`var_pool`, " +
            "`task_group_id`, " +
            "`cpu_quota`, " +
            "`dry_run`, " +
            "`memory_max`, " +
            "`test_flag`, " +
            "`create_time`, " +
            "`last_modification_time`, " +
            "`error_strategy`, " +
            "`run_times`, " +
            "`process_instance_name`, " +
            "`global_params`, " +
            "`tenant_code`, " +
            "`create_by`, " +
            "`create_by_name`, " +
            "`is_public`, " +
            "`pos`, " +
            "`dbid`, " +
            "`update_by`, " +
            "`del_flag`, " +
            "`project`, " +
            "`data_owner_dept_id`, " +
            "`data_owner_dept_name`, " +
            "`data_owner_user_id`, " +
            "`data_owner_user_name`, " +
            "`readonly`, " +
            "`project_id`, " +
            "`product_id`, " +
            "`catalog_id`," +
            "`catalog_parent_ids`, " +
            "`ac_date`" +
            ") VALUES ( " +
            "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
            "    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?" +
            ") ON DUPLICATE KEY UPDATE " +
            "    `name` = VALUES(`name`), " +
            "    `task_type` = VALUES(`task_type`), " +
            "    `task_execute_type` = VALUES(`task_execute_type`), " +
            "    `task_version` = VALUES(`task_version`), " +
            "    `state` = VALUES(`state`), " +
            "    `submit_time` = VALUES(`submit_time`), " +
            "    `start_time` = VALUES(`start_time`), " +
            "    `end_time` = VALUES(`end_time`), " +
            "    `host` = VALUES(`host`), " +
            "    `execute_path` = VALUES(`execute_path`), " +
            "    `log_path` = VALUES(`log_path`), " +
            "    `alert_flag` = VALUES(`alert_flag`), " +
            "    `retry_times` = VALUES(`retry_times`), " +
            "    `pid` = VALUES(`pid`), " +
            "    `app_link` = VALUES(`app_link`), " +
            "    `task_params` = VALUES(`task_params`), " +
            "    `flag` = VALUES(`flag`), " +
            "    `retry_interval` = VALUES(`retry_interval`), " +
            "    `max_retry_times` = VALUES(`max_retry_times`), " +
            "    `task_instance_priority` = VALUES(`task_instance_priority`), " +
            "    `worker_group` = VALUES(`worker_group`), " +
            "    `environment_code` = VALUES(`environment_code`), " +
            "    `executor_id` = VALUES(`executor_id`), " +
            "    `environment_config` = VALUES(`environment_config`), " +
            "    `first_submit_time` = VALUES(`first_submit_time`), " +
            "    `delay_time` = VALUES(`delay_time`), " +
            "    `var_pool` = VALUES(`var_pool`), " +
            "    `task_group_id` = VALUES(`task_group_id`), " +
            "    `cpu_quota` = VALUES(`cpu_quota`), " +
            "    `dry_run` = VALUES(`dry_run`), " +
            "    `memory_max` = VALUES(`memory_max`), " +
            "    `test_flag` = VALUES(`test_flag`), " +
            "    `create_time` = VALUES(`create_time`), " +
            "    `last_modification_time` = VALUES(`last_modification_time`), " +
            "    `error_strategy` = VALUES(`error_strategy`), " +
            "    `tenant_code` = VALUES(`tenant_code`), " +
            "    `create_by` = VALUES(`create_by`), " +
            "    `create_by_name` = VALUES(`create_by_name`), " +
            "    `is_public` = VALUES(`is_public`), " +
            "    `pos` = VALUES(`pos`), " +
            "    `update_by` = VALUES(`update_by`), " +
            "    `del_flag` = VALUES(`del_flag`), " +
            "    `project` = VALUES(`project`), " +
            "    `data_owner_dept_id` = VALUES(`data_owner_dept_id`), " +
            "    `data_owner_dept_name` = VALUES(`data_owner_dept_name`), " +
            "    `data_owner_user_id` = VALUES(`data_owner_user_id`), " +
            "    `data_owner_user_name` = VALUES(`data_owner_user_name`), " +
            "    `readonly` = VALUES(`readonly`), " +
            "    `project_id` = VALUES(`project_id`), " +
            "    `product_id` = VALUES(`product_id`), " +
            "    `catalog_id` = VALUES(`catalog_id`), " +
            "    `run_times` = VALUES(`run_times`), " +
            "    `process_instance_name` = VALUES(`process_instance_name`)," +
            "    `catalog_parent_ids` = VALUES(`catalog_parent_ids`)," +
            "    `ac_date` = VALUES(`ac_date`);";
}
