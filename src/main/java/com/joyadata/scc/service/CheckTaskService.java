package com.joyadata.scc.service;

import com.joyadata.scc.model.CheckTask;
import com.joyadata.scc.model.Task;
import com.joyadata.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CheckTaskService extends BaseService<CheckTask> {

    @Autowired
    private ProcessTaskRelationService processTaskRelationService;
    @Autowired
    private TaskService taskService;

    public void addByProcessDefinitionId(String projectId, String processDefinitionId, String tenantCode) {
        List<String> taskIdList = processTaskRelationService.getQuery().eq("processDefinitionCode", processDefinitionId).listValue("postTaskCode", String.class);
        //工作流中任务节点不可能为空
        Set<String> taskIdSet = new HashSet<>(taskIdList);
        List<Task> taskList = taskService.getQuery()
                .in("id", taskIdSet)
                .eq("notRunCheck", 1)
                .filters("id","planExecTime","effectiveRange","effectiveRangeUnit").list();
        if (null != taskList && !taskList.isEmpty()) {
            Map<String, Task> taskMap = taskList.stream().collect(Collectors.toMap(Task::getId, task -> task));
            List<CheckTask> addCheckTaskList = new ArrayList<>();
            taskMap.keySet().forEach(taskId -> {
                Task task = taskMap.get(taskId);
                CheckTask checkTask = new CheckTask();
                checkTask.setProjectId(projectId);
                checkTask.setProcessDefinitionId(processDefinitionId);
                checkTask.setTaskId(taskId);
                checkTask.setPlanExecTime(task.getPlanExecTime());
                checkTask.setEffectiveRange(task.getEffectiveRange());
                checkTask.setEffectiveRangeUnit(task.getEffectiveRangeUnit());
                checkTask.setTenantCode(tenantCode);
                addCheckTaskList.add(checkTask);
            });
            this.add(addCheckTaskList);
        }
    }
}
