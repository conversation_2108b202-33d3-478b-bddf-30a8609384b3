package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.TaskGroup;
import com.joyadata.scc.model.TaskGroupQueue;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhus<PERSON>eng on 2024/6/24 13:11.
 */
@Slf4j
@Service
public class TaskGroupQueueService extends BaseService<TaskGroupQueue> {
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Autowired
    HttpRequestFeignService httpRequestFeignService;

    @Override
    public Integer update(String id, TaskGroupQueue bean) {
        String url = dolphinscheduler + Constants.UPDATE_TASK_GROUP_QUEUE_PRIORITY;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("queueId", bean.getId());
        data.put("priority", Integer.toString(bean.getPriority()));
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("修改优先级失败，错误信息是{}", saveMap);
                throw new AppErrorException("修改优先级失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("修改优先级失败，错误信息是{}", e.getMessage());
            throw new AppErrorException("修改优先级失败！报错信息=" + e.getMessage());
        }
        return 1;
    }

    public Response<?> forceStart(String taskGroupQueueId) {
        String url = dolphinscheduler + Constants.TASK_GROUP_QUEUE_FORCE_START;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("queueId", taskGroupQueueId);
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0 &&
                    Integer.valueOf(String.valueOf(saveMap.get("code"))) == 130017) {
                log.error("强制启动失败，节点已经获取任务组资源");
                throw new AppErrorException("强制启动失败，节点已经获取任务组资源。");
            } else if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("强制启动失败,原因是{}", saveMap);
                throw new AppErrorException("强制启动失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("强制启动失败，错误信息是{}", e.getMessage());
            throw new AppErrorException("强制启动失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("强制启动成功！");
    }

    public Response<?> getTaskGroupQueueList(String projectId, Integer page, Integer pager,
                                             String taskInstanceName, String processInstanceName, String groupId) {
        List<String> taskGroupIdList = new ArrayList<>();
        if (StringUtils.isBlank(groupId)) {
            taskGroupIdList = getService(TaskGroup.class).getQuery().eq("projectId", projectId).listValue("id", String.class);
        } else {
            taskGroupIdList.add(groupId);
        }
        if (null == taskGroupIdList || taskGroupIdList.size() == 0) {
            return ResponseFactory.makeSuccess("success");
        }
        String groupIds = String.join(",", taskGroupIdList);
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.GET_GROUP_QUEUE_LIST +
                "?pageSize=" + pager
                + "&pageNo=" + (page + 1)
                + "&groupIds=" + groupIds;
        if (StringUtils.isNotBlank(taskInstanceName)) {
            url = url + "&taskInstanceName=" + taskInstanceName;
        }
        if (StringUtils.isNotBlank(processInstanceName)) {
            url = url + "&processInstanceName=" + processInstanceName;
        }
        try {
            Map saveMap = httpRequestFeignService.get4url(url, headers, Map.class, false);
            JSONObject jsonObject = JsonUtil.toJSON(saveMap);
            if (jsonObject.getInteger("code") == 0) {
                JSONObject data = jsonObject.getJSONObject("data");
                return ResponseFactory.makeSuccess(data.getJSONArray("totalList"), page, pager, data.getInteger("total"));
            } else {
                log.error("获取列表失败，信息是{}", jsonObject.getInteger("msg"));
                return ResponseFactory.makeError("获取列表失败！报错信息=" + jsonObject.getInteger("msg"));
            }
        } catch (Exception e) {
            log.error("获取列表失败，信息是{}", e.getMessage());
            throw new AppErrorException("获取列表失败！报错信息=" + e.getMessage());
        }
    }
}
