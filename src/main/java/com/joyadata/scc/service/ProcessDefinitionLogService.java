package com.joyadata.scc.service;

import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.scc.model.ProcessDefinitionLog;
import com.joyadata.scc.model.ProcessTaskRelationLog;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.sql.ConditionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Condition;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinitionLogService
 * @date 2023/11/3
 */
@Slf4j
@Service
public class ProcessDefinitionLogService extends BaseService<ProcessDefinitionLog> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private ProcessTaskRelationLogService processTaskRelationLogService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private TaskService taskService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Transactional
    @Override
    public Integer delete(String id, ProcessDefinitionLog bean) {
        ProcessDefinitionLog processDefinitionLog = this.getQuery().eq("id", id).one();
        String processDefinitionId = processDefinitionLog.getProcessDefinitionId();
        Integer version = processDefinitionLog.getVersion();
        //删除工作流版本时，判断要删除的版本关联的任务，在其他版本有没有关联，没有关联就将任务的processDefinitionCode字段置空，其他工作流才可以试用该任务
        List<String> taskIdList = processTaskRelationLogService.getQuery().eq("processDefinitionCode", processDefinitionId)
                .eq("processDefinitionVersion", version).listValue("postTaskCode", String.class);
        if (!CollectionUtils.isEmpty(taskIdList)) {
            List<String> updateTaskIdList = new ArrayList<>();
            for (String taskId : taskIdList) {
                Integer total = processTaskRelationLogService.getQuery().eq("processDefinitionCode", processDefinitionId)
                        .notIn("processDefinitionVersion", version).eq("postTaskCode", taskId).total();
                if (null == total || total == 0) {
                    updateTaskIdList.add(taskId);
                }
            }
            if (!CollectionUtils.isEmpty(updateTaskIdList)) {
                taskService.getSqlExecutor().excuteUpdate(getUpdateSql(updateTaskIdList));
            }
        }


        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.SWITCH_PROCESS_VERSION.replace("{projectCode}",
                processDefinitionLog.getProjectId()) + "/" + processDefinitionId + "/versions/" + version + "?projectCode=" + processDefinitionLog.getProjectId();
        Map saveMap = httpRequestFeignService.delete4url(url, null, headers, Map.class);
        if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
            log.error("删除工作流版本失败,错误信息是 {}", saveMap);
            throw new AppErrorException("删除工作流版本失败！请联系管理员，报错信息：" + saveMap.get("msg"));
        }
        return 1;
    }

    private String getUpdateSql(List<String> updateTaskIdList) {
        String updateSql = "UPDATE scc_task \n" +
                "SET process_definition_code = NULL \n" +
                "WHERE\n" +
                "\tscc_task.id IN (\n" +
                "\t'%s')";
        String join = String.join("','", updateTaskIdList);
        return String.format(updateSql, join);
    }
}
