package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.joyadata.cms.model.User;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.file.model.JoyadataAppFile;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.Resource;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.util.ExportFile;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.service.RestTemplateService;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.JsonUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ResourceService extends BaseService<Resource> {

    @Autowired
    HttpRequestFeignService httpRequestFeignService;

    JoyaFeignService<JoyadataAppFile> fileJoyaFeignService = FeignFactory.make(JoyadataAppFile.class);

    @Autowired
    private RestTemplateService restTemplateService;

    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Value("${saveFilePath:/dsg/app/backend/}")
    private String saveFilePath;

    private String buildFile(Resource bean) throws IOException, URISyntaxException {
        //生成文件
        String fileName = null;
        JoyadataAppFile appFile = null;
        try {
            ExportFile exportFile = new ExportFile();
            File file = exportFile.createFile(bean.getFileContent(), saveFilePath + bean.getFileName());
            //获取文件 上传file服务
            fileName = file.getName();
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, null, IOUtils.toByteArray(fileInputStream));
            appFile = fileJoyaFeignService.upload(multipartFile, ApplicationContextHelp.getModule(), fileName);
            //filekey写进表
            Resource resource = new Resource();
            resource.setId(bean.getId());
            resource.setFileKey(appFile.getId());
            update(resource);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        } finally {
            File delfile = new File(saveFilePath + fileName);
            if (delfile.exists()) {
                boolean delete = delfile.delete();
                log.info("删除生成文件：" + delete);
            }
        }
        //删除生成的文件
        return appFile.getId();
    }


    @SneakyThrows
    @Override
    @Transactional
    public Resource add(String id, Resource bean) {
        //分别校验创建文件夹和创建文件的参数是否必填
        checkRequired(bean);
        //计算文件大小，存入数据库
        forFileSize(bean);
        if (null == bean.getSize()) {
            bean.setSize("0B");
        }
        if (null == bean.getSize()) {
            bean.setFileByte(0L);
        }
        //用于判断引用资源时，文件夹是否置灰
        bean.setIsDisabled(bean.getIsFolder());
        //区分来源 创建文件、上传文件
        if (bean.getFileKey() != null) {
            bean.setOrigin("上传文件");
            bean.setEncoder("UTF-8");
        }
        //传过来的父级id与资源表中的id匹配，匹配到数据更改 isEmptyFolder
        if (null != bean.getPid() && !"-1".equals(bean.getPid())) {
            String resourceId = getQuery().eq("id", bean.getPid()).eq("projectId", bean.getProjectId()).oneValue("id", String.class);
            if (null != resourceId) {
                Resource resource = new Resource();
                resource.setId(resourceId);
                resource.setIsEmptyFolder(false);
                update(resource);
            }
        }
        //前端截取，后台暂不处理
        //上传的文件，默认格式为空，要从文件名中获取
//        if (StringUtils.isEmpty(bean.getFileFormat()) && !bean.getIsFolder()){
//            bean.setFileFormat(bean.getFileName().substring(bean.getFileName().lastIndexOf(".") + 1));
//        }
        //生成全路径名
        Resource add = super.add(id, bean);
        if (null != add.getParentNames()) {
            String fullName = "/" + add.getParentNames().replace(",", "/");
            add.setFullName(fullName);
            update(add);
        }
        //组装参数
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> body = new HashMap<>();
        body.put("type", add.getType());
        String addName = add.getName();
        body.put("fileName", add.getFileName());
        body.put("name", addName);
        body.put("description", add.getDescription());
        body.put("id", add.getId());
        body.put("pid", add.getPid());
        String currentDir = "/";
        if (null != add.getParentNames() && add.getParentNames().contains(",")) {
            String parentNames = currentDir + add.getParentNames().replace(",", "/");
            currentDir = parentNames.substring(parentNames.indexOf("/"), parentNames.lastIndexOf("/"));
        }
        //项目A，项目B同时添加 a文件，我们的表区分租户和项目，但是海豚的资源中心表不区分
        //所以把projectId存的时候放在海豚的全路径字段上
        String projectId = "/" + add.getProjectId();
        currentDir = projectId + currentDir;
        body.put("currentDir", currentDir);
        if (currentDir.endsWith("/")) {
            currentDir = projectId;
            body.put("currentDir", currentDir);
        }
        log.info("currentDir={}", currentDir);
        Response response = null;
        //创建文件夹
        if (bean.getIsFolder().equals(true)) {
            String url = dolphinscheduler + "/resources/directory";
            Map map = Utils.webClientPost(url, body, headers.get(Constants.SESSION_ID).toString());
            response = Utils.responseInfo(map);
        }

        //创建文件
        if (bean.getIsFolder().equals(false) && bean.getFileKey() == null) {
            if (null != addName && addName.contains(".")) {
                String name = addName.substring(0, addName.lastIndexOf("."));
                body.put("fileName", name);
            }
            body.put("suffix", add.getFileFormat());
            body.put("content", bean.getFileContent());
            String url = dolphinscheduler + "/resources/online-create";
            Map map = Utils.webClientPost(url, body, headers.get(Constants.SESSION_ID).toString());
            response = Utils.responseInfo(map);
        } else if (bean.getIsFolder().equals(false) && bean.getFileKey() != null) {
            //上传文件
            User user = ThreadLocalUserUtil.getUser(User.class);
            String project = ApplicationContextHelp.getProject();
            String module = ApplicationContextHelp.getModule();
            String appGatewayHttp = ApplicationContextHelp.getAppGatewayHttp();
            String version = ApplicationContextHelp.getVersion();
            String urlPath = appGatewayHttp + File.separator + project + File.separator + version + "/file/app/file/download" +
                    "?id=" + bean.getFileKey() + "&project=" + project + "&module=" + module + "&token=" + user.getToken();
            File file = Utils.downloadFile(urlPath, body.get("fileName"), saveFilePath);
            FileInputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(body.get("fileName"), body.get("fileName"), null, IOUtils.toByteArray(inputStream));
            String url = dolphinscheduler + "/resources";
            Map<String, String> bodyMap = new HashMap<>();
            bodyMap.put("id", body.get("id"));
            bodyMap.put("type", body.get("type"));
            bodyMap.put("name", body.get("name"));
            bodyMap.put("description", StringUtils.defaultIfBlank(body.get("description"), ""));
            bodyMap.put("pid", body.get("pid"));
            bodyMap.put("currentDir", body.get("currentDir"));
            log.info("上传参数{}", bodyMap);
            String result = Utils.uploadFile(url, multipartFile, "file", headers, bodyMap);
            Map map = JSONObject.parseObject(result, Map.class);
            response = Utils.responseInfo(map);
        }
        log.info("add resource result={}", response.getResult());
        log.info("add resource code={}", response.getCode());
        if (response.getCode() != 0) {
            throw new AppErrorException("资源创建失败！请联系管理员:" + response.getMessage());
        }
        //生成文件
        if (bean.getIsFolder().equals(false) && bean.getFileKey() == null) {
            buildFile(bean);
        }
        return add;
    }


    @SneakyThrows
    @Override
    @Transactional
    public Integer update(String id, Resource bean) {
        //计算文件大小，存入数据库
        bean.setSize(null);
        forFileSize(bean);
        //文件后缀若是改动，数据库也需要更改对应字段
        String fileName = bean.getFileName();
        String fileFormat = bean.getFileFormat();
        String fullName = bean.getFullName();
        String parentNames = bean.getParentNames();
        if (null != fileName && null != fileFormat && null != fullName && null != parentNames) {
            fileName = fileName + "." + fileFormat;
            bean.setFileName(fileName);
            fullName = fullName.substring(0, fullName.lastIndexOf("/") + 1) + fileName;
            bean.setFullName(fullName);
            if (parentNames.contains(",")) {
                parentNames = parentNames.substring(0, fullName.lastIndexOf(",") + 1) + fileName;
            } else {
                parentNames = fileName;
            }
            bean.setParentNames(parentNames);
            //重新生成文件
            bean.setFileKey(buildFile(bean));
        }
        //修改创建文件、文件夹，前端传fileName，后台把fileName备份在name中一份
        //修改上传文件 前端传name直接编辑，fileName不做改动
        if (null != bean.getFileName()) {
            bean.setName(bean.getFileName());
        }
        //组装参数
        Map<String, Object> headers = Utils.getHeader();
        if (null != bean.getFileContent()) {
            //编辑文件内容
            //zsp 20250626 编辑文件时调用restTemplateService里的方法，httpRequestFeignService中没有指定x3w请求体的方式，直接拼接在url上会超长
            String updateContentUrl = dolphinscheduler + "/resources/" + id + "/update-content";
            Map<String, Object> params = new HashMap<>();
            params.put("content", bean.getFileContent());
            String mapString = restTemplateService.putWithX_WWW_Form(updateContentUrl, parseJsonHeaders(headers), params);
            Map updateContentMap = JsonUtil.toMap(mapString, Map.class);
            Response response = Utils.responseInfo(updateContentMap);
            if (response.getCode() != 0) {
                throw new AppErrorException("资源内容修改失败！请联系管理员:" + response.getMessage());
            }
        }
        //编辑文件名称等信息
        String name = bean.getFileName() == null ? bean.getName() : bean.getFileName();
        String updateInfoUrl = dolphinscheduler + "/resources/" + id + "?type=" + bean.getType() + "&name=" + name + "&description=";
        if (StringUtils.isNotEmpty(bean.getDescription())) {
            updateInfoUrl = updateInfoUrl + bean.getDescription();
        }
        Map updateInfoMap = httpRequestFeignService.put4url(updateInfoUrl, null, headers, Map.class);
        Response response = Utils.responseInfo(updateInfoMap);
        if (response.getCode() != 0) {
            throw new AppErrorException("资源信息修改失败！请联系管理员:" + response.getMessage());
        }
        return super.update(id, bean);
    }

    private HttpHeaders parseJsonHeaders(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        HttpHeaders headers = new HttpHeaders();
        try {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null) {
                    headers.set(key, value.toString());
                }
            }
            return headers.isEmpty() ? null : headers;
        } catch (Exception e) {
            log.warn("解析headers失败: {}", map, e);
            return null;
        }
    }

    @Transactional
    public Response customDelete(String id) {
        //查询文件夹下是否还有内容，更改 isEmptyFolder
        String pid = getQuery().eq("id", id).oneValue("pid", String.class);
        if (null != pid && !"-1".equals(pid)) {
            //除要删除的记录外，还有没有文件在该目录下，若没有isEmptyFolder为true
            List<String> resourceIds = getQuery().eq("pid", pid).notIn("id", id).listValue("id", String.class);
            if (CollectionUtils.isEmpty(resourceIds)) {
                Resource resource = new Resource();
                resource.setId(pid);
                resource.setIsEmptyFolder(true);
                update(resource);
            }
        }
        //若资源被关联任务，不能被删除
        List<Task> taskList = getService(Task.class).getQuery().like("resourceIds", id).list();
        if (!CollectionUtils.isEmpty(taskList)) {
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (Task task : taskList) {
                ProcessDefinition processDefinition = getService(ProcessDefinition.class).getQuery().like("taskIds", task.getId()).one();
                JSONObject jsonObject = new JSONObject();
                if (null != processDefinition) {
                    jsonObject.put("processDefinitionName", processDefinition.getName());
                }
                jsonObject.put("taskName", task.getName());
                jsonObjectList.add(jsonObject);
            }
            return ResponseFactory.makeSuccess(jsonObjectList);
        }
        //组装参数
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/resources/" + id;
        Response response = httpRequestFeignService.delete4url(url, null, headers, Response.class);
        log.info("delete resource result={}", response.getResult());
        log.info("delete resource code={}", response.getCode());
        if (response.getCode() != 0) {
            throw new AppErrorException("资源删除失败！请联系管理员:" + response.getMessage());
        }
        Integer delete = super.delete(id);
        return ResponseFactory.makeSuccess(delete);
    }

    private void forFileSize(Resource bean) throws UnsupportedEncodingException {

        //上传文件后返回的size保存
        if (bean.getIsFolder().equals(false) && bean.getSize() != null) {
            long s = Long.parseLong(bean.getSize());
            bean.setFileByte(s);
            String size = formatFileSize(s);
            bean.setSize(size);
        }
        //创建文件计算string字节的size保存
        if (bean.getIsFolder().equals(false) && bean.getFileContent() != null) {
            try {
                long length = bean.getFileContent().getBytes("UTF-8").length;
                bean.setFileByte(length);
                String size = formatFileSize(length);
                bean.setSize(size);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        Long fileByte = bean.getFileByte();
        //原来的文件大小
        Long oldFileByte = getQuery().eq("id", bean.getId()).oneValue("fileByte", Long.class);
        if (null != oldFileByte) {
            fileByte = fileByte - oldFileByte;
        }
        //当 前端没传pid，pid为-1
        if (null == bean.getPid()) {
            bean.setPid("-1");
        }
        //文件夹累加文件的大小
        if (null != fileByte || "-1".equals(bean.getPid())) {
            String pid = bean.getPid();
            String parentIds = getQuery().like("parentIds", pid).oneValue("parentIds", String.class);
            if (null != parentIds) {
                String[] split = parentIds.split(",");
                List<Resource> list = getQuery().in("id", split).eq("isFolder", true).list();
                if (null != list && list.size() > 0) {
                    for (Resource resource : list) {
                        Long fb = resource.getFileByte();
                        if (null == fb) {
                            fb = 0L;
                        }
                        fb += fileByte;
                        String size = formatFileSize(fb);
                        resource.setFileByte(fb);
                        resource.setSize(size);
                        update(resource);
                    }
                }
            }
        } else {
            bean.setFileByte(0L);
            bean.setSize("0B");
        }
    }

    public static String formatFileSize(long size) {
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        String wrongSize = "0B";
        if (size == 0) {
            return wrongSize;
        }
        if (size < 1024) {
            fileSizeString = df.format((double) size) + "B";
        } else if (size < 1048576) {
            fileSizeString = df.format((double) size / 1024) + "KB";
        } else if (size < 1073741824) {
            fileSizeString = df.format((double) size / 1048576) + "MB";
        } else if (size < 1099511627776L) {
            fileSizeString = df.format((double) size / 1073741824) + "GB";
        } else if (size < 1125899906842624L) {
            fileSizeString = df.format((double) size / 1099511627776L) + "TB";
        } else {
            fileSizeString = df.format((double) size / 1125899906842624L) + "PB";
        }
        return fileSizeString;
    }

    private void checkRequired(Resource bean) {
        String fileName = bean.getFileName();
        //除上传文件后调用的保存  都需把fileName备份进name
        if (StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(bean.getFileFormat())) {
            bean.setFileName(fileName + "." + bean.getFileFormat());
            bean.setName(bean.getFileName());
        } else if (StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(bean.getFileKey())) {
            bean.setName(fileName);
        } else if (StringUtils.isBlank(fileName)) {
            throw new AppErrorException("缺少必要参数:fileName");
        }
        //文件id为空，校验文件格式、文件内容
        if (StringUtils.isBlank(bean.getFileKey())) {
            Boolean isFolder = bean.getIsFolder();
            if (isFolder.equals(false)) {
                if (null == bean.getFileFormat()) {
                    throw new AppErrorException("缺少必要参数:fileFormat");
                }
                if (null == bean.getFileContent()) {
                    throw new AppErrorException("缺少必要参数:fileContent");
                }
            }
        }
    }

}
