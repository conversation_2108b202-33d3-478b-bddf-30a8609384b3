package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.User;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.WorkerGroup;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhushipeng on 2024/6/24 20:48.
 */
@Slf4j
@Service
public class WorkerGroupService extends BaseService<WorkerGroup> {
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Autowired
    HttpRequestFeignService httpRequestFeignService;

    public Response<?> createWorkerGroup(WorkerGroup bean) {
        String tenantCode = ThreadLocalUserUtil.getUser(User.class).getTenantCode();
        String url = dolphinscheduler + Constants.CREATE_WORKER_GROUP;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("name", bean.getName());
        data.put("addrList", bean.getAddrList());
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("新增工作组失败,错误信息是{}", saveMap);
                throw new AppErrorException("新增工作组失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("新增工作组失败,错误信息是{}", e.getMessage());
            throw new AppErrorException("新增工作组失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("success");
    }

    public Response<?> updateWorkerGroup(WorkerGroup bean) {
        String tenantCode = ThreadLocalUserUtil.getUser(User.class).getTenantCode();
        String url = dolphinscheduler + Constants.CREATE_WORKER_GROUP;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("id", bean.getId());
        data.put("name", bean.getName());
        data.put("addrList", bean.getAddrList());
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("修改工作组失败,错误信息是{}", saveMap);
                throw new AppErrorException("修改工作组失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("修改工作组失败,错误信息是{}", e.getMessage());
            throw new AppErrorException("修改工作组失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("success");
    }

    public Response<?> deleteWorkerGroup(String id) {
        String url = dolphinscheduler + Constants.DELETE_WORKER_GROUP + "?id=" + id;
        url = url.replace("{id}", id);
        Map<String, Object> headers = Utils.getHeader();
        JSONObject data = new JSONObject();
        data.put("id", id);
        try {
            Map saveMap = httpRequestFeignService.delete4url(url, null, headers, Map.class);
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("删除工作组失败{}", saveMap);
                throw new AppErrorException("删除工作组失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("删除工作组失败 {}", e.getMessage());
            throw new AppErrorException("删除工作组失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("success");
    }


//    @Override
//    public List<WorkerGroup> getList(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
//        List<WorkerGroup> workerGroupList = super.getList(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
//        //如果页面加了根据名称搜索的条件，就不添加default工作组
//        Boolean flag = true;
//        for (ConditionGroup conditionGroup : conditionGroupList) {
//            if (conditionGroup.getConditions().size() == 0) {
//                continue;
//            }
//            List<WhereCondition> conditions = conditionGroup.getConditions();
//            for (WhereCondition whereCondition : conditions) {
//                if (whereCondition.getFiled().equals("name")
//                        && whereCondition.getSymbol().equalsIgnoreCase("like")
//                        && !"default".contains(whereCondition.getValue().toString().substring(1, whereCondition.getValue().toString().length() - 1))) {
//                    flag = false;
//                }
//            }
//        }
//        if (null == workerGroupList) {
//            workerGroupList = new ArrayList<>();
//        }
//        if (flag) {
//            Response addressList = getAddressList();
//            WorkerGroup workerGroup = new WorkerGroup();
//            workerGroup.setName("default");
//            workerGroup.setSystemDefault(true);
//            workerGroup.setAddrList(String.join(",", (List<String>) addressList.getResult()));
//            workerGroupList.add(workerGroup);
//        }
//        return workerGroupList;
//    }


    public Response getAddressList() {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.WORKER_GROUPS_ADDRESS_LIST;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        return Utils.responseInfo(map);
    }
}
