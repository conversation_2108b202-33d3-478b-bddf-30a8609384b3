package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionSetting;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.GeneratorUtil;
import com.joyadata.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinitionSettingService
 * @date 2024/9/19
 */
@Slf4j
@Service
public class ProcessDefinitionSettingService extends BaseService<ProcessDefinitionSetting> {

    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private HttpRequestFeignService httpRequestFeignService;

    @Override
    @Transactional
    public ProcessDefinitionSetting add(String id, ProcessDefinitionSetting processSetting) {
        String processDefinitionId = processSetting.getProcessDefinitionId();
        //记录工作流定义设置。
        ProcessDefinitionSetting processDefinitionSetting = this.getQuery().eq("processDefinitionId", processDefinitionId).one();
        //执行策略有变更，更新工作流定义
        ProcessDefinition processDefinition = processDefinitionService.getById(processDefinitionId);
        String executionType = processSetting.getExecutionType();
        int failedCount = processSetting.getFailedCount();
        processDefinition.setExecutionType(executionType);
        processDefinitionService.update(processDefinitionId, processDefinition, false);
        //更新海豚工作流定义
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", processDefinition.getProjectId()) + "/updateExecutionType/" + processDefinitionId
                + "?executionType=" + processSetting.getExecutionType()
                + "&failedCount=" + failedCount;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(String.valueOf(map.get("code"))) != 0) {
            log.error("工作流设置失败！={}", map);
            throw new AppErrorException("工作流设置失败！", map);
        }
        if (null != processDefinitionSetting) {
            //如果有设置过,update
            processDefinitionSetting.setProcessDefinitionId(processSetting.getProcessDefinitionId());
            processDefinitionSetting.setExecutionType(processSetting.getExecutionType());
            processDefinitionSetting.setFailureStrategy(processSetting.getFailureStrategy());
            processDefinitionSetting.setProcessInstancePriority(processSetting.getProcessInstancePriority());
            processDefinitionSetting.setWorkerGroup(processSetting.getWorkerGroup());
            processDefinitionSetting.setEnvironmentCode(processSetting.getEnvironmentCode());
            processDefinitionSetting.setFailedCount(failedCount);
            this.update(processDefinitionSetting);
            return processDefinitionSetting;
        }
        return super.add(id, processSetting);
    }
}
