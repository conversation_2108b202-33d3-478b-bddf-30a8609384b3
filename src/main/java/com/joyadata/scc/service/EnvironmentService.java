package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.Environment;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhushipeng on 2024/6/24 21:30.
 */
@Slf4j
@Service
public class EnvironmentService extends BaseService<Environment> {
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Autowired
    HttpRequestFeignService httpRequestFeignService;

    public Response<?> updateEnvironment(Environment environment) {
        String url = dolphinscheduler + Constants.UPDATE_ENVIRONMENT;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("name", environment.getName());
        data.put("config", environment.getConfig());
        data.put("code", Long.toString(environment.getCode()));
        data.put("description", environment.getDescription());
        data.put("workerGroups", JSONObject.toJSONString(environment.getWorkers()));
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("修改环境管理失败,信息是{}", saveMap);
                throw new AppErrorException("修改环境管理失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("修改环境管理失败,信息是{}", e.getMessage());
            throw new AppErrorException("修改环境管理失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("success");
    }

    public Response<?> createEnvironment(Environment environment) {
        String url = dolphinscheduler + Constants.CREATE_ENVIRONMENT;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("name", environment.getName());
        data.put("config", environment.getConfig());
        data.put("description", environment.getDescription());
        data.put("workerGroups", JSONObject.toJSONString(environment.getWorkers()));
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("新增环境管理失败,信息是{}", saveMap);
                throw new AppErrorException("新增环境管理失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("新增环境管理失败,信息是{}", e.getMessage());
            throw new AppErrorException("新增环境管理失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("success");
    }

    public Response<?> deleteEnvironment(String environmentCode) {
        String url = dolphinscheduler + Constants.DELETE_ENVIRONMENT;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("environmentCode", environmentCode);
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("删除环境管理失败,信息是{}", saveMap);
                throw new AppErrorException("删除环境管理失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            throw new AppErrorException("删除环境管理失败！报错信息=" + e.getMessage());
        }
        return ResponseFactory.makeSuccess("success");
    }

}
