package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.Calendar;
import com.joyadata.cms.model.CalendarConfig;
import com.joyadata.cms.model.User;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.web.Response;
import com.joyadata.scc.dto.SchedulerParam;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionSetting;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.util.CronUtils;
import com.joyadata.scc.util.DateUtils;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: SchedulerService
 * @date 2023/11/3
 */
@Slf4j
@Service
public class SchedulerService extends BaseService<Scheduler> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private ProcessDefinitionExecTimeService processDefinitionExecTimeService;

    private JoyaFeignService<Calendar> calendarJoyaFeignService = FeignFactory.make(Calendar.class);

    @SneakyThrows
    @Transactional
    @Override
    public Scheduler add(String id, Scheduler bean) {
        //2024-09-23 如果开始时间结束时间为null 自动赋值100年
        if (null == bean.getStartTime()) {
            initStartTime(bean);
        }
        if (null == bean.getEndTime()) {
            initEndTime(bean);
        }

        String processDefinitionCode = bean.getProcessDefinitionCode();
        //先查看是否有工作流设置
        ProcessDefinitionSetting processSetting = getService(ProcessDefinitionSetting.class).getQuery().eq("processDefinitionId", processDefinitionCode).one();
        if (null != processSetting) {
            bean.setFailureStrategy(processSetting.getFailureStrategy());
            bean.setProcessInstancePriority(processSetting.getProcessInstancePriority());
            bean.setWorkerGroup(processSetting.getWorkerGroup());
            bean.setEnvironmentCode(processSetting.getEnvironmentCode());
        }
        String releaseState = getService(ProcessDefinition.class).getQuery().eq("id", processDefinitionCode).oneValue("releaseState", String.class);
        if (ProcessDefinition.ReleaseState.notOnline.equals(releaseState)) {
            log.error("查询工作流定义出现错误，processDefinitionCode={},releaseState={}", processDefinitionCode, releaseState);
            throw new AppErrorException("该工作流未上线，不可设置定时！");
        }
        String crontab = bean.getCrontab();
        if (null == crontab || crontab.equals("")) {
            log.error("bean中获取到的crontab为{}", crontab);
            throw new AppErrorException("cron不可为空！");
        }
        boolean validExpression = CronExpression.isValidExpression(crontab);
        if (!validExpression) {
            log.error("cron表达式格式错误");
            throw new AppErrorException("cron表达式格式错误，请检查！");
        }
        if (DateUtils.differSec(bean.getStartTime(), bean.getEndTime()) == 0) {
            log.error("开始时间不能和结束时间一样,开始时间是{}，结束时间是{}", bean.getStartTime(), bean.getEndTime());
            throw new AppErrorException("开始时间不能和结束时间一样！");
        }
        if (bean.getStartTime().getTime() > bean.getEndTime().getTime()) {
            log.error("开始时间在结束时间之后错误,开始时间是{},结束时间是{}", bean.getStartTime().getTime(), bean.getEndTime().getTime());
            throw new AppErrorException("开始时间在结束时间之后错误！");
        }
        Scheduler schedule = this.getQuery().eq("processDefinitionCode", processDefinitionCode).fixeds("dbid").one();
        if (null != schedule) {
            Long dbid = schedule.getDbid();
            //如果是下线，先下线海豚定时管理 再更新本地定时管理
            if (Scheduler.ReleaseState.online.equals(schedule.getReleaseState())) {
                offline(schedule);
            }
            //更新
            this.update(schedule.getId(), bean);
            BeanUtils.copyProperties(bean, schedule);
            //如果是上线，更新本地定时管理 再更新海豚
            if (Scheduler.ReleaseState.online.equals(bean.getReleaseState())) {
                schedule.setDbid(dbid);
                online(schedule);
            }
        } else {
            //如果不存在 先入库
            schedule = super.add(id, bean);
            String dbid = this.getQuery().eq("id", schedule.getId()).fixeds("dbid").oneValue("dbid", String.class);
            SchedulerParam body = new SchedulerParam(schedule, dbid);
            Response response = createSchedule(body);
            if (response.getCode() != 0) {
                log.error("添加失败！请联系管理员 {}", response.getMessage());
                throw new AppErrorException("添加失败！请联系管理员，报错信息：" + response.getMessage());
            }
            //如果是上线状态 直接上线
            if (Scheduler.ReleaseState.online.equals(schedule.getReleaseState())) {
                //2024-08-27直接点上线该定时不存在时，这里dbid是空
                schedule.setDbid(Long.valueOf(dbid));
                online(schedule);
            }
        }
//        log.info("上下线处理完毕，开始初始化工作日到字典");
//        初始化工作日到字典
//        initCalendarCache(schedule);
        return schedule;
    }

    //把日历工作日信息放到redis
    private void initCalendarCache(Scheduler schedule) {
        String tenantCode = ThreadLocalUserUtil.getUser(User.class).getTenantCode();
        String calendarId = schedule.getCalendarId();
        if (StringUtils.isNotBlank(calendarId)) {
            Calendar calendar = calendarJoyaFeignService.getQuery().eq("id", calendarId).lazys("calendarConfigList").one();
            if (null != calendar) {
                List<CalendarConfig> calendarConfigList = calendar.getCalendarConfigList();
                //计算工作日
                List<String> selectDate = new ArrayList<>();
                List<JSONArray> jsonArrays = new ArrayList<>();
                Optional.ofNullable(calendarConfigList).ifPresent(calendarConfigs -> {
                    calendarConfigList.forEach(t -> jsonArrays.add(t.getSelectData()));
                    jsonArrays.stream().forEach(t -> {
                        selectDate.addAll(JSONObject.parseArray(t.toJSONString(), String.class));
                    });
                });

                //把工作日放入redis
                String key = "DEDP:SCHEDULER:CALENDAR:" + tenantCode + ":" + calendarId;
                if (!redisTemplate.hasKey(key)) {
                    redisTemplate.opsForSet().add(key, selectDate.toArray(new String[]{}));
                }
                //2024-6-11 日历停用策略: 不给值的时候，默认 0:不停止工作流
                if (null == schedule.getStopStrategy()) {
                    schedule.setStopStrategy("0");
                }
                //把定时任务的停止策略放到redis
                redisTemplate.opsForValue().set(key + ":" + schedule.getProcessDefinitionCode(), schedule.getStopStrategy());
            }
        }
    }


    @Transactional
    @Override
    public Integer delete(String id, Scheduler bean) {
        Scheduler schedule = this.getQuery().eq("id", id).fixeds("dbid").one();
        Response response = deleteScheduleById(schedule.getDbid(), schedule.getProjectId());
        if (response.getCode() != 0) {
            log.error("删除失败{}", response.getMessage());
            throw new AppErrorException("删除失败！请联系管理员，报错信息：" + response.getMessage());
        }
        return super.delete(id, bean);
    }

    @SneakyThrows
    @Transactional
    @Override
    public Integer update(String id, Scheduler bean) {
        //2024-09-23 如果开始时间结束时间为null 自动赋值100年
        if (null == bean.getStartTime()) {
            initStartTime(bean);
        }
        if (null == bean.getEndTime()) {
            initEndTime(bean);
        }
        String crontab = bean.getCrontab();
        if (null == crontab || crontab.equals("")) {
            log.error("cron不可为空");
            throw new AppErrorException("cron不可为空！");
        }
        boolean validExpression = CronExpression.isValidExpression(crontab);
        if (!validExpression) {
            log.error("cron表达式格式错误");
            throw new AppErrorException("cron表达式格式错误，请检查！");
        }
        if (DateUtils.differSec(bean.getStartTime(), bean.getEndTime()) == 0) {
            log.error("开始时间不能和结束时间一样");
            throw new AppErrorException("开始时间不能和结束时间一样！");
        }
        if (bean.getStartTime().getTime() > bean.getEndTime().getTime()) {
            log.error("开始时间在结束时间之后错误");
            throw new AppErrorException("开始时间在结束时间之后错误！");
        }
        Integer update = super.update(id, bean);
        Scheduler schedule = this.getQuery().eq("id", id).fixeds("dbid").one();
        SchedulerParam body = new SchedulerParam(schedule, schedule.getDbid().toString());
        Response response = updateSchedule(body, schedule.getDbid());
        //如果调度是上线状态，强制下线，再修改
        if (response.getMessage().equals("online status does not allow update operations")) {
            offline(schedule);
            response = updateSchedule(body, schedule.getDbid());
        }
        if (response.getCode() != 0) {
            log.error("修改失败,错误信息是{}", response.getMessage());
            throw new AppErrorException("修改失败！请联系管理员，报错信息：" + response.getMessage());
        }
        //初始化工作日到字典
//        initCalendarCache(schedule);
        return update;
    }

    public Response createSchedule(SchedulerParam param) throws UnsupportedEncodingException {
        Map<String, Object> headers = Utils.getHeader();
        //根据code删除旧数据
        String delUrl = dolphinscheduler + "/projects/" + param.getProjectCode() + "/schedules/processDefinitionCode" + "/" + param.getProcessDefinitionCode();
        Map delMap = httpRequestFeignService.delete4url(delUrl, null, headers, Map.class);
        if (Integer.parseInt(delMap.get("code").toString()) != 0) {
            throw new AppErrorException("定时配置失败！请联系管理员，报错信息：" + delMap.get("msg"));
        }
        //创建定时配置
        String url = dolphinscheduler + "/projects/" + param.getProjectCode() + "/schedules?processDefinitionCode=" + param.getProcessDefinitionCode()
                + "&schedule=" + URLEncoder.encode(param.getSchedule(), "UTF-8") + "&warningType=" + param.getWarningType() + "&warningGroupId=" + param.getWarningGroupId()
                + "&failureStrategy=" + param.getFailureStrategy() + "&workerGroup=" + param.getWorkerGroup() + "&environmentCode=" + param.getEnvironmentCode()
                + "&processInstancePriority=" + param.getProcessInstancePriority() + "&id=" + param.getId() + "&calendarId=" + param.getCalendarId();
        Map map = httpRequestFeignService.post4url(url, null, headers, Map.class);
        return Utils.responseInfo(map);
    }

    public Response deleteScheduleById(Long id, String projectCode) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/projects/" + projectCode + "/schedules/" + id;
        Map map = httpRequestFeignService.delete4url(url, null, headers, Map.class);
        return Utils.responseInfo(map);
    }

    public Response updateSchedule(SchedulerParam param, Long id) throws UnsupportedEncodingException {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/projects/" + param.getProjectCode() + "/schedules/" + id + "?schedule=" + URLEncoder.encode(param.getSchedule(), "UTF-8")
                + "&warningType=" + param.getWarningType() + "&warningGroupId=" + param.getWarningGroupId()
                + "&failureStrategy=" + param.getFailureStrategy() + "&workerGroup=" + param.getWorkerGroup() + "&environmentCode=" + param.getEnvironmentCode()
                + "&processInstancePriority=" + param.getProcessInstancePriority() + "&calendarId=" + param.getCalendarId();
        Map map = httpRequestFeignService.put4url(url, null, headers, Map.class);
        return Utils.responseInfo(map);
    }

    /**
     * 上线、下线调度方法
     *
     * @param id
     */
    @Transactional
    public void release(String id) {
        Scheduler schedule = this.getQuery().eq("id", id).fixeds("dbid").one();
        if (Scheduler.ReleaseState.online.equals(schedule.getReleaseState())) {
            //下线offline
            offline(schedule);
        } else {
            //上线online
            online(schedule);
        }

    }

    /**
     * 上线
     *
     * @param schedule
     */
    public void online(Scheduler schedule) {
        String projectId = schedule.getProjectId();
        //判断是否在生效时间段内
        String nextExecTime = CronUtils.getNextExecTime(schedule.getCrontab(), schedule.getStartTime(), schedule.getEndTime(), schedule.getTimezoneId());
        if (StringUtils.isBlank(nextExecTime)) {
            throw new AppErrorException("调度周期不在生效时间内，请调整！");
        }
        schedule.setReleaseState(Scheduler.ReleaseState.online);
        this.update(schedule.getId(), schedule, false);
        //调海豚的上线
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_SCHEDULE_ONLINE.replace("{projectCode}", projectId).replace("{id}", String.valueOf(schedule.getDbid()));
        Map resultMap = httpRequestFeignService.post4url(url, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0 && !resultMap.get("msg").equals("schedule release is already ONLINE")) {
            log.error("上线失败，错误信息是{}", resultMap);
            throw new AppErrorException("定时配置失败，请检查调度引擎服务是否正常！", resultMap.get("msg"));
        }

        String processDefinitionId = schedule.getProcessDefinitionCode();
        String tenantCode = ThreadLocalUserUtil.getUser(User.class).getTenantCode();
        //上线定时任务，计算下次执行时间
        processDefinitionExecTimeService.calculate30Times(processDefinitionExecTimeService.getProcessDefinitionWiths(Collections.singletonList(processDefinitionId)), tenantCode);
        log.info("下次执行时间计算结束");
    }

    /**
     * 下线
     *
     * @param schedule
     */
    public void offline(Scheduler schedule) {
        schedule.setReleaseState(Scheduler.ReleaseState.notOnline);
        this.update(schedule.getId(), schedule, false);
        //调海豚的下线
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_SCHEDULE_OFFLINE.replace("{projectCode}", schedule.getProjectId()).replace("{id}", String.valueOf(schedule.getDbid()));
        Map resultMap = httpRequestFeignService.post4url(url, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0 && !resultMap.get("msg").equals("schedule release is already OFFLINE")) {
            log.error("下线失败！请联系管理员，报错信息{}", resultMap);
            throw new AppErrorException("定时配置失败，请检查调度引擎服务是否正常！", resultMap.get("msg"));
        }
        //删除最近执行时间
        Integer delExecTimeCount = processDefinitionExecTimeService.deleteBy(new EqCondition("processDefinitionId", schedule.getProcessDefinitionCode()));
        log.info("下线定时任务删除下次执行时间数量 = {}", delExecTimeCount);
    }

    public void initStartTime(Scheduler scheduleParam) {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        scheduleParam.setStartTime(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()));
    }

    public void initEndTime(Scheduler scheduleParam) {
        // 获取100年后的日期
        LocalDate hundredYearsLater = LocalDate.now().plusYears(100);
        scheduleParam.setEndTime(Date.from(hundredYearsLater.atStartOfDay(ZoneId.systemDefault()).toInstant()));
    }


    public Map releaseOffline(String projectId, String processDefinitionCode) {
        Map<String, Object> map = new HashMap<>();
        String releaseState = Scheduler.ReleaseState.notOnline;
        Scheduler schedule = this.setIgnoreTenantCode().getQuery()
                .eq("projectId", projectId)
                .eq("processDefinitionCode", processDefinitionCode)
                .one();

        if (schedule == null) {
            map.put("code", 0);
            map.put("msg", "更新失败");
            map.put("data", 0);
            return map;
        }

        try {
            schedule.setReleaseState(releaseState);
            // Integer update = super.update(schedule.getId(), schedule);
            offline(schedule);
            map.put("code", 200);
            map.put("msg", "更新成功");
            map.put("data", 1);
        } catch (Exception e) {
            log.error("scc接口定时下线失败：", e.getMessage());
        }
        return map;
    }
}
