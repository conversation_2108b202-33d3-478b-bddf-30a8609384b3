package com.joyadata.scc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.User;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.interfaces.IService;
import com.joyadata.model.sql.*;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.poi.excel.model.ExcelTitle;
import com.joyadata.poi.excel.util.JoyadataPoiUtil;
import com.joyadata.scc.dto.*;
import com.joyadata.scc.enums.ReleaseState;
import com.joyadata.scc.enums.WorkflowExecutionStatus;
import com.joyadata.scc.model.*;
import com.joyadata.scc.model.dto.Instance;
import com.joyadata.scc.model.dto.IntegrationMessageDTO;
import com.joyadata.scc.model.dto.Release;
import com.joyadata.scc.model.dto.StartParam;
import com.joyadata.scc.util.DataConverter;
import com.joyadata.scc.util.TaskUtils;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.*;
import groovy.lang.Tuple2;
import groovy.lang.Tuple4;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ProcessDefinitionService
 * @date 2023/11/3
 */
@Slf4j
@Service
public class ProcessDefinitionService extends BaseService<ProcessDefinition> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private ProcessDefinitionLogService processDefinitionLogService;
    @Autowired
    private ProcessTaskRelationService processTaskRelationService;
    @Autowired
    private ProcessTaskRelationLogService processTaskRelationLogService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private SchedulerService schedulerService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    private IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private ProcessDefinitionCatalogService processDefinitionCatalogService;
    @Autowired
    private CatalogService catalogService;
    @Autowired
    private CheckTaskService checkTaskService;

    @Autowired
    private ProcessDefinitionExecTimeService processDefinitionExecTimeService;

    private final String TEST_TASK_KEY = "CINDASC:DS:TEST_TASK:";
    private final String DS_KEY = "CINDASC:DS:CHANNEL:TEST:";
    private final String DATASOURCEINFO_KEY = "DSG:";
    private final String PROCESS_UPDATE = "dedp:scc:processupdate:";
//    private JoyaFeignService<RunResultPushConfig> resultPushConfigJoyaFeignService = FeignFactory.make(RunResultPushConfig.class);

    @SneakyThrows
    @Transactional
    @Override
    public ProcessDefinition add(String id, ProcessDefinition bean) {
        String productId = bean.getProductId();
        String projectId = bean.getProjectId();
        Assert.notNull(productId, "产品id不能为空");
        Assert.notNull(projectId, "项目id不能为空");
        String processId = GeneratorUtil.genCode();
        if (StringUtils.isNotBlank(id)) {
            processId = id;
        }
        String newProcessId = processId;

        if (bean.getTaskRelationList() == null || bean.getTaskRelationList().size() == 0) {
            log.error("未创建节点，保存失败！");
            throw new AppErrorException("未创建节点，保存失败！");
        }

        //校验全局变量中的key是否有重复的
        checkGlobalParamsKey(bean);
        //判断是否有循环依赖 - 使用优化后的拓扑排序算法
        checkCyclicDependencyWithTopologicalSort(bean.getTaskRelationList());

        //新增任务
        List<Task> taskList = bean.getTaskList();
        if (null != taskList && taskList.size() > 0) {
            taskList.forEach(t -> {
                t.setProcessDefinitionCode(newProcessId);
                t.setProjectId(projectId);
                taskService.addTask(t.getId(), t, false);
            });
        }

        List<ProcessTaskRelation> taskRelationList = bean.getTaskRelationList();
        //2025-03-04 初始化目录id
        if (null != bean.getIsImport() && bean.getIsImport()) {
            //说明是外部使用，需要获取目录id
            Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initDefaultCatalogId(productId, projectId, bean.getCatalogNames());
            bean.setCatalogId(catalogTuple.getV1());
            bean.setCatalogParentIds(catalogTuple.getV2());
        }
        ProcessDefinition processDefinition = super.add(newProcessId, bean);
//        ProcessDefinitionLog processDefinitionLog = initProcessDefinitionLog(processDefinition);
//        processDefinitionLogService.add(processDefinitionLog);
        //获取前端传的工作流任务关联对象，set工作流id后insert
        List<String> taskIds = new ArrayList<>();
        for (ProcessTaskRelation processTaskRelation : taskRelationList) {
            processTaskRelation.setProjectId(projectId);
            //保存任务id
            taskIds.add(processTaskRelation.getPostTaskCode());
            processTaskRelation.setProcessDefinitionCode(processDefinition.getId());
            processTaskRelation.setProcessDefinitionVersion(processDefinition.getVersion());
            processTaskRelation.setPostTaskVersion(processDefinition.getVersion());
            processTaskRelationService.add(processTaskRelation);
//            ProcessTaskRelationLog processTaskRelationLog = JsonUtil.toBean(processTaskRelation, ProcessTaskRelationLog.class);
//            processTaskRelationLogService.add(processTaskRelationLog);
        }
        //组装body
        //工作流对象赋值taskList,在initBody方法中使用
        processDefinition.setTaskList(taskList);
        //调海豚的新增接口
        Response response = createProcessDefinition(initBody(processDefinition, "0"));
        if (null != response.getResult()) {
            if (response.getResult().equals("process node has cycle")) {
                log.error("流程节点间存在循环依赖，保存失败！");
                throw new AppErrorException("流程节点间存在循环依赖，保存失败！");
            }
        }
        if (response.getCode() != 0) {
            log.error("工作流添加失败！请联系管理员，报错信息 {}", response.getMessage());
            throw new AppErrorException("工作流添加失败！请联系管理员，报错信息：" + response.getMessage());
        }

        if (bean.getOriginProcess()) {
            IntegrationMessageDTO messageDTO = new IntegrationMessageDTO();
            messageDTO.setProjectId(processDefinition.getProjectId());
            messageDTO.setTenantCode(processDefinition.getTenantCode());
            messageDTO.setProcessDefinitionName(processDefinition.getName());
            messageDTO.setTaskIds(TaskUtils.getTaskIds(null, processDefinition));
            messageDTO.setProcessDefinitionId(id);
            try {
                kafkaTemplate.send(TaskUtils.TASK_BINDING_INFO, UUID.randomUUID().toString(), JSON.toJSONString(messageDTO));
            } catch (Exception e) {
                log.error("删除工作流发送kafka失败，发送消息内容是 {}");
            }
        }
        return processDefinition;
    }

    /**
     * 为了回显时不返回普通任务和依赖任务的关联对象（ProcessTaskRelation）
     * 为了返回taskList
     *
     * @return
     */
    public ProcessDefinition initTaskList(ProcessDefinition processDefinition) {
        if (null == processDefinition) {
            log.error("工作流不能为null");
            throw new AppErrorException("工作流不能为null");
        }
        String id = processDefinition.getId();
        List<ProcessTaskRelation> relationList = getService(ProcessTaskRelation.class).getQuery().eq("processDefinitionCode", id).list();
        //获取任务id查询任务list，任务详情中前置任务id如果是0 不返回
        List<String> taskIds = relationList.stream().map(ProcessTaskRelation::getPostTaskCode).collect(Collectors.toList());
        List<Task> taskList = getService(Task.class).getQuery().in("id", taskIds).withs("preTaskCode").list();
        if (null != taskList && taskList.size() > 0) {
            Optional.of(taskList.stream().filter(task -> null != task.getPreTaskCode() && task.getPreTaskCode().size() == 1 && task.getPreTaskCode().get(0).equals("0")).collect(Collectors.toList())).ifPresent(tasks -> {
                tasks.forEach(t -> t.setPreTaskCode(null));
            });
        } else {
            taskList = new ArrayList<>();
        }
        processDefinition.setTaskRelationList(relationList);
        processDefinition.setTaskList(taskList);
        return processDefinition;
    }

    @Transactional
    @Override
    public Integer delete(String id, ProcessDefinition bean) {
        ProcessDefinition processDefinition = this.getQuery().eq("id", id).one();
        if (null == processDefinition) {
            //导入导出使用，如果scc没有工作流，海豚有的情况下，会返回-2
            return -2;
        }
        if (processDefinition.getReleaseState().equals(ProcessDefinition.ReleaseState.online)) {
            log.error("该工作流已上线，不可删除");
            throw new AppErrorException("该工作流已上线，不可删除！");
        }
        //2024-08-21如果不是调度中心自己编排的工作流，不清空
        //删除工作流时清空任务表中的工作流code
        if (null == bean.getIsImport() || !bean.getIsImport()) {
            taskService.getSqlExecutor().excuteUpdate(getUpdateTaskSql(id));
        }
        //调海豚的删除接口
        Response response = deleteProcessDefinitionByCode(id, processDefinition.getProjectId());
        if (response.getCode() != 0
                && !response.getMessage().contains("does not exist")) {//忽略海豚报工作流不存在的错误
            log.error("工作流删除失败，错误信息是{}", response.getMessage());
            throw new AppErrorException("工作流删除失败！请联系管理员，报错信息：" + response.getMessage());
        }
        Integer delete = super.delete(id, bean);
        if (delete > 0) {
            //如果是任务测试生成的工作流，就需要删除redis中的key
            if (Boolean.TRUE.equals(processDefinition.getIsTest())) {
                redisTemplate.delete(TEST_TASK_KEY + processDefinition.getProjectId() + ":" + processDefinition.getName());
                redisTemplate.delete(DS_KEY + processDefinition.getProjectId() + ":" + processDefinition.getName());
            }
            if (Boolean.TRUE.equals(bean.getOriginProcess())) {
                initTaskList(processDefinition);
                IntegrationMessageDTO messageDTO = new IntegrationMessageDTO();
                messageDTO.setProjectId(processDefinition.getProjectId());
                messageDTO.setTenantCode(processDefinition.getTenantCode());
                messageDTO.setProcessDefinitionName(processDefinition.getName());
                messageDTO.setTaskIds(TaskUtils.getTaskIds(processDefinition, null));
                messageDTO.setProcessDefinitionId(id);
                try {
                    kafkaTemplate.send(TaskUtils.TASK_BINDING_INFO, UUID.randomUUID().toString(), JSON.toJSONString(messageDTO));
                } catch (Exception e) {
                    log.error("删除工作流发送kafka失败，发送消息内容是 {}");
                }
            }
            //删除工作流与任务的关联关系
            processTaskRelationService.deleteBy(new EqCondition("processDefinitionCode", id));
//        processTaskRelationLogService.deleteBy(new EqCondition("processDefinitionCode", id));

            //删除定时管理
            schedulerService.deleteBy(new EqCondition("processDefinitionCode", id));
            //删除工作流设置
            getService(ProcessDefinitionSetting.class).deleteBy(new EqCondition("processDefinitionId", id));
            //2025-03-04判断该目录下是否有任务，如果没有，进行删除
            int delCatalogCount = processDefinitionCatalogService.delCaseCatalog(processDefinition.getCatalogId());
            log.info("删除目录数量:{}", delCatalogCount);
        }
        //删除工作流log表
//        processDefinitionLogService.deleteBy(new EqCondition("processDefinitionId", id));
        return delete;
    }

    private String getUpdateTaskSql(String id) {
        String updateSql = "UPDATE scc_task \n" +
                "SET process_definition_code = NULL \n" +
                "WHERE\n" +
                "\tscc_task.process_definition_code = '%s'";
        return String.format(updateSql, id);
    }

    /**
     * 编辑工作流
     *
     * @param id
     * @param bean
     * @return
     */
    @Transactional
    @Override
    public Integer update(String id, ProcessDefinition bean) {
        String projectId = bean.getProjectId();
        String productId = bean.getProductId();
        //2024-12-30 10秒一次保存，10秒之内的保存不处理
        String processupdate = redisTemplate.opsForValue().get(PROCESS_UPDATE + id);
        if (null != processupdate) {
            return 0;
        }
        redisTemplate.opsForValue().set(PROCESS_UPDATE + id, String.valueOf(bean.toString().hashCode()), 10, TimeUnit.SECONDS);
        String releaseState = this.getQuery().eq("id", id).oneValue("releaseState", String.class);
        if (releaseState.equals(ProcessDefinition.ReleaseState.online)) {
            log.error("工作流是上线状态，不可编辑");
            throw new AppErrorException("工作流是上线状态，不可编辑！");
        }
        List<ProcessTaskRelation> taskRelationList = bean.getTaskRelationList();
        List<Task> taskList = bean.getTaskList();
        if (CollectionUtils.isEmpty(taskRelationList) || CollectionUtils.isEmpty(taskList)) {
            log.error("未创建节点，保存失败");
            throw new AppErrorException("未创建节点，保存失败！");
        }
        //更新前工作流信息
        ProcessDefinition processDefinition = initTaskList(this.getById(id));
        ProcessDefinition processDefinitionDeepCopy =
                JSONObject.parseObject(JSONObject.toJSONString(processDefinition), ProcessDefinition.class);

        Integer version = processDefinitionLogService.getQuery().eq("processDefinitionId", id).sortbyDesc("version").page(0, 1).oneValue("version", Integer.class);
        Integer finalVersion = version == null ? 1 : ++version;
        //判断是否有循环依赖 - 使用优化后的拓扑排序算法
        checkCyclicDependencyWithTopologicalSort(bean.getTaskRelationList());

        //校验全局变量中的key是否有重复的
        checkGlobalParamsKey(bean);
        List<String> taskIds = taskList.stream().map(Task::getId).collect(Collectors.toList());
        //每次编辑工作流时将，页面删掉的任务数据的工作流id改为0
        taskService.getSqlExecutor().excuteUpdate(getUpdateSql(taskIds, id));
        //删除旧的任务依赖关系
        processTaskRelationService.deleteBy(new EqCondition("processDefinitionCode", id));

        List<ProcessTaskRelation> taskRelationList1 = bean.getTaskRelationList();
        for (ProcessTaskRelation processTaskRelation : taskRelationList1) {
            processTaskRelation.setProjectId(projectId);
            processTaskRelation.setProcessDefinitionCode(id);
            processTaskRelation.setProcessDefinitionVersion(finalVersion);
            Integer postTaskVersion = processTaskRelation.getPostTaskVersion();
            processTaskRelation.setPostTaskVersion(postTaskVersion == null ? 1 : postTaskVersion + 1);
        }
        processTaskRelationService.add(taskRelationList1);
        //更新任务
        Map<String, Integer> taskVersion = taskLogService.getQuery().in("taskId", taskIds).groupBy("taskId").groupMax("version").map("taskId", "versionMax");
        taskList.forEach(t -> {
            t.setProcessDefinitionCode(id);
            //编辑工作流时任务版本+1
            t.setVersion(taskVersion.get(t.getId()) == null ? 1 : taskVersion.get(t.getId()) + 1);
            taskService.updateTask(t.getId(), t, false);
        });
        if (null == bean.getIsImport()) {
            bean.setIsImport(false);
        }
        //工作流对象赋值taskList,在initBody方法中使用
        processDefinition.setTaskList(taskList);
        //调海豚的接口
        ProcessDefinitionDTO body = initBody(bean, "1");
        Response response = updateProcessDefinitionByCode(body);
        if (response.getCode() != 0) {
            throw new AppErrorException("工作流修改失败！请联系管理员，报错信息：" + response.getMessage());
        }
        bean.setUpdateNull(true);
        JSONObject processDefinitionJson = JsonUtil.toJSON(bean);
        bean.setJsonKeys(processDefinitionJson.keySet());
        bean.setVersion(finalVersion);
        //2025-03-04 初始化目录id
        if (null != bean.getIsImport() && bean.getIsImport()) {
            //说明是外部使用，需要获取目录id
            Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initDefaultCatalogId(productId, projectId, bean.getCatalogNames());
            bean.setCatalogId(catalogTuple.getV1());
            bean.setCatalogParentIds(catalogTuple.getV2());
        }
        Integer update = super.update(id, bean);
        if (bean.getOriginProcess()) {
            IntegrationMessageDTO messageDTO = new IntegrationMessageDTO();
            messageDTO.setProjectId(bean.getProjectId());
            messageDTO.setTenantCode(bean.getTenantCode());
            messageDTO.setProcessDefinitionName(bean.getName());
            messageDTO.setTaskIds(TaskUtils.getTaskIds(processDefinitionDeepCopy, bean));
            messageDTO.setProcessDefinitionId(id);
            try {
                kafkaTemplate.send(TaskUtils.TASK_BINDING_INFO, UUID.randomUUID().toString(), JSON.toJSONString(messageDTO));
            } catch (Exception e) {
                log.error("删除工作流发送kafka失败，发送消息内容是 {}");
            }
        }
        int delCatalogCount = processDefinitionCatalogService.delCaseCatalog(processDefinition.getCatalogId());
        log.info("删除目录数量:{}", delCatalogCount);
        return update;
    }

    public void checkGlobalParamsKey(ProcessDefinition processDefinition) {
        String globalParams = processDefinition.getGlobalParamMap();
        if (null != globalParams && !globalParams.equals("")) {
            JSONArray jsonArray = JsonUtil.toJSONArray(globalParams);
            if (null != jsonArray && jsonArray.size() > 0) {
                Set<String> key = new HashSet<>();
                for (Object object : jsonArray) {
                    JSONObject jsonObject = JsonUtil.toJSON(object);
                    key.add(jsonObject.getString("prop"));
                }
                if (key.size() != jsonArray.size()) {
                    throw new AppErrorException("全局变量中有重复的key，保存失败！");
                }
            }
        }
    }

    //海豚新增工作流
    private Response createProcessDefinition(ProcessDefinitionDTO processDefinitionDTO) throws UnsupportedEncodingException {
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", processDefinitionDTO.getProjectCode());
        String check = preUrl + Constants.PROCESS_VERIFY_NAME + "?name=" + URLEncoder.encode(processDefinitionDTO.getName(), "UTF-8");
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.get4url(check, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) == 0) {
            Map saveMap = Utils.webClientPost(preUrl, JsonUtil.toMap(processDefinitionDTO, Map.class), headers.get(Constants.SESSION_ID).toString());
            return Utils.responseInfo(saveMap);
        } else {
            return Utils.responseInfo(resultMap);
        }
    }

    //海豚删除工作流
    public Response deleteProcessDefinitionByCode(String code, String projectId) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_DELETE.replace("{projectCode}", projectId).replace("{code}", code);
        Map result = httpRequestFeignService.delete4url(url, null, headers, Map.class);
        return Utils.responseInfo(result);
    }

    //海豚编辑工作流
    private Response updateProcessDefinitionByCode(ProcessDefinitionDTO processDefinitionDTO) {
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", processDefinitionDTO.getProjectCode());
        Map<String, Object> headers = Utils.getHeader();
        Map saveMap = Utils.webClientPut(preUrl + "/" + processDefinitionDTO.getCode(), JsonUtil.toMap(processDefinitionDTO, Map.class), headers.get(Constants.SESSION_ID).toString());
        return Utils.responseInfo(saveMap);
    }

    public ProcessDefinitionDTO initBody(ProcessDefinition processDefinition, String updateVersion) {
        List<ProcessTaskRelation> addProcessTaskRelationList = getService(ProcessTaskRelation.class).getQuery().eq("processDefinitionCode", processDefinition.getId()).list();
        //20240827 批量发布调度任务时候，根据taskIds无法查询到任务。
        List<Task> taskList = processDefinition.getTaskList();//getService(Task.class).getQuery().in("id", taskIds).list();
        ProcessDefinitionDTO body = new ProcessDefinitionDTO();
        List<TaskDTO> taskDTOList = new ArrayList<>();
        if (null != taskList && taskList.size() > 0) {
            for (Task task : taskList) {
                task.setVersion(null);
                if (2 == task.getTaskTimeoutStrategy() || 3 == task.getTaskTimeoutStrategy()) {
                    task.setTimeout(Task.conversionUnit(task.getTaskTimeout(), task.getTimeoutUnit()));
                }
                taskDTOList.add(new TaskDTO(task));
            }
        }
        if (null != addProcessTaskRelationList && addProcessTaskRelationList.size() > 0) {
            for (ProcessTaskRelation processTaskRelation : addProcessTaskRelationList) {
                //海豚的工作流与任务的关联关系表的id是int类型，所以用dbid
                processTaskRelation.setId(processTaskRelation.getDbid().toString());
                //这个字段暂时给null，不然会报错，现在没用到
                processTaskRelation.setConditionParams("null");
            }
        }
        JSONArray taskDTOListJson = JsonUtil.toJSONArray(taskDTOList);
        JSONArray addProcessTaskRelationJson = JsonUtil.toJSONArray(addProcessTaskRelationList);
        body.setCode(processDefinition.getId());
        body.setProjectCode(processDefinition.getProjectId());
        body.setDescription(processDefinition.getDescription());
        body.setGlobalParams(processDefinition.getGlobalParamMap());
        body.setName(processDefinition.getName());
        body.setLocations(processDefinition.getLocations());
        body.setReleaseState(ReleaseState.getEnum(processDefinition.getReleaseState()).getValue());
        body.setTaskDefinitionJson(taskDTOListJson.toJSONString());
        body.setTaskRelationJson(addProcessTaskRelationJson.toJSONString());
        body.setExecutionType(processDefinition.getExecutionType());
        body.setTenantCode(processDefinition.getTenantCode());
        body.setUpdateVersion(updateVersion);
        body.setCatalogId(processDefinition.getCatalogId());
        body.setCatalogParentIds(processDefinition.getCatalogParentIds());
        return body;
    }

    private ProcessDefinitionLog initProcessDefinitionLog(ProcessDefinition processDefinition) {
        ProcessDefinitionLog processDefinitionLog = new ProcessDefinitionLog();
        processDefinitionLog.setProcessDefinitionId(processDefinition.getId());
        processDefinitionLog.setProjectId(processDefinition.getProjectId());
        processDefinitionLog.setName(processDefinition.getName());
        processDefinitionLog.setVersion(processDefinition.getVersion());
        processDefinitionLog.setDescription(processDefinition.getDescription());
        processDefinitionLog.setReleaseState(processDefinition.getReleaseState());
        processDefinitionLog.setGlobalParamMap(processDefinition.getGlobalParamMap());
        processDefinitionLog.setLocations(processDefinition.getLocations());
        processDefinitionLog.setExecutionType(processDefinition.getExecutionType());
        processDefinitionLog.setProductId(processDefinition.getProductId());
        processDefinitionLog.setTaskIds(processDefinition.getTaskIds());
//        processDefinitionLog.setIsSubmit(processDefinition.getIsSubmit());
        return processDefinitionLog;
    }


    private List<ProcessTaskRelation> addProcessTaskRelation(String taskId, String addRelyTaskId, Integer version, String processDefinitionId) {
        ProcessTaskRelation processTaskRelation = new ProcessTaskRelation();
        processTaskRelation.setPostTaskVersion(1);
        processTaskRelation.setPostTaskCode(addRelyTaskId);
        processTaskRelation.setPreTaskCode("0");
        processTaskRelation.setProcessDefinitionCode(processDefinitionId);
        processTaskRelation.setPreTaskVersion(0);
        processTaskRelation.setConditionType("NONE");

        ProcessTaskRelation processTaskRelation1 = new ProcessTaskRelation();
        processTaskRelation1.setPostTaskVersion(version);
        processTaskRelation1.setPostTaskCode(taskId);
        processTaskRelation1.setPreTaskCode(addRelyTaskId);
        processTaskRelation1.setPreTaskVersion(1);
        processTaskRelation1.setProcessDefinitionCode(processDefinitionId);
        processTaskRelation1.setConditionType("NONE");
        List<ProcessTaskRelation> list = new ArrayList<>();
        list.add(processTaskRelation);
        list.add(processTaskRelation1);
        return list;
    }

    /**
     * 使用拓扑排序检查循环依赖 - 优化版本
     * 时间复杂度: O(V + E)，其中V是任务数，E是依赖关系数
     *
     * @param taskRelationList 任务依赖关系列表
     * @throws AppErrorException 如果存在循环依赖
     */
    private void checkCyclicDependencyWithTopologicalSort(List<ProcessTaskRelation> taskRelationList) {
        if (CollectionUtils.isEmpty(taskRelationList)) {
            return;
        }

        // 构建邻接表和入度表
        Map<String, List<String>> adjacencyList = new HashMap<>();
        Map<String, Integer> inDegree = new HashMap<>();
        Set<String> allTasks = new HashSet<>();

        // 初始化图结构
        for (ProcessTaskRelation relation : taskRelationList) {
            String preTask = relation.getPreTaskCode();
            String postTask = relation.getPostTaskCode();

            // 跳过起始节点（preTaskCode为"0"）
            if ("0".equals(preTask)) {
                allTasks.add(postTask);
                inDegree.putIfAbsent(postTask, 0);
                continue;
            }

            allTasks.add(preTask);
            allTasks.add(postTask);

            // 构建邻接表：preTask -> postTask
            adjacencyList.computeIfAbsent(preTask, k -> new ArrayList<>()).add(postTask);

            // 计算入度
            inDegree.put(postTask, inDegree.getOrDefault(postTask, 0) + 1);
            inDegree.putIfAbsent(preTask, 0);
        }

        // 拓扑排序
        Queue<String> queue = new LinkedList<>();
        int processedCount = 0;

        // 将入度为0的节点加入队列
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }

        // 处理队列中的节点
        while (!queue.isEmpty()) {
            String currentTask = queue.poll();
            processedCount++;

            // 处理当前节点的所有邻接节点
            List<String> neighbors = adjacencyList.get(currentTask);
            if (neighbors != null) {
                for (String neighbor : neighbors) {
                    int newInDegree = inDegree.get(neighbor) - 1;
                    inDegree.put(neighbor, newInDegree);

                    if (newInDegree == 0) {
                        queue.offer(neighbor);
                    }
                }
            }
        }

        // 如果处理的节点数少于总节点数，说明存在循环依赖
        if (processedCount < allTasks.size()) {
            // 找出参与循环的节点
            List<String> cyclicTasks = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
                if (entry.getValue() > 0) {
                    cyclicTasks.add(entry.getKey());
                }
            }

            log.error("检测到循环依赖，涉及的任务: {}", cyclicTasks);
            throw new AppErrorException("流程节点间存在循环依赖，涉及任务: " + String.join(", ", cyclicTasks));
        }

        log.debug("循环依赖检查完成，共处理 {} 个任务节点", processedCount);
    }

    /**
     * 检查工作流的任务依赖关系是否有循环的 - 保留原方法作为备用
     *
     * @deprecated 使用 checkCyclicDependencyWithTopologicalSort 替代，性能更好
     */
    @Deprecated
    private void checkTaskRelationList(String postTaskCode, ProcessTaskRelation processTaskRelation, Map<String, List<ProcessTaskRelation>> map) {
        if (null != processTaskRelation) {
            String preTaskCode = processTaskRelation.getPreTaskCode();
            if (StringUtils.isNotBlank(preTaskCode) && !preTaskCode.equals("0")) {
                if (postTaskCode.equals(preTaskCode)) {
                    throw new AppErrorException("流程节点间存在循环依赖，保存失败！");
                }
                List<ProcessTaskRelation> list = map.get(processTaskRelation.getPreTaskCode());
                if (list != null) {
                    for (ProcessTaskRelation preProcessTaskRelation : list) {
                        checkTaskRelationList(postTaskCode, preProcessTaskRelation, map);
                    }
                }
            }
        }
    }

    /**
     * 运行任务
     *
     * @param param
     * @return
     */
    public Map startProcessInstance(String id, StartParam param, boolean returnInstanceId) throws UnsupportedEncodingException {
        ProcessDefinition processDefinition = this.getQuery().eq("id", id).one();
        initStartParam(processDefinition, param);
        String preUrl = dolphinscheduler + Constants.EXECUTORS_START_PROCESS_INSTANCE.replace("{projectCode}", param.getProjectId());
        String scheduleTime = param.getScheduleTime();
        if (null != scheduleTime) {
            scheduleTime = URLEncoder.encode(scheduleTime, "UTF-8");
        }
        String url = preUrl + "?processDefinitionCode=" + param.getProcessDefinitionCode()
                + "&failureStrategy=" + param.getFailureStrategy()
                + "&warningType=" + param.getWarningType()
                + "&warningGroupId=" + param.getWarningGroupId()
                + "&execType=" + param.getExecType()
                + "&startNodeList=" + StringUtils.defaultIfBlank(param.getStartNodeList(), "")
                + "&taskDependType=" + param.getTaskDependType()
                + "&complementDependentMode=" + param.getComplementDependentMode()
                + "&runMode=" + param.getRunMode()
                + "&processInstancePriority=" + param.getProcessInstancePriority()
                + "&workerGroup=" + param.getWorkerGroup()
                + "&environmentCode=" + param.getEnvironmentCode()
                + "&startParams=" + URLEncoder.encode(StringUtils.defaultIfBlank(param.getStartParams(), ""), "UTF-8")
                + "&expectedParallelismNumber=" + Integer.parseInt(StringUtils.defaultIfBlank(param.getExpectedParallelismNumber(), "0"))
                + "&dryRun=" + param.getDryRun()
                + "&testFlag=" + param.getTestFlag()
                + "&scheduleTime=" + scheduleTime;
        if (returnInstanceId) {
            url = url + "&returnInstanceId=true";
        }
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.post4url(url, null, headers, Map.class);
        return resultMap;
    }

    /**
     * 运行任务
     * 供批量运行调用，工作流id，项目id前端传到param对象中
     *
     * @param param
     * @return
     */
    public Map startProcessInstance(StartParam param) throws UnsupportedEncodingException {
        initStartParam(null, param);
        String preUrl = dolphinscheduler + Constants.EXECUTORS_START_PROCESS_INSTANCE.replace("{projectCode}", param.getProjectId());
        String scheduleTime = param.getScheduleTime();
        if (null != scheduleTime) {
            scheduleTime = URLEncoder.encode(scheduleTime, "UTF-8");
        }
        String url = preUrl + "?processDefinitionCode=" + param.getProcessDefinitionCode()
                + "&failureStrategy=" + param.getFailureStrategy()
                + "&warningType=" + param.getWarningType()
                + "&warningGroupId=" + param.getWarningGroupId()
                + "&execType=" + param.getExecType()
                + "&startNodeList=" + StringUtils.defaultIfBlank(param.getStartNodeList(), "")
                + "&taskDependType=" + param.getTaskDependType()
                + "&complementDependentMode=" + param.getComplementDependentMode()
                + "&runMode=" + param.getRunMode()
                + "&processInstancePriority=" + param.getProcessInstancePriority()
                + "&workerGroup=" + param.getWorkerGroup()
                + "&environmentCode=" + param.getEnvironmentCode()
                + "&startParams=" + URLEncoder.encode(StringUtils.defaultIfBlank(param.getStartParams(), ""), "UTF-8")
                + "&expectedParallelismNumber=" + Integer.parseInt(StringUtils.defaultIfBlank(param.getExpectedParallelismNumber(), "0"))
                + "&dryRun=" + param.getDryRun()
                + "&testFlag=" + param.getTestFlag()
                + "&scheduleTime=" + scheduleTime;
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.post4url(url, null, headers, Map.class);
        return resultMap;
    }

    /**
     * 批量运行工作流定义
     *
     * @param startParams
     * @return
     */
    public List<JSONObject> batchStartProcessInstance(List<StartParam> startParams) {
        List<JSONObject> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(startParams)) {
            return result;
        }
        startParams.forEach(t -> {
            String processDefinitionName = t.getProcessDefinitionName();
            try {
                Map map = startProcessInstance(t);
                if (Integer.parseInt(String.valueOf(map.get("code"))) != 0) {
                    //运行失败
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", processDefinitionName);
                    jsonObject.put("status", "error");
                    jsonObject.put("errormsg", map.get("msg").toString());
                    result.add(jsonObject);
                } else {
                    //运行成功
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", processDefinitionName);
                    jsonObject.put("status", "success");
                    result.add(jsonObject);
                }
            } catch (UnsupportedEncodingException e) {
                //报错算失败
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", processDefinitionName);
                jsonObject.put("status", "error");
                jsonObject.put("errormsg", e.getMessage());
                result.add(jsonObject);
            }
        });
        return result;
    }

    /**
     * init运行参数对象
     *
     * @param processDefinition
     * @param startParam
     */
    public void initStartParam(ProcessDefinition processDefinition, StartParam startParam) {
        if (null != processDefinition) {
            String processDefinitionId = processDefinition.getId();
            if (StringUtils.isNotBlank(processDefinitionId)) {
                startParam.setProcessDefinitionCode(processDefinitionId);
            }
            String projectId = processDefinition.getProjectId();
            if (StringUtils.isNotBlank(projectId)) {
                startParam.setProjectId(projectId);
            }
        }
        if (StringUtils.isBlank(startParam.getComplementDependentMode())) {
            startParam.setComplementDependentMode("OFF_MODE");//海豚默认值
        }
        startParam.setTaskDependType(StringUtils.defaultString(startParam.getTaskDependType(), "TASK_POST"));//海豚默认值 (运行当前任务和依赖于当前任务的其他任务)
        if (StringUtils.isBlank(startParam.getRunMode())) {
            startParam.setRunMode("RUN_MODE_SERIAL");//海豚默认值 串行
        }
        startParam.setExecType(startParam.getExecType());
        startParam.setScheduleTime(startParam.getScheduleTime());
        startParam.setWarningType("NONE");
        if (StringUtils.isBlank(startParam.getExpectedParallelismNumber())) {
            startParam.setExpectedParallelismNumber("0");
        }
        if (startParam.getEnvironmentCode() == null) {
            startParam.setEnvironmentCode(-1L);
        }
    }

    /**
     * 上线、下线
     *
     * @param id 工作流id
     */
    @Transactional
    public void release(String id) {
        ProcessDefinition processDefinition = this.getById(id);
        String projectId = processDefinition.getProjectId();
        String tenantCode = processDefinition.getTenantCode();

        Release release = new Release();
        release.setProjectCode(projectId);
        String errorExplain = "";
        if (processDefinition.getReleaseState().equals(ProcessDefinition.ReleaseState.online)) {
            processDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
            this.update(id, processDefinition, false);
            // 工作流下线时 定时任务也要下线
            Scheduler schedule = schedulerService.getQuery().eq("processDefinitionCode", id).one();
            if (null != schedule && schedule.getReleaseState().equals(Scheduler.ReleaseState.online)) {
                schedulerService.offline(schedule);
            }
            release.setReleaseState(Release.ReleaseState.OFFLINE);
            errorExplain = "下线";
        } else {
            processDefinition.setReleaseState(ProcessDefinition.ReleaseState.online);
            this.update(id, processDefinition, false);
            release.setReleaseState(Release.ReleaseState.ONLINE);
            errorExplain = "上线";
        }

        if (processDefinition.getOriginProcess()) {
            IntegrationMessageDTO messageDTO = new IntegrationMessageDTO();
            messageDTO.setProjectId(projectId);
            messageDTO.setTenantCode(tenantCode);
            messageDTO.setProcessDefinitionName(processDefinition.getName());
            Map<String, List<String>> map = new HashMap<>();
            map.put("update", Arrays.asList(processDefinition.getTaskIds().split(",")));
            messageDTO.setTaskIds(map);
            messageDTO.setReleaseState(errorExplain);
            messageDTO.setProcessDefinitionId(id);
            try {
                kafkaTemplate.send(TaskUtils.TASK_BINDING_INFO, UUID.randomUUID().toString(), JSON.toJSONString(messageDTO));
            } catch (Exception e) {
                log.error("删除工作流发送kafka失败，发送消息内容是 {}");
            }
        }

        Map<String, Object> headers = Utils.getHeader();
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", release.getProjectCode());
        String uri = Constants.PROCESS_RELEASE.replace("{code}", id) + "?releaseState=" + release.getReleaseState();
        Map resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
            throw new AppErrorException("工作流" + errorExplain + "失败！请联系管理员，报错信息：" + resultMap.get("msg"));
        }

        if ("上线".equals(errorExplain)) {
            //上线工作流定义，任务添加到检查列表
            checkTaskService.addByProcessDefinitionId(projectId, id, tenantCode);
            log.info("到点未运行检查任务存储结束");
        } else if ("下线".equals(errorExplain)) {
            //删除任务检查表
            Integer delCheckTaskCount = checkTaskService.deleteBy(new EqCondition("processDefinitionId", id));
            log.info("下线工作流任务删除到点检查任务表数量 = {}", delCheckTaskCount);
        }
    }

    @Transactional
    public void switchVersion(String id, Integer version) {
        ProcessDefinition processDefinition = this.getQuery().eq("id", id).one();
        if (processDefinition.getReleaseState().equals(ProcessDefinition.ReleaseState.online)) {
            throw new AppErrorException("该工作流已上线，不可以切换版本！");
        }
        //查出要切换的工作流版本并切换
        ProcessDefinitionLog processDefinitionLog = processDefinitionLogService.getQuery().eq("processDefinitionId", id).eq("version", version).one();
        initProcessDefinition(processDefinition, processDefinitionLog);
        //查出要切换的工作流与任务关联关系数据，调用重写的update
        List<ProcessTaskRelationLog> processTaskRelationLogList = processTaskRelationLogService.getQuery().eq("processDefinitionCode", id).eq("processDefinitionVersion", version).list();
        List<ProcessTaskRelation> processTaskRelationList = JsonUtil.toBeanList(processTaskRelationLogList, ProcessTaskRelation.class);
        //删除旧的任务依赖关系
        processTaskRelationService.deleteBy(new EqCondition("processDefinitionCode", id));
        processTaskRelationService.add(processTaskRelationList);

        List<String> postTaskCodes = processTaskRelationList.stream().map(ProcessTaskRelation::getPostTaskCode).collect(Collectors.toList());
        //切换工作流版本时将没用到的任务的工作流id改为0
        taskService.getSqlExecutor().excuteUpdate(getUpdateSql(postTaskCodes, id));
        List<TaskLog> taskLogList = new ArrayList<>();
        for (ProcessTaskRelation processTaskRelation : processTaskRelationList) {
            TaskLog taskLog = taskLogService.getQuery().eq("taskId", processTaskRelation.getPostTaskCode()).eq("version", processTaskRelation.getPostTaskVersion()).one();
            taskLogList.add(taskLog);
        }
        List<Task> taskList = initTask(taskLogList, id);
        taskList.forEach(task -> taskService.update(task, false));
//        //查出要切换的任务版本
//        List<TaskLog> taskLogList = taskLogService.getQuery().eq("processDefinitionCode", id).eq("version", version).list();
//        List<Task> taskList = initTask(taskLogList);
//        //替换任务版本
//        taskList.forEach(task -> taskService.update(task, true));

        processDefinition.setUpdateNull(true);
        this.update(processDefinition.getId(), processDefinition, true);
        // 调海豚的切换版本
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.SWITCH_PROCESS_VERSION.replace("{projectCode}", processDefinition.getProjectId()) + "/" + processDefinition.getId() + "/versions/" + version + "?projectCode=" + processDefinition.getProjectId();
        Map resultMap = httpRequestFeignService.get4url(url, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
            throw new AppErrorException("切换工作流版本失败！请联系管理员，报错信息：" + resultMap.get("msg"));
        }
    }

    private static String getUpdateSql(List<String> postTaskCodes, String processDefinitionCode) {
        String updateSql = "UPDATE scc_task \n" +
                "SET process_definition_code = '%s' \n" +
                "WHERE\n" +
                "\tprocess_definition_code = '%s' \n" +
                "\tAND scc_task.id NOT IN ('%s')";
        String join = String.join("','", postTaskCodes);
        return String.format(updateSql, "0", processDefinitionCode, join);
    }


    private List<Task> initTask(List<TaskLog> taskLogList, String processDefinitionCode) {
        List<Task> result = new ArrayList<>();
        if (null != taskLogList && taskLogList.size() > 0) {
            taskLogList.forEach(t -> {
                Task task = JSONObject.parseObject(JSONObject.toJSONString(t), Task.class);
                task.setId(t.getTaskId());
                task.setProcessDefinitionCode(processDefinitionCode);
                //不更新创建时间和最后更新时间字段
                task.setCreateTime(null);
                task.setLastModificationTime(null);
                result.add(task);
            });
        }
        return result;
    }

    /**
     * 切换版本时init工作流对象
     *
     * @param processDefinition
     * @param processDefinitionLog
     */
    private void initProcessDefinition(ProcessDefinition processDefinition, ProcessDefinitionLog processDefinitionLog) {
        processDefinition.setName(processDefinitionLog.getName());
        processDefinition.setVersion(processDefinitionLog.getVersion());
        processDefinition.setReleaseState(processDefinitionLog.getReleaseState());
        processDefinition.setDescription(processDefinitionLog.getDescription());
        processDefinition.setGlobalParamMap(processDefinitionLog.getGlobalParamMap());
        processDefinition.setExecutionType(processDefinitionLog.getExecutionType());
        processDefinition.setLocations(processDefinitionLog.getLocations());
//        processDefinition.setTimingState(processDefinitionLog.getTimingState());
    }

    /**
     * 复制工作流
     *
     * @param processDefinition
     */
    @Transactional
    public void copyProcessDefinition(ProcessDefinition processDefinition, String productId) throws UnsupportedEncodingException {
        String id = processDefinition.getId();
        String newProcessDefinitionId = GeneratorUtil.genCode();
        User user = ThreadLocalUserUtil.getUser(User.class);
        Date date = new Date();
        //复制任务表  先复制任务表  因为工作流locations字段信息中的任务id需要更换成新的任务id
        List<ProcessTaskRelation> processTaskRelationList = processTaskRelationService.getQuery().eq("processDefinitionCode", id).list();
        List<String> taskIds = processTaskRelationList.stream().map(ProcessTaskRelation::getPostTaskCode).collect(Collectors.toList());
        IService<Task> taskIService = getService(Task.class);
        List<Task> taskList = taskIService.getQuery().in("id", taskIds).list();
        //保留新旧任务id的对应关系
        Map<String, String> oldMap = new HashMap<>();
        //保留新id
        List<String> newTaskIds = new ArrayList<>();
        taskList.forEach(task -> {
            if (null != task.getProductId() && !task.getProductId().equals(productId)) {
                throw new RuntimeException("工作流中包含非调度中心的任务不允许克隆！");
            }
            task.setName(Utils.getNewName(task.getName(), Constants.COPY_SUFFIX));
            String taskId = task.getId();
            task.setId(null);
            task.setVersion(1);
            task.setProcessDefinitionCode(newProcessDefinitionId);
//            task.setSubmitTime(null);
//            task.setStatus(Task.Status.notSubmitted);
            task.setCreateBy(user.getId());
            task.setCreateByName(user.getNickname());
            task.setCreateTime(date);
            //当成调度新增任务
            task.setIsImport(false);
            task.setProductId(productId);
            Task addTask = taskIService.add(task);
            oldMap.put(taskId, addTask.getId());
            newTaskIds.add(addTask.getId());
        });

        String newName = Utils.getNewName(processDefinition.getName(), Constants.COPY_SUFFIX);
        String locations = processDefinition.getLocations();
        //将locations里的旧任务id替换为新的
        Set<String> keySet = oldMap.keySet();
        for (String key : keySet) {
            String target = "\"taskCode\":\"" + key + "\"";
            String replacement = "\"taskCode\":\"" + oldMap.get(key) + "\"";
            locations = locations.replace(target, replacement);
        }
        processDefinition.setLocations(locations);
        processDefinition.setVersion(1);
        processDefinition.setId(newProcessDefinitionId);
        processDefinition.setName(newName);
        processDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
//        processDefinition.setIsSubmit(ProcessDefinition.IsSubmit.notSubmitted);
        processDefinition.setCreateBy(user.getId());
        processDefinition.setCreateByName(user.getNickname());
        processDefinition.setCreateTime(date);
        //当成调度新增任务
        processDefinition.setIsImport(false);
        processDefinition.setProductId(productId);
        ProcessDefinition addProcessDefinition = this.add(processDefinition);
//        ProcessDefinitionLog processDefinitionLog = initProcessDefinitionLog(addProcessDefinition);
//        processDefinitionLogService.add(processDefinitionLog);

        //复制工作流与任务的关联关系表
        processTaskRelationList.forEach(processTaskRelation -> {
            processTaskRelation.setPostTaskCode(oldMap.get(processTaskRelation.getPostTaskCode()));
            if (!processTaskRelation.getPreTaskCode().equals("0")) {
                processTaskRelation.setPreTaskCode(oldMap.get(processTaskRelation.getPreTaskCode()));
            }
            processTaskRelation.setPostTaskVersion(1);
            processTaskRelation.setPreTaskVersion(1);
            processTaskRelation.setProcessDefinitionCode(addProcessDefinition.getId());
            processTaskRelation.setProcessDefinitionVersion(1);
            processTaskRelation.setId(null);
            processTaskRelation.setCreateBy(user.getId());
            processTaskRelation.setCreateByName(user.getNickname());
            processTaskRelation.setCreateTime(date);
        });
        processTaskRelationService.add(processTaskRelationList);
//        List<ProcessTaskRelationLog> processTaskRelationLogList = JsonUtil.toBeanList(processTaskRelationList, ProcessTaskRelationLog.class);
//        processTaskRelationLogService.add(processTaskRelationLogList);

        //想让海豚复制的工作流和batch中的一样  就得调新增接口   调海豚的复制接口  任务code对应不上
        //工作流对象赋值taskList,在initBody方法中使用
        processDefinition.setTaskList(taskList);
        //组装body
        ProcessDefinitionDTO body = initBody(addProcessDefinition, "0");
        Response response = this.createProcessDefinition(body);
        if (response.getCode() != 0) {
            throw new AppErrorException("复制工作流失败！请联系管理员，报错信息：" + response.getMessage());
        }
    }

    /**
     * 树形图
     *
     * @param id
     * @return
     */
    public Object viewTree(String id, String projectCode) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectCode) + "/" + id
                + Constants.PROCESS_VIEW_TREE + "?limit=100";
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        Response response = Utils.responseInfo(map);
        if (response.getCode() != 0) {
            if (response.getMessage().contains("does not exist")) {
                throw new AppErrorException("该工作流已被删除！");
            }
            throw new AppErrorException("查看树形图失败！请联系管理员，报错信息：" + response.getMessage());
        }
        JSONObject jsonObject = (JSONObject) response.getResult();
        JSONArray children = (JSONArray) jsonObject.get("children");
        List<Instance> instances = JSONArray.parseArray(jsonObject.getJSONArray("instances").toJSONString(),
                Instance.class);
        if (null != instances && instances.size() > 0) {
            //根据时间排序取最新一条工作流实例
            List<Instance> instanceList = instances.stream().sorted(Comparator.comparing(Instance::getId).reversed()).collect(Collectors.toList());
            disposeViewTreeResult(children, projectCode, headers, instanceList.get(0).getId());
        }
        return response.getResult();
    }

    /**
     * 处理树形图数据，树形图中的实例对象取最新一条判断执行状态返回给前端
     *
     * @param children
     */
    private void disposeViewTreeResult(JSONArray children, String projectCode, Map<String, Object> headers, String instanceId) {
        //获取工作流实例最新任务状态
        String tasksUrl = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectCode) + "/" + instanceId + "/tasks";
        Map tasksResponse = httpRequestFeignService.get4url(tasksUrl, headers, Map.class);
        if (Integer.parseInt(String.valueOf(tasksResponse.get("code"))) != 0) {
            throw new AppErrorException("查看工作流实例失败！请联系管理员，报错信息：" + tasksResponse.get("msg").toString());
        }
        //获取执行的任务实例
        JSONArray taskList = JSONObject.parseObject(JSONObject.toJSONString(tasksResponse.get("data"))).getJSONArray("taskList");
        if (null != taskList && taskList.size() > 0) {
            List<String> instanceIdList = taskList.stream().map(t -> JSONObject.parseObject(JSONObject.toJSONString(t)).getString("id")).collect(Collectors.toList());
            for (int i = 0; i < children.size(); i++) {
                JSONObject task = (JSONObject) children.get(i);
                JSONArray instanceJSONArray = (JSONArray) task.get("instances");
                List<Instance> instanceList = JsonUtil.toBeanList(instanceJSONArray, Instance.class);
                task.put("status", "未开始");
                if (null != instanceList && instanceList.size() > 0) {
                    Optional<Instance> instanceOptional = instanceList.stream().filter(t -> instanceIdList.contains(t.getId())).findAny();
                    //如果最新执行的任务实例中有该任务
                    if (instanceOptional.isPresent()) {
                        //根据实例开始时间倒序排，获取到最后一次执行的实例
                        List<Instance> sortedList = instanceList.stream().filter(instance -> instance.getStartTime() != null).sorted(Comparator.comparing(Instance::getId).reversed()).collect(Collectors.toList());
                        //防止出现下标越界异常
                        if (sortedList.size() == 0) {
                            continue;
                        }
                        String state = sortedList.get(0).getState();
                        if (null != state) {
                            if (state.contains("success")) {
                                task.put("status", "成功");
                            } else if (state.contains("failure")) {
                                task.put("status", "失败");
                            }
                        }
                    }
                }
                //判断还有没有子级  如果有就再调这个方法(递归)
                JSONArray children1 = (JSONArray) task.get("children");
                if (null != children1 && children1.size() > 0) {
                    disposeViewTreeResult(children1, projectCode, headers, instanceId);
                }
            }
        }
    }

    /**
     * 导入json文件
     *
     * @param file
     * @param projectId
     */
    @Transactional
    public List<JSONObject> importProcess(MultipartFile file, String projectId, String productId) throws UnsupportedEncodingException {
        String fileName = file.getOriginalFilename() == null ? file.getName() : file.getOriginalFilename();
        int lastIndexOf = fileName.lastIndexOf(".");
        String substring = fileName.substring(lastIndexOf);
        if (!substring.equals(".json")) {
            throw new AppErrorException("需要导入json类型的文件！");
        }
        List<ProcessExportDTO> processExportDTOList;
        try {
            String fileToString = fileToString(file);
            JSONArray jsonArray = JsonUtil.toJSONArray(fileToString);
            processExportDTOList = JsonUtil.toBeanList(jsonArray, ProcessExportDTO.class);
        } catch (Exception e) {
            throw new AppErrorException("数据为空或是无效数据！");
        }
        if (null == processExportDTOList || processExportDTOList.size() < 1) {
            throw new AppErrorException("数据为空或是无效数据！");
        }
        List<JSONObject> result = new ArrayList<>();
        for (ProcessExportDTO processExportDTO : processExportDTOList) {
            JSONObject json = new JSONObject();
            json.put("name", processExportDTO.getProcessDefinition().getName());
            try {
                importData(processExportDTO, projectId, productId);
                json.put("status", "success");
            } catch (Exception e) {
                log.error("导入失败:", e);
                json.put("errorMsg", e.getMessage());
                json.put("status", "fail");
            }
            result.add(json);
        }
        return result;
    }

    @Transactional
    public List<JSONObject> importProcessAll(MultipartFile file, String projectId, String productId) throws UnsupportedEncodingException {
        String fileName = file.getOriginalFilename() == null ? file.getName() : file.getOriginalFilename();
        int lastIndexOf = fileName.lastIndexOf(".");
        String substring = fileName.substring(lastIndexOf);
        if (!substring.equals(".json")) {
            throw new AppErrorException("需要导入json类型的文件！");
        }
        List<ProcessExportDTO> processExportDTOList;
        try {
            String fileToString = fileToString(file);
            JSONArray jsonArray = JsonUtil.toJSONArray(fileToString);
            processExportDTOList = JsonUtil.toBeanList(jsonArray, ProcessExportDTO.class);
        } catch (Exception e) {
            throw new AppErrorException("数据为空或是无效数据！");
        }
        if (null == processExportDTOList || processExportDTOList.size() < 1) {
            throw new AppErrorException("数据为空或是无效数据！");
        }
        List<JSONObject> result = new ArrayList<>();
        for (ProcessExportDTO processExportDTO : processExportDTOList) {
            JSONObject json = new JSONObject();
            json.put("name", processExportDTO.getProcessDefinition().getName());
            try {
                importData(processExportDTO, projectId, productId);
                json.put("status", "success");
            } catch (Exception e) {
                log.error("导入失败:", e);
                json.put("errorMsg", e.getMessage());
                json.put("status", "fail");
            }
            result.add(json);
        }
        return result;
    }

    /**
     * 将导入的数据存到数据库
     *
     * @param processExportDTO
     * @param projectId
     */
    @Transactional
    public void importData(ProcessExportDTO processExportDTO, String projectId, String productId) throws UnsupportedEncodingException {
        User user = ThreadLocalUserUtil.getUser(User.class);
        String tenantCode = user.getTenantCode();
        Date date = new Date();
        //导入工作流对象
        String newProcessId = GeneratorUtil.genCode();
        ProcessDefinition processDefinition = processExportDTO.getProcessDefinition();
        processDefinition.setProductId(productId);
        processDefinition.setProjectId(projectId);
        processDefinition.setId(newProcessId);
        processDefinition.setIsImport(false);
        processDefinition.setVersion(1);
        processDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
        //2024-12-23 入库时检查，名字如果重复，就加后缀，如果不重复，就不加后缀
        processDefinition.setName(checkName(processDefinition.getName(), null, "process", projectId));
        processDefinition.setCreateBy(user.getId());
        processDefinition.setCreateByName(user.getNickname());
        processDefinition.setCreateTime(date);
        processDefinition.setTenantCode(tenantCode);
        processDefinition.setGlobalParamMap(processDefinition.getGlobalParams());
        //导入任务对象
        List<Task> taskList = processExportDTO.getTaskDefinitionList();
        if (null == taskList || taskList.size() < 1) {
            throw new AppErrorException("数据为空或是无效数据！");
        }
        //保留新旧id的对应关系
        Map<String, String> oldTaskIdMap = new HashMap<>();
        for (Task task : taskList) {
            task.setProductId(null != task.getProductId() ? task.getProductId() : productId);
            task.setProjectId(projectId);
            if ("STX".equals(task.getTaskType())) {
                //如果是STX任务要去判断任务管理中是否存在，如果不存在直接报错
                Task stxTask = updateSTXTask(oldTaskIdMap, task, newProcessId);
                getService(Task.class).update(stxTask);
                BeanUtils.copyProperties(stxTask, task);
            } else {
                String newTaskId = GeneratorUtil.genCode();
                oldTaskIdMap.put(task.getCode(), newTaskId);
                task.setId(newTaskId);
                task.setVersion(1);
                //2024-12-23 入库时检查，名字如果重复，就加后缀，如果不重复，就不加后缀
                task.setName(checkName(task.getName(), task.getCatalogId(), "task", projectId));
                task.setProcessDefinitionCode(newProcessId);
                if (null != task.getIsImport()) {
                    if (task.getIsImport()) {
                        //说明是外部使用，需要获取目录id
                        Tuple2<String, String> catalogTuple = catalogService.initDefaultCatalogId(productId, projectId, task.getCatalogNames());
                        task.setCatalogId(catalogTuple.getV1());
                        task.setCatalogParentIds(catalogTuple.getV2());
                    } else {
                        Tuple2<String, String> catalogTuple = catalogService.initCustomCatalogId(productId, projectId, task.getCatalogNames());
                        task.setCatalogId(catalogTuple.getV1());
                        task.setCatalogParentIds(catalogTuple.getV2());
                    }
                }
                task.setCreateBy(user.getId());
                task.setCreateByName(user.getNickname());
                task.setCreateTime(date);
                task.setTenantCode(tenantCode);
                getService(Task.class).add(task);
            }
        }
        //将工作流的locations里的旧任务id替换为新的
        String locations = processDefinition.getLocations();
        Set<String> keySet = oldTaskIdMap.keySet();
        for (String key : keySet) {
            String target = "\"taskCode\":\"" + key + "\"";
            String replacement = "\"taskCode\":\"" + oldTaskIdMap.get(key) + "\"";
            locations = locations.replace(target, replacement);
        }
        processDefinition.setLocations(locations);
        if (null != processDefinition.getIsImport()) {
            if (processDefinition.getIsImport()) {
                //说明是外部使用，需要获取目录id
                Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initDefaultCatalogId(productId, projectId, processDefinition.getCatalogNames());
                processDefinition.setCatalogId(catalogTuple.getV1());
                processDefinition.setCatalogParentIds(catalogTuple.getV2());
            } else {
                Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initCustomCatalogId(productId, projectId, processDefinition.getCatalogNames());
                processDefinition.setCatalogId(catalogTuple.getV1());
                processDefinition.setCatalogParentIds(catalogTuple.getV2());
            }
        }
        this.add(processDefinition);
        //导入工作流与任务的关联关系对象
        //保存新的任务id
        List<String> taskIds = new ArrayList<>();
        List<ProcessTaskRelation> processTaskRelationList = processExportDTO.getProcessTaskRelationList();
        for (ProcessTaskRelation processTaskRelation : processTaskRelationList) {
            processTaskRelation.setId(null);
            processTaskRelation.setProcessDefinitionCode(newProcessId);
            processTaskRelation.setProcessDefinitionVersion(1);
            String newPreTaskCode = oldTaskIdMap.get(processTaskRelation.getPreTaskCode()) == null ? "0" : oldTaskIdMap.get(processTaskRelation.getPreTaskCode());
            processTaskRelation.setPreTaskCode(newPreTaskCode);
            processTaskRelation.setPreTaskVersion(1);
            processTaskRelation.setPostTaskCode(oldTaskIdMap.get(processTaskRelation.getPostTaskCode()));
            processTaskRelation.setPostTaskVersion(1);
            processTaskRelation.setProjectId(projectId);
            processTaskRelation.setCreateBy(user.getId());
            processTaskRelation.setCreateByName(user.getNickname());
            processTaskRelation.setCreateTime(date);
            processTaskRelation.setTenantCode(tenantCode);
            getService(ProcessTaskRelation.class).add(processTaskRelation);
            taskIds.add(processTaskRelation.getPostTaskCode());
        }
        //工作流对象赋值taskList,在initBody方法中使用
        processDefinition.setTaskList(taskList);
        //组装body
        ProcessDefinitionDTO body = initBody(processDefinition, "0");
        //调海豚的新增工作流接口
        Map<String, Object> headers = Utils.getHeader();
        Response response = createProcessDefinition(body);
        if (response.getCode() != 0) {
            throw new AppErrorException("工作流导入失败！请联系管理员，报错信息：" + response.getMessage());
        }
        //导入定时信息对象
        Scheduler schedule = processExportDTO.getSchedule();
        if (null != schedule) {
            //上线工作流，因为工作流是上线状态时才能添加定时
            Release online = new Release();
            online.setProjectCode(processDefinition.getProjectId());
            online.setReleaseState(Release.ReleaseState.ONLINE);
            String onlinePreUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", online.getProjectCode());
            String onlineUri = Constants.PROCESS_RELEASE.replace("{code}", newProcessId) + "?releaseState=" + online.getReleaseState();
            Map onlineResultMap = httpRequestFeignService.post4url(onlinePreUrl + onlineUri, null, headers, Map.class);
            if (Integer.parseInt(onlineResultMap.get("code").toString()) != 0) {
                throw new AppErrorException("上线工作流失败！请联系管理员，报错信息：" + onlineResultMap.get("msg"));
            }
            schedule.setId(null);
            schedule.setProcessDefinitionCode(newProcessId);
            schedule.setProjectId(projectId);
            schedule.setCreateBy(user.getId());
            schedule.setCreateByName(user.getNickname());
            schedule.setCreateTime(date);
            schedule.setTenantCode(tenantCode);
            schedule.setExecutionFrequency(schedule.getCrontab());
            //海豚定时状态和scc不一致
            schedule.setReleaseState(StringUtils.equals(schedule.getReleaseState(), Release.ReleaseState.ONLINE) ? Scheduler.ReleaseState.online : Scheduler.ReleaseState.notOnline);
            getService(Scheduler.class).add(schedule);
            //将定时信息添加到海豚
            String dbid = getService(Scheduler.class).getQuery().eq("id", schedule.getId()).fixeds("dbid").oneValue("dbid", String.class);
            SchedulerParam schedulerBody = new SchedulerParam(schedule, dbid);
            Response schedulerUrlResponse = schedulerService.createSchedule(schedulerBody);
            if (schedulerUrlResponse.getCode() != 0) {
                throw new AppErrorException("导入定时信息失败！请联系管理员，报错信息：" + schedulerUrlResponse.getMessage());
            }
            //下线工作流，添加完定时后需要把工作流下线
            Release offline = new Release();
            offline.setProjectCode(processDefinition.getProjectId());
            offline.setReleaseState(Release.ReleaseState.OFFLINE);

            String offlinePreUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", offline.getProjectCode());
            String offlineUri = Constants.PROCESS_RELEASE.replace("{code}", newProcessId) + "?releaseState=" + offline.getReleaseState();
            Map offlineResultMap = httpRequestFeignService.post4url(offlinePreUrl + offlineUri, null, headers, Map.class);
            if (Integer.parseInt(offlineResultMap.get("code").toString()) != 0) {
                throw new AppErrorException("下线工作流失败！请联系管理员，报错信息：" + offlineResultMap.get("msg"));
            }
        }
    }

    @Transactional
    public void importDataV2(ProcessExportDTO processExportDTO, String projectId) throws UnsupportedEncodingException {
        User user = ThreadLocalUserUtil.getUser(User.class);
        String tenantCode = user.getTenantCode();
        Date date = new Date();
        //导入工作流对象
        ProcessDefinition processDefinition = processExportDTO.getProcessDefinition();
        String productId = processDefinition.getProductId();
        String newProcessId = processDefinition.getId();
        processDefinition.setProductId(productId);
        processDefinition.setProjectId(projectId);
        processDefinition.setVersion(1);
        processDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
        //2024-12-23 入库时检查，名字如果重复，就加后缀，如果不重复，就不加后缀
        //processDefinition.setName(checkNameV2(processDefinition.getName(), null, "process", projectId));
        processDefinition.setCreateBy(user.getId());
        processDefinition.setCreateByName(user.getNickname());
        processDefinition.setCreateTime(date);
        processDefinition.setTenantCode(tenantCode);
        processDefinition.setGlobalParamMap(processDefinition.getGlobalParams());
        //导入任务对象
        List<Task> taskList = processExportDTO.getTaskDefinitionList();
        if (null == taskList || taskList.size() < 1) {
            throw new AppErrorException("数据为空或是无效数据！");
        }
        //保留新旧id的对应关系
        Map<String, String> oldTaskIdMap = new HashMap<>();
        for (Task task : taskList) {
            task.setProductId(null != task.getProductId() ? task.getProductId() : productId);
            task.setProjectId(projectId);
            String newTaskId = task.getCode();
            oldTaskIdMap.put(task.getCode(), newTaskId);
            task.setId(newTaskId);
            task.setVersion(1);
            //2024-12-23 入库时检查，名字如果重复，就加后缀，如果不重复，就不加后缀
            task.setName(checkName(task.getName(), task.getCatalogId(), "task", projectId));
            task.setProcessDefinitionCode(newProcessId);
            if (null != task.getIsImport()) {
                if (task.getIsImport()) {
                    //说明是外部使用，需要获取目录id
                    Tuple2<String, String> catalogTuple = catalogService.initDefaultCatalogId(productId, projectId, task.getCatalogNames());
                    task.setCatalogId(catalogTuple.getV1());
                    task.setCatalogParentIds(catalogTuple.getV2());
                } else {
                    Tuple2<String, String> catalogTuple = catalogService.initCustomCatalogId(productId, projectId, task.getCatalogNames());
                    task.setCatalogId(catalogTuple.getV1());
                    task.setCatalogParentIds(catalogTuple.getV2());
                }
            }
            task.setCreateBy(user.getId());
            task.setCreateByName(user.getNickname());
            task.setCreateTime(date);
            task.setTenantCode(tenantCode);
            getService(Task.class).add(task);
        }
        //将工作流的locations里的旧任务id替换为新的
        String locations = processDefinition.getLocations();
        Set<String> keySet = oldTaskIdMap.keySet();
        for (String key : keySet) {
            String target = "\"taskCode\":\"" + key + "\"";
            String replacement = "\"taskCode\":\"" + oldTaskIdMap.get(key) + "\"";
            locations = locations.replace(target, replacement);
        }
        processDefinition.setLocations(locations);
        if (null != processDefinition.getIsImport()) {
            if (processDefinition.getIsImport()) {
                //说明是外部使用，需要获取目录id
                Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initDefaultCatalogId(productId, projectId, processDefinition.getCatalogNames());
                processDefinition.setCatalogId(catalogTuple.getV1());
                processDefinition.setCatalogParentIds(catalogTuple.getV2());
            } else {
                Tuple2<String, String> catalogTuple = processDefinitionCatalogService.initCustomCatalogId(productId, projectId, processDefinition.getCatalogNames());
                processDefinition.setCatalogId(catalogTuple.getV1());
                processDefinition.setCatalogParentIds(catalogTuple.getV2());
            }
        }
        this.add(processDefinition);
        //导入工作流与任务的关联关系对象
        //保存新的任务id
        List<String> taskIds = new ArrayList<>();
        List<ProcessTaskRelation> processTaskRelationList = processExportDTO.getProcessTaskRelationList();
        for (ProcessTaskRelation processTaskRelation : processTaskRelationList) {
            processTaskRelation.setId(null);
            processTaskRelation.setProcessDefinitionCode(newProcessId);
            processTaskRelation.setProcessDefinitionVersion(1);
            String newPreTaskCode = oldTaskIdMap.get(processTaskRelation.getPreTaskCode()) == null ? "0" : oldTaskIdMap.get(processTaskRelation.getPreTaskCode());
            processTaskRelation.setPreTaskCode(newPreTaskCode);
            processTaskRelation.setPreTaskVersion(1);
            processTaskRelation.setPostTaskCode(oldTaskIdMap.get(processTaskRelation.getPostTaskCode()));
            processTaskRelation.setPostTaskVersion(1);
            processTaskRelation.setProjectId(projectId);
            processTaskRelation.setCreateBy(user.getId());
            processTaskRelation.setCreateByName(user.getNickname());
            processTaskRelation.setCreateTime(date);
            processTaskRelation.setTenantCode(tenantCode);
            getService(ProcessTaskRelation.class).add(processTaskRelation);
            taskIds.add(processTaskRelation.getPostTaskCode());
        }
        //工作流对象赋值taskList,在initBody方法中使用
        processDefinition.setTaskList(taskList);
        //组装body
        ProcessDefinitionDTO body = initBody(processDefinition, "0");
        //调海豚的新增工作流接口
        Map<String, Object> headers = Utils.getHeader();
        Response response = createProcessDefinition(body);
        if (response.getCode() != 0) {
            throw new AppErrorException("工作流导入失败！请联系管理员，报错信息：" + response.getMessage());
        }
        //导入定时信息对象
        Scheduler schedule = processExportDTO.getSchedule();
        if (null != schedule) {
            //上线工作流，因为工作流是上线状态时才能添加定时
            Release online = new Release();
            online.setProjectCode(processDefinition.getProjectId());
            online.setReleaseState(Release.ReleaseState.ONLINE);
            String onlinePreUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", online.getProjectCode());
            String onlineUri = Constants.PROCESS_RELEASE.replace("{code}", newProcessId) + "?releaseState=" + online.getReleaseState();
            Map onlineResultMap = httpRequestFeignService.post4url(onlinePreUrl + onlineUri, null, headers, Map.class);
            if (Integer.parseInt(onlineResultMap.get("code").toString()) != 0) {
                throw new AppErrorException("上线工作流失败！请联系管理员，报错信息：" + onlineResultMap.get("msg"));
            }
            schedule.setId(null);
            schedule.setProcessDefinitionCode(newProcessId);
            schedule.setProjectId(projectId);
            schedule.setCreateBy(user.getId());
            schedule.setCreateByName(user.getNickname());
            schedule.setCreateTime(date);
            schedule.setTenantCode(tenantCode);
            getService(Scheduler.class).add(schedule);
            //将定时信息添加到海豚
            String dbid = getService(Scheduler.class).getQuery().eq("id", schedule.getId()).fixeds("dbid").oneValue("dbid", String.class);
            SchedulerParam schedulerBody = new SchedulerParam(schedule, dbid);
            Response schedulerUrlResponse = schedulerService.createSchedule(schedulerBody);
            if (schedulerUrlResponse.getCode() != 0) {
                throw new AppErrorException("导入定时信息失败！请联系管理员，报错信息：" + schedulerUrlResponse.getMessage());
            }
            //下线工作流，添加完定时后需要把工作流下线
            Release offline = new Release();
            offline.setProjectCode(processDefinition.getProjectId());
            offline.setReleaseState(Release.ReleaseState.OFFLINE);

            String offlinePreUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", offline.getProjectCode());
            String offlineUri = Constants.PROCESS_RELEASE.replace("{code}", newProcessId) + "?releaseState=" + offline.getReleaseState();
            Map offlineResultMap = httpRequestFeignService.post4url(offlinePreUrl + offlineUri, null, headers, Map.class);
            if (Integer.parseInt(offlineResultMap.get("code").toString()) != 0) {
                throw new AppErrorException("下线工作流失败！请联系管理员，报错信息：" + offlineResultMap.get("msg"));
            }
        }
    }

    private Task updateSTXTask(Map<String, String> oldTaskIdMap, Task importTask, String newProcessId) {
        Tuple2<String, String> catalogId = catalogService.initDefaultCatalogId(importTask.getProductId(), importTask.getProjectId(), importTask.getCatalogNames());
        String name = importTask.getName();
        //确认是否拼接目录名
        String subName = importTask.getName();
        if (subName.contains("__[") && subName.contains("_copy")) {
            //如果是复制的工作流，要截取中间的一节
            subName = Utils.removeBetween(subName, "__", "_copy");
        } else if (subName.contains("__[")) {
            subName = name.substring(0, name.lastIndexOf("__["));
        }
        AndOrConditionGroup andOrConditionGroup = new AndOrConditionGroup();
        andOrConditionGroup.addCondition(new EqCondition("name", name));
        andOrConditionGroup.addCondition(new EqCondition("name", subName));
        andOrConditionGroup.addCondition(new StartsWithCondition("name", subName + "__["));
        Task task = taskService.getQuery().eq("catalogId", catalogId.getV1()).andConditionGroup(andOrConditionGroup).withs("processDefinitionName").one();
        if (null != task) {
            if (StringUtils.isNotBlank(task.getProcessDefinitionCode())) {
                throw new RuntimeException("【" + task.getName() + "】任务已经被【" + task.getProcessDefinitionName() + "】工作流引用");
            } else {
                String id = task.getId();
                oldTaskIdMap.put(importTask.getCode(), id);
                task.setId(id);
                //2024-12-23 入库时检查，名字如果重复，就加后缀，如果不重复，就不加后缀
                task.setName(importTask.getName());
                task.setProcessDefinitionCode(newProcessId);
                task.setIsImport(true);
                task.setTaskParams(fixTaskParams(importTask.getTaskParams(), importTask.getDatasourceInfos(), importTask.getTableInfos()));
            }
        } else {
            throw new RuntimeException("先导入集成任务" + "【" + importTask.getName() + "】");
        }
        return task;
    }

    private JSONObject fixTaskParams(JSONObject taskParams, Map<String, String> datasourceInfos, Map<String, Map<String, String>> tableInfos) {
        String str = taskParams.getJSONObject("rawScript").toJSONString();
        Map<String, Object> headers = new HashMap<>();
        headers.put("token", ThreadLocalUserUtil.getCurrentUserToken());
        Set<Map.Entry<String, String>> entries = datasourceInfos.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            String url = ApplicationContextHelp.getAppGatewayHttp() + "/api/database/datasource/info/datasourceInfo/dataName/" + entry.getValue();
            Map datasourceMap = httpRequestFeignService.get4url(url, headers, Map.class, false);
            if (null != datasourceMap.get("code") && 200 == Integer.valueOf(datasourceMap.get("code") + "")) {
                JSONObject jsonObject = JSONObject.parseObject(datasourceMap.get("data") + "");
                String newDatasourceInfoId = jsonObject.getString("datasourceInfoId");
                str = str.replaceAll(entry.getKey(), newDatasourceInfoId);
            }
            Map<String, String> tableInfo = tableInfos.get(entry.getKey());
            if (null != tableInfo) {
                String tableId = tableInfo.get("tableId");
                String schemaName = tableInfo.get("schemaName");
                String tableName = tableInfo.get("tableName");
                String urlTable = ApplicationContextHelp.getAppGatewayHttp() + "/api/database/table/getMetatableId?datasourceId=" + entry.getKey() + "&tableName=" + tableName + "&schema" + schemaName;
                Map tableMap = httpRequestFeignService.get4url(urlTable, headers, Map.class, false);
                if (null != tableMap.get("code") && 200 == Integer.valueOf(tableMap.get("code") + "")) {
                    String newDatasourceInfoId = tableMap.get("data") + "";
                    str = str.replaceAll(tableId, newDatasourceInfoId);
                }
            } else {
                log.info("表未进行元数据采集，不更新数据源id信息");
            }
        }
        taskParams.put("rawScript", str);
        return taskParams;
    }

    /**
     * 检查名称是否重复，如果重复，用新生成的
     *
     * @param name 任务名称
     * @param type task:任务;process:工作流
     * @return
     */
    private String checkName(String name, String catalogId, String type, String projectId) {
        if ("task".equals(type)) {
            //有的任务名称后会带__[catalogId]
            AndOrConditionGroup andOrConditionGroup = new AndOrConditionGroup();
            andOrConditionGroup.addCondition(new EqCondition("name", name));
            andOrConditionGroup.addCondition(new EqCondition("name", name + "__[" + catalogId + "]"));
            List<Task> taskList = getService(Task.class).getQuery().andConditionGroup(andOrConditionGroup).eq("projectId", projectId).list();
            if (null != taskList && taskList.size() > 0) {
                return Utils.getNewName(name, Constants.IMPORT_SUFFIX);
            }
        }

        if ("process".equals(type)) {
            //有的任务名称后会带__[catalogId]
            AndOrConditionGroup andOrConditionGroup = new AndOrConditionGroup();
            andOrConditionGroup.addCondition(new EqCondition("name", name));
            List<ProcessDefinition> processDefinitionList = this.getQuery().andConditionGroup(andOrConditionGroup).eq("projectId", projectId).list();
            if (null != processDefinitionList && processDefinitionList.size() > 0) {
                return Utils.getNewName(name, Constants.IMPORT_SUFFIX);
            }
        }
        return name;
    }

    private String checkNameV2(String name, String catalogId, String type, String projectId) {
        if ("task".equals(type)) {
            //有的任务名称后会带__[catalogId]
            return Utils.getNewName(name, Constants.IMPORT_SUFFIX);
        }

        if ("process".equals(type)) {
            //有的任务名称后会带__[catalogId]
            return Utils.getNewName(name, Constants.IMPORT_SUFFIX);
        }
        return name;
    }

    /**
     * 将文件转为字符串
     *
     * @param file
     * @return
     */
    private String fileToString(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            return IOUtils.toString(inputStream);
        } catch (IOException e) {
            throw new AppErrorException("file转String失败,失败原因：{}", e.getMessage());
        }
    }

    //赋值任务挂载目录id
    public List<ProcessExportDTO> initTaskCatalog(List<ProcessExportDTO> processExportDTOList) {
        if (null != processExportDTOList && !processExportDTOList.isEmpty()) {
            processExportDTOList.forEach(processExportDTOs -> {
                List<Task> taskDefinitionList = processExportDTOs.getTaskDefinitionList();
                Set<String> taskIds = taskDefinitionList.stream().map(t -> t.getCode()).collect(Collectors.toSet());
                List<Task> taskList = taskService.getQuery().in("id", taskIds).filters("id", "isImport", "projectId", "productId", "catalogParentNames").withs("catalogParentNames").list();
                if (null == taskList || taskList.isEmpty()) {
                    //throw new RuntimeException("导出的工作流不存在！！！");
                    return;
                }
                Map<String, Task> taskMap = taskList.stream().collect(Collectors.toMap(Task::getId, task -> task));
                if (!taskDefinitionList.isEmpty()) {
                    taskDefinitionList.forEach(t -> {
                        Task task = taskMap.get(t.getCode());
                        String catalogParentNames = task.getCatalogParentNames();
                        if (StringUtils.isNotBlank(catalogParentNames)) {
                            List<String> catalogs = new ArrayList(Arrays.asList(catalogParentNames.split(",")));
                            if (catalogs.size() > 1) {
                                //移除默认目录/自定义目录
                                catalogs.remove(0);
                                Boolean isImport = task.getIsImport();
                                if ((null != isImport && isImport) || "STX".equals(task.getTaskType())) {
                                    //如果是外部产品，移除产品目录
                                    catalogs.remove(0);
                                }
                            }
                            t.setCatalogNames(catalogs);
                        }
                        if ("STX".equalsIgnoreCase(t.getTaskType())) {
                            JSONObject json = t.getTaskParams().getJSONObject("rawScript");
                            JSONArray source = json.getJSONArray("source");
                            JSONArray sink = json.getJSONArray("sink");
                            Map<String, String> datasourceInfos = getDatasourceInfos(source, sink);
                            t.setDatasourceInfos(datasourceInfos);
                            Map<String, Map<String, String>> tableInfos = getTableInfos(source);
                            t.setTableInfos(tableInfos);
                            t.setIsImport(true);
                        } else {
                            t.setIsImport(task.getIsImport());
                        }
                        t.setProjectId(task.getProjectId());
                        t.setProductId(task.getProductId());


                        //去掉目录后缀
                        //确认是否拼接目录名
                        String name = t.getName();
                        if (name.contains("__[") && name.contains("_copy")) {
                            //如果是复制的工作流，要截取中间的一节
                            name = Utils.removeBetween(name, "__", "_copy");
                        } else if (name.contains("__[")) {
                            name = name.substring(0, name.lastIndexOf("__["));
                        }
                        t.setName(name);
                    });
                }
            });
        }
        return processExportDTOList;
    }

    private Map<String, Map<String, String>> getTableInfos(JSONArray source) {
        Map<String, Map<String, String>> result = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < source.size(); i++) {
            JSONObject json = source.getJSONObject(i);
            JSONObject jdbcSource = json.getJSONObject("jdbcSource");
            if (null == jdbcSource) {
                continue;
            }
            map.put("tableId", jdbcSource.getString("tableId"));
            String schemaName = jdbcSource.getString("schemaName") == null ? jdbcSource.getString("dbName") : jdbcSource.getString("schemaName");
            map.put("schemaName", schemaName);
            map.put("tableName", jdbcSource.getString("tableName"));
            result.put(json.getString("datasourceInfoId"), map);
        }
        return result;
    }

    private Map<String, String> getDatasourceInfos(JSONArray source, JSONArray sink) {
        Map<String, String> map = new HashMap<>();
        Map<String, Object> headers = new HashMap<>();
        headers.put("token", ThreadLocalUserUtil.getCurrentUserToken());
        for (int i = 0; i < source.size(); i++) {
            JSONObject json = source.getJSONObject(i);
            String datasourceInfoId = json.getString("datasourceInfoId");
            String type = json.getString("type");
            if ("LOCALFILE".equalsIgnoreCase(type) || "MergeLocalFile".equalsIgnoreCase(type) || "LOCALFILE2FILE".equalsIgnoreCase(type) || "HTTP".equalsIgnoreCase(type)) {
                continue;
            }
            if (StringUtils.isEmpty(datasourceInfoId)) {
                throw new AppErrorException("导出失败，datasourceInfoId为null，任务类型={}", type);
            }
            String url = ApplicationContextHelp.getAppGatewayHttp() + "/api/database/datasource/info/datasourceInfo/" + datasourceInfoId;
            Map datasourceMap = httpRequestFeignService.get4url(url, headers, Map.class, false);
            if (null != datasourceMap.get("code") && 200 == Integer.valueOf(datasourceMap.get("code") + "")) {
                String data = datasourceMap.get("data") + "";
                if ("null".equalsIgnoreCase(data)) {
                    throw new AppErrorException("导出失败，未查询到数据源，datasourceInfoId=" + datasourceInfoId + "，任务类型=" + type);
                }
                JSONObject jsonObject = JSONObject.parseObject(data);
                map.put(json.getString("datasourceInfoId"), jsonObject.get("dataName") + "");
            }
        }

        for (int i = 0; i < sink.size(); i++) {
            JSONObject json = sink.getJSONObject(i);
            String datasourceInfoId = json.getString("datasourceInfoId");
            String type = json.getString("type");
            if ("LOCALFILE".equalsIgnoreCase(type) || "MergeLocalFile".equalsIgnoreCase(type) || "LOCALFILE2FILE".equalsIgnoreCase(type) || "HTTP".equalsIgnoreCase(type)) {
                continue;
            }
            if (StringUtils.isEmpty(datasourceInfoId)) {
                throw new AppErrorException("导出失败，datasourceInfoId为null，任务类型={}", type);
            }
            String url = ApplicationContextHelp.getAppGatewayHttp() + "/api/database/datasource/info/datasourceInfo/" + datasourceInfoId;
            Map datasourceMap = httpRequestFeignService.get4url(url, headers, Map.class, false);
            if (null != datasourceMap.get("code") && 200 == Integer.valueOf(datasourceMap.get("code") + "")) {
                String data = datasourceMap.get("data") + "";
                if ("null".equalsIgnoreCase(data)) {
                    throw new AppErrorException("导出失败，未查询到数据源，datasourceInfoId=" + datasourceInfoId + "，任务类型=" + type);
                }
                JSONObject jsonObject = JSONObject.parseObject(data);
                map.put(json.getString("datasourceInfoId"), jsonObject.get("dataName") + "");
            }
        }
        return map;
    }

    @Transactional
    public ProcessDefinition onlineExternal(ProcessDefinition newProcessDefinition, Scheduler scheduleParam) {
        List<Task> taskList = newProcessDefinition.getTaskList();
        if (null != taskList && !taskList.isEmpty()) {
            taskList.forEach(t -> {
                if (t.getTaskType().equals("SQL")) {
                    JSONObject taskParams = t.getTaskParams();
                    taskService.initTaskParams(taskParams);
                }
            });
        }
        //添加工作流
        ProcessDefinition processDefinition = this.add(newProcessDefinition.getId(), newProcessDefinition);

        if (ProcessDefinition.ReleaseState.online.equals(processDefinition.getReleaseState())) {
            String processDefinitionId = processDefinition.getId();
            String projectId = processDefinition.getProjectId();
            //新增完工作流如果是上线状态 上线工作流
            processDefinition.setReleaseState(ProcessDefinition.ReleaseState.online);
            this.update(processDefinitionId, processDefinition, false);

            Release release = new Release();
            release.setProjectCode(projectId);
            release.setReleaseState(Release.ReleaseState.ONLINE);
            String errorExplain = "上线";

            Map<String, Object> headers = Utils.getHeader();
            String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", release.getProjectCode());
            String uri = Constants.PROCESS_RELEASE.replace("{code}", processDefinitionId) + "?releaseState=" + release.getReleaseState();
            Map resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
            if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
                throw new AppErrorException("工作流" + errorExplain + "失败！请联系管理员，报错信息：" + resultMap.get("msg"));
            }
            //添加定时策略
            if (null != scheduleParam) {
                Date startTime = scheduleParam.getStartTime();
                Date endTime = scheduleParam.getEndTime();
                if (null == startTime) {
                    schedulerService.initStartTime(scheduleParam);
                }
                if (null == endTime) {
                    schedulerService.initEndTime(scheduleParam);
                }
                scheduleParam.setProcessDefinitionCode(processDefinitionId);
                scheduleParam.setProjectId(projectId);
                schedulerService.add(GeneratorUtil.genCode(), scheduleParam);
            }
            //上线工作流定义，任务添加到检查列表
            checkTaskService.addByProcessDefinitionId(projectId, processDefinitionId, processDefinition.getTenantCode());
            log.info("到点未运行检查任务存储结束");
        }

        return processDefinition;
    }

    @Transactional
    public ProcessDefinition periodicRelease(TestTask testTask, ProcessDefinition newProcessDefinition, Scheduler scheduleParam) throws InterruptedException {
        ProcessDefinition processDefinition = null;
        //工作流id
        String processDefinitionId = newProcessDefinition.getId();
        if (null != processDefinitionId) {
            processDefinition = getService(ProcessDefinition.class).getById(newProcessDefinition.getId());
        } else {
            //如果没传工作流id，自动生成
            newProcessDefinition.setId(GeneratorUtil.genCode());
        }
        //判断工作流是否存在
        if (null != processDefinition) {
            newProcessDefinition.setTenantCode(processDefinition.getTenantCode());
            return this.updatePeriodicRelease(processDefinition.getReleaseState(), newProcessDefinition, scheduleParam);
        }

        return this.onlineExternal(newProcessDefinition, scheduleParam);
    }

    @Transactional
    public ProcessDefinition updatePeriodicRelease(String releaseState, ProcessDefinition newProcessDefinition, Scheduler scheduleParam) throws InterruptedException {
        //工作流id
        String processDefinitionId = newProcessDefinition.getId();
        //项目id
        String projectId = newProcessDefinition.getProjectId();
        //工作流存在 判断是否下线
        if (ProcessDefinition.ReleaseState.online.equals(releaseState)) {
            //未下线 检查是否可下线
            Map checkResult = checkDelete(processDefinitionId, projectId);
            if (Integer.parseInt(String.valueOf(checkResult.get("code"))) != 0) {
                //不可下线 先停止任务
                Map forceStopResult = forceStopProcessInstance(processDefinitionId, projectId);
                if (Integer.parseInt(String.valueOf(forceStopResult.get("code"))) != 0) {
                    throw new AppErrorException("工作流实例强制停止错误，报错信息：" + forceStopResult.get("msg"));
                }
                Thread.sleep(5 * 1000);
            }
            //下线工作流
            this.release(processDefinitionId);
        }
        //更新工作流
        newProcessDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
        this.update(processDefinitionId, newProcessDefinition);

        if (ProcessDefinition.ReleaseState.online.equals(newProcessDefinition.getReleaseState())) {
            //任务上线
            this.release(processDefinitionId);
            //更新定时策略
            if (null != scheduleParam) {
                Scheduler schedule = schedulerService.getQuery().eq("processDefinitionCode", processDefinitionId).fixeds("dbid").one();
                scheduleParam.setProcessDefinitionCode(processDefinitionId);
                Date startTime = scheduleParam.getStartTime();
                Date endTime = scheduleParam.getEndTime();
                if (null == startTime) {
                    schedulerService.initStartTime(scheduleParam);
                }
                if (null == endTime) {
                    schedulerService.initEndTime(scheduleParam);
                }
                scheduleParam.setProjectId(projectId);
                scheduleParam.setDbid(schedule.getDbid());
                schedulerService.add(schedule.getId(), scheduleParam);
            }
        }
        return newProcessDefinition;
    }

    @Transactional
    public Integer offlineExternal(String id, Boolean isForceStop) throws InterruptedException {
        ProcessDefinition processDefinition = this.getById(id);
        if (null == processDefinition) {
            return 1;
        }
        String projectId = processDefinition.getProjectId();

        //检查是否可下线
        Map checkResult = checkDelete(id, projectId);
        if (Integer.parseInt(String.valueOf(checkResult.get("code"))) != 0) {
            if (isForceStop) {
                //强制停止
                Map forceStopResult = forceStopProcessInstance(id, projectId);
                if (Integer.parseInt(String.valueOf(forceStopResult.get("code"))) != 0) {
                    throw new AppErrorException("工作流实例强制停止错误，报错信息：" + forceStopResult.get("msg"));
                }
                Thread.sleep(5 * 1000);
            } else {
                throw new AppErrorException("工作流不可下线!请联系管理员，报错信息：" + checkResult.get("msg"));
            }
        }

        //下线工作流
        processDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
        this.update(id, processDefinition, false);
//        // 工作流下线时 定时任务也要下线
//        Scheduler schedule = schedulerService.getQuery().eq("processDefinitionCode", id).one();
//        if (null != schedule && schedule.getReleaseState().equals(Scheduler.ReleaseState.online)) {
//            schedulerService.offline(schedule);
//        }

        Release release = new Release();
        release.setProjectCode(projectId);
        release.setReleaseState(Release.ReleaseState.OFFLINE);

        Map<String, Object> headers = Utils.getHeader();
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", release.getProjectCode());
        String uri = Constants.PROCESS_RELEASE.replace("{code}", id) + "?releaseState=" + release.getReleaseState();
        Map resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
            throw new AppErrorException("工作流下线失败！请联系管理员，报错信息：" + resultMap.get("msg"));
        }

        //2024-08-21 先删除任务，如果有关联任务影响，会删除失败任务，所以先删除工作流
        processDefinition.setOriginProcess(false);
        processDefinition.setIsImport(true);

        //删除工作流
        Integer delete = this.delete(id, processDefinition);

        //删除任务
        List<String> taskIdList = taskService.getQuery().eq("processDefinitionCode", id).listValue("id", String.class);
        if (null != taskIdList && taskIdList.size() > 0) {
            taskIdList.forEach(taskId -> taskService.delete(taskId, new Task()));
        }

        //删除任务检查表
        Integer delCheckTaskCount = checkTaskService.deleteBy(new EqCondition("processDefinitionId", id));
        log.info("下线工作流任务删除到点检查任务表数量 = {}", delCheckTaskCount);

        return delete;
    }

    /**
     * 检查工作流是否可删除
     *
     * @param id
     * @param projectId
     */
    public Map checkDelete(String id, String projectId) {
        String url = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectId) + "/" + id + "/check";
        Map<String, Object> header = Utils.getHeader();
        return httpRequestFeignService.get4url(url, header, Map.class);
    }

    /**
     * 通过工作流code强制停止所以引用中的工作流实例
     *
     * @param id
     * @param projectId
     * @return
     */
    public Map forceStopProcessInstance(String id, String projectId) {
        String url = dolphinscheduler + Constants.PROCESS_INSTANCES.replace("{projectCode}", projectId) + "/" + id + "/forceStop";
        Map<String, Object> header = Utils.getHeader();
        return httpRequestFeignService.get4url(url, header, Map.class);
    }

    //停止工作流的实例
    public Map stopProcess(String id) {
        ProcessDefinition processDefinition = this.getQuery().eq("id", id).one();
        return forceStopProcessInstance(processDefinition.getId(), processDefinition.getProjectId());
    }

    //下线工作流
    @Transactional
    public void offlineProcessDefinition(ProcessDefinition processDefinition) {
        String id = processDefinition.getId();
        String projectId = processDefinition.getProjectId();
        String tenantCode = processDefinition.getTenantCode();

        Release release = new Release();
        release.setProjectCode(projectId);
        release.setReleaseState(Release.ReleaseState.OFFLINE);

        // 工作流下线时 定时任务也要下线
        Scheduler schedule = schedulerService.getQuery().eq("processDefinitionCode", id).one();
        if (null != schedule && schedule.getReleaseState().equals(Scheduler.ReleaseState.online)) {
            schedulerService.offline(schedule);
        }

        Map<String, Object> headers = Utils.getHeader();
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", projectId);
        String uri = Constants.PROCESS_RELEASE.replace("{code}", id) + "?releaseState=" + release.getReleaseState();
        Map resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
            throw new AppErrorException("工作流下线失败！请联系管理员，报错信息：" + resultMap.get("msg"));
        }
        processDefinition.setReleaseState(ProcessDefinition.ReleaseState.notOnline);
        this.update(id, processDefinition, false);

        //删除任务检查表
        Integer delCheckTaskCount = checkTaskService.deleteBy(new EqCondition("processDefinitionId", id));
        log.info("下线工作流任务删除到点检查任务表数量 = {}", delCheckTaskCount);

        if (processDefinition.getOriginProcess()) {
            IntegrationMessageDTO messageDTO = new IntegrationMessageDTO();
            messageDTO.setProjectId(projectId);
            messageDTO.setTenantCode(tenantCode);
            messageDTO.setProcessDefinitionName(processDefinition.getName());
            Map<String, List<String>> map = new HashMap<>();
            map.put("update", Arrays.asList(processDefinition.getTaskIds().split(",")));
            messageDTO.setTaskIds(map);
            messageDTO.setReleaseState("下线");
            //di服务获取信息只需要更新任务的状态，
//            messageDTO.setProcessDefinitionId(id);
            try {
                kafkaTemplate.send(TaskUtils.TASK_BINDING_INFO, UUID.randomUUID().toString(), JSON.toJSONString(messageDTO));
            } catch (Exception e) {
                log.error("删除工作流发送kafka失败，发送消息内容是 {}");
            }
        }
    }

    //上线工作流
    @Transactional
    public void onlineProcessDefinition(ProcessDefinition processDefinition) {
        Release release = new Release();
        String projectId = processDefinition.getProjectId();
        String tenantCode = processDefinition.getTenantCode();

        release.setProjectCode(projectId);
        release.setReleaseState(Release.ReleaseState.ONLINE);

        String id = processDefinition.getId();
        Map<String, Object> headers = Utils.getHeader();
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", release.getProjectCode());
        String uri = Constants.PROCESS_RELEASE.replace("{code}", id) + "?releaseState=" + release.getReleaseState();
        Map resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
        if (Integer.parseInt(resultMap.get("code").toString()) != 0) {
            throw new AppErrorException("工作流上线失败！请联系管理员，报错信息：" + resultMap.get("msg"));
        }
        processDefinition.setReleaseState(ProcessDefinition.ReleaseState.online);
        this.update(id, processDefinition, false);

        //上线工作流定义，任务添加到检查列表
        checkTaskService.addByProcessDefinitionId(projectId, id, tenantCode);
        log.info("到点未运行检查任务存储结束");

        if (processDefinition.getOriginProcess()) {
            IntegrationMessageDTO messageDTO = new IntegrationMessageDTO();
            messageDTO.setProjectId(projectId);
            messageDTO.setTenantCode(tenantCode);
            messageDTO.setProcessDefinitionName(processDefinition.getName());
            Map<String, List<String>> map = new HashMap<>();
            map.put("update", Arrays.asList(processDefinition.getTaskIds().split(",")));
            messageDTO.setTaskIds(map);
            messageDTO.setReleaseState("上线");
            //di服务获取信息只需要更新任务的状态，
//            messageDTO.setProcessDefinitionId(id);
            try {
                kafkaTemplate.send(TaskUtils.TASK_BINDING_INFO, UUID.randomUUID().toString(), JSON.toJSONString(messageDTO));
            } catch (Exception e) {
                log.error("删除工作流发送kafka失败，发送消息内容是 {}");
            }
        }

    }

    public Map<String, ProcessInstance> getProcessInstanceMap(Set<String> processDefinitionIds) {
        Map<String, ProcessInstance> result = new HashMap<>();
        String sql = "SELECT" +
                " t1.process_definition_code AS process_definition_id," +
                " any_value ( t1.state ) AS state," +
                " any_value ( t1.start_time ) AS start_time" +
                " FROM" +
                " business_ds.t_ds_process_instance t1" +
                " INNER JOIN ( SELECT process_definition_code, MAX( start_time ) AS mx_start_time FROM business_ds.t_ds_process_instance WHERE process_definition_code IN ( '%s' ) GROUP BY process_definition_code ) t2 " +
                " ON t1.process_definition_code = t2.process_definition_code" +
                " AND t1.start_time = t2.mx_start_time" +
                " WHERE t1.process_definition_code IN ( '%s' ) " +
                " GROUP BY" +
                " t1.process_definition_code";
        String processDefinitionCodes = StringUtils.join(processDefinitionIds, "','");
        List<ProcessInstance> processInstanceList = getSqlExecutor().excuteSelect(String.format(sql, processDefinitionCodes, processDefinitionCodes)).list(ProcessInstance.class);
        if (null != processInstanceList && processInstanceList.size() > 0) {
            processInstanceList.forEach(ProcessInstance::afterDbInit);
            result = processInstanceList.stream().collect(Collectors.toMap(ProcessInstance::getProcessDefinitionId, t -> t));
        }
        return result;
    }

    @Override
    public List<ProcessDefinition> getList(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        //赋值最新执行状态
        String[] withs = queryFilter.getWiths();
        boolean hasExecState = false;
        boolean hasStartTime = false;
        if (null != withs) {
            //with join处理效率慢 execState,startTime
            HashSet<String> hashSet = new HashSet(Arrays.asList(withs));
            if (hashSet.contains("exec_state")) {
                hashSet.remove("exec_state");
                hasExecState = true;
            }
            if (hashSet.contains("start_time")) {
                hashSet.remove("start_time");
                hasStartTime = true;
            }
            queryFilter.setWiths(hashSet.toArray(new String[]{}));
        }
        List<ProcessDefinition> list = this.getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter).page(page, pager).list();
        if (null != list && !list.isEmpty()) {
            if (hasExecState || hasStartTime) {
                setExecState(list, hasExecState, hasStartTime);
            }

        }
        return list;
    }

    private void setExecState(List<ProcessDefinition> list, boolean hasExecState, boolean hasStartTime) {
        Set<String> processDefinitionIds = list.stream().map(ProcessDefinition::getId).collect(Collectors.toSet());
        Map<String, ProcessInstance> processInstanceMap = getProcessInstanceMap(processDefinitionIds);
        list.forEach(t -> {
            ProcessInstance processInstance = processInstanceMap.get(t.getId());
            if (null != processInstance) {
                if (hasExecState) {
                    t.setExecState(processInstance.getState());
                }
                if (hasStartTime) {
                    t.setStartTime(processInstance.getStartTime());
                }
            }
        });
    }

    public JSONArray getProcessCatalogTree(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter,
                                           Integer page,
                                           Integer pager,
                                           String projectId,
                                           Integer type,
                                           String catalogProductId,
                                           String level,
                                           String sortby) {
        //获取目录列表
        IQueryWrapper<ProcessDefinitionCatalog> processDefinitionCatalogIQueryWrapper = getService(ProcessDefinitionCatalog.class).getQuery().eq("projectId", projectId);
        //获取任务列表
        if (StringUtils.isNotBlank(catalogProductId)) {
            conditionGroupList.get(0).addCondition(new EqCondition("productId", catalogProductId));
            processDefinitionCatalogIQueryWrapper.ge("level", level);
            processDefinitionCatalogIQueryWrapper.eq("productId", catalogProductId);
        }
        List<ProcessDefinition> processDefinitionList = this.getList(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        if (null != type) {
            processDefinitionCatalogIQueryWrapper.eq("type", type);
        }
        //根据name排序
        String sortbyField = sortby;
        String sortbyType = "asc";
        if (StringUtils.isNotBlank(sortby) && sortby.contains("_")) {
            String[] split = sortbyField.split("_");
            sortbyField = split[0];
            sortbyType = split[1];
        }
        if (StringUtils.isNotBlank(sortby) && "name".equals(sortbyField)) {
            if ("asc".equals(sortbyType)) {
                processDefinitionCatalogIQueryWrapper.sortbyAsc("name");
            } else if ("desc".equals(sortbyType)) {
                processDefinitionCatalogIQueryWrapper.sortbyDesc("name");
            }
        }
        List<ProcessDefinitionCatalog> catalogList = processDefinitionCatalogIQueryWrapper.list();
        List<JSONObject> jsonObjectList = new ArrayList<>();
        //构建树
        if (null != catalogList && !catalogList.isEmpty()) {
            if (null != processDefinitionList && !processDefinitionList.isEmpty()) {
                catalogList.forEach(c -> {
                    List<ProcessDefinition> processDefinitions = processDefinitionList.stream().filter(t2 -> null != t2.getCatalogId() && t2.getCatalogId().equals(c.getId())).collect(Collectors.toList());
                    if (!processDefinitions.isEmpty()) {
                        processDefinitions.forEach(process -> {
                            JSONObject jsonObject = JsonUtil.toJSON(process);
                            String name = jsonObject.getString("name");
                            if (name.contains("__[") && name.contains("_copy")) {
                                //如果是复制的工作流，要截取中间的一节
                                jsonObject.put("name", Utils.removeBetween(name, "__", "_copy"));
                            } else if (name.contains("__[")) {
                                jsonObject.put("name", name.substring(0, name.lastIndexOf("__[")));
                            } else {
                                jsonObject.put("name", name);
                            }
                            jsonObject.put("parentIds", process.getCatalogParentIds());
                            jsonObject.put("execState", null != process.getExecState() ? process.getExecState() : WorkflowExecutionStatus.NOTRUNNING.name());
                            jsonObject.put("pid", c.getId());
                            jsonObject.put("level", c.getLevel() + 1);
                            jsonObject.put("isCatalog", false);
                            jsonObjectList.add(jsonObject);
                        });
                    }
                    JSONObject jsonObject = JsonUtil.toJSON(c);
                    jsonObject.put("isCatalog", true);
                    jsonObjectList.add(jsonObject);
                });
            } else {
                catalogList.forEach(c -> {
                    JSONObject jsonObject = JsonUtil.toJSON(c);
                    jsonObject.put("isCatalog", true);
                    jsonObjectList.add(jsonObject);
                });
            }
        } else {
            return new JSONArray();
        }
        TreeNodeUtil treeNodeUtil = new TreeNodeUtil(jsonObjectList);
        return treeNodeUtil.getTree();
    }

    public Response<List<JSONObject>> batchOnlineProcessDefinition(String releaseState, List<String> ids) {
        List<JSONObject> result = new ArrayList<>();

        // 查询工作流定义列表
        List<ProcessDefinition> processDefinitionList = this.getQuery()
                .in("id", ids)
                .filters("id", "name", "releaseState", "projectId", "tenantCode", "taskIds")
                .list();

        if (CollectionUtils.isEmpty(processDefinitionList)) {
            log.error("工作流定义不存在！！！");
            throw new AppErrorException("工作流定义不存在！！！");
        }

        // 根据 releaseState 执行上线或下线操作
        switch (releaseState) {
            case Release.ReleaseState.ONLINE:
                processDefinitionList.forEach(process -> handleProcess(process, result, this::onlineProcessDefinition, ProcessDefinition.ReleaseState.online));
                break;
            case Release.ReleaseState.OFFLINE:
                processDefinitionList.forEach(process -> handleProcess(process, result, this::offlineProcessDefinition, ProcessDefinition.ReleaseState.notOnline));
                break;
            default:
                throw new IllegalArgumentException("非法的 releaseState: " + releaseState);
        }
        return ResponseFactory.makeSuccess(result);
    }

    /**
     * 处理单个工作流定义
     *
     * @param process     工作流定义
     * @param result      结果列表
     * @param operation   操作函数（上线或下线）
     * @param targetState 目标状态
     */
    private void handleProcess(ProcessDefinition process, List<JSONObject> result, Consumer<ProcessDefinition> operation, String targetState) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", process.getId());
        jsonObject.put("name", process.getName());

        if (targetState.equals(process.getReleaseState())) {
            jsonObject.put("status", "fail");
            jsonObject.put("errorMsg", "当前工作流已经是" + targetState + "状态，无需重复" + targetState);
        } else {
            try {
                operation.accept(process); // 执行上线或下线操作
                jsonObject.put("status", "success");
                jsonObject.put("errorMsg", null);
            } catch (Exception e) {
                jsonObject.put("status", "fail");
                jsonObject.put("errorMsg", e.getMessage());
            }
        }

        result.add(jsonObject);
    }

    public List<ProcessExportDTO> initProcessDefinitionCatalog(List<ProcessExportDTO> processExportDTOList) {
        if (null != processExportDTOList && !processExportDTOList.isEmpty()) {
            //获取所有导出工作流信息
            Set<String> processDefinitionIds = processExportDTOList.stream().map(t -> t.getProcessDefinition().getCode()).collect(Collectors.toSet());
            List<ProcessDefinition> processDefinitionList = this.getQuery().in("id", processDefinitionIds).withs("catalogParentNames").filters("id", "isImport", "catalogParentNames", "projectId", "productId").list();
            if (null == processDefinitionList || processDefinitionList.isEmpty()) {
                //throw new RuntimeException("导出的工作流不存在！！！");
                return processExportDTOList;
            }
            Map<String, ProcessDefinition> processDefinitionMap = processDefinitionList.stream().collect(Collectors.toMap(ProcessDefinition::getId, processDefinition -> processDefinition));
            processExportDTOList.forEach(processExportDTOs -> {
                String id = processExportDTOs.getProcessDefinition().getCode();
                ProcessDefinition processDefinition = processDefinitionMap.get(id);
                String catalogParentNames = processDefinition.getCatalogParentNames();
                if (StringUtils.isNotBlank(catalogParentNames)) {
                    List<String> catalogs = new ArrayList(Arrays.asList(catalogParentNames.split(",")));
                    //移除默认目录/自定义目录
                    catalogs.remove(0);
                    Boolean isImport = processDefinition.getIsImport();
                    if (null != isImport && isImport) {
                        //如果是外部产品，移除产品目录
                        catalogs.remove(0);
                    }
                    processExportDTOs.getProcessDefinition().setCatalogNames(catalogs);
                }
                processExportDTOs.getProcessDefinition().setProductId(processDefinition.getProductId());
                processExportDTOs.getProcessDefinition().setProjectId(processDefinition.getProjectId());
                processExportDTOs.getProcessDefinition().setIsImport(processDefinition.getIsImport());
                //一体化导入导出发现这里的id会与定时任务中不匹配，这里的id就是海豚的id，我们要用海豚的code
                processExportDTOs.getProcessDefinition().setId(id);
            });
        }
        return processExportDTOList;
    }

    public void fixProcessDefinitionCatalogId() {
        //把所有非调度中心产品的工作流isImport改为true
        Integer update = this.getSqlExecutor().excuteUpdate("UPDATE `business_scc`.`scc_process_definition` SET `is_import` = 1 where product_id <> '11575107048320'");
        log.info("修改isImport数量={}", update);
        List<ProcessDefinition> processDefinitionList = this.getQuery().ignoreTenantCode().ignorePermissions().isNull("catalogId").list();
        if (null != processDefinitionList && !processDefinitionList.isEmpty()) {

            Map<String, List<ProcessDefinition>> tenantProcessDefinitionList = processDefinitionList.stream()
                    .collect(Collectors.groupingBy(ProcessDefinition::getTenantCode));
            if (null != tenantProcessDefinitionList && !tenantProcessDefinitionList.isEmpty()) {
                for (String tenantCode : tenantProcessDefinitionList.keySet()) {
                    List<ProcessDefinition> processDefinitions = tenantProcessDefinitionList.get(tenantCode);
                    if (null != processDefinitions && !processDefinitions.isEmpty()) {
                        String systemToken = AuthUtil.getSystemToken(null);
                        ThreadLocalUserUtil.setCode("token", systemToken);
                        //加上超管的租户code
                        ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
                        Map<Boolean, List<ProcessDefinition>> groupedByIsImport = processDefinitions.stream()
                                .collect(Collectors.groupingBy(ProcessDefinition::getIsImport));
                        List<ProcessDefinition> externalProcessDefinitionList = groupedByIsImport.get(true);
                        if (null != externalProcessDefinitionList && !externalProcessDefinitionList.isEmpty()) {
                            //外部工作流
                            externalProcessDefinitionList.forEach(t -> {
                                try {
                                    Tuple2<String, String> catalog = processDefinitionCatalogService.initDefaultCatalogId(t.getProductId(), t.getProjectId(), null);
                                    t.setCatalogId(catalog.getV1());
                                    t.setCatalogParentIds(catalog.getV2());
                                    this.update(t);
                                } catch (Exception e) {
                                    log.info("初始化目录失败：" + t.getName());
                                }

                            });
                        }
                        List<ProcessDefinition> selfProcessDefinitionList = groupedByIsImport.get(false);
                        if (null != selfProcessDefinitionList && !selfProcessDefinitionList.isEmpty()) {
                            //调度中心工作流
                            selfProcessDefinitionList.forEach(t -> {
                                try {
                                    Tuple4<String, String, String, String> catalog = processDefinitionCatalogService.initCustomCatalog(t.getProductId(), t.getProjectId());
                                    t.setCatalogId(catalog.getV1());
                                    t.setCatalogParentIds(catalog.getV2());
                                    this.update(t);
                                } catch (Exception e) {
                                    log.info("初始化目录失败：" + t.getName());
                                }
                            });
                        }
                    }
                }
            }
        }
    }

    public void exportXlsx(HttpServletRequest request, HttpServletResponse response, List<ProcessDefinition> processDefinitionList) throws IOException {
        if (CollectionUtils.isEmpty(processDefinitionList)) processDefinitionList = new ArrayList<>();
        List<JSONObject> jsonObjectList = JsonUtil.toBeanList(processDefinitionList, JSONObject.class);
        List<String> processDefinitionIds = processDefinitionList.stream().map(ProcessDefinition::getId).collect(Collectors.toList());
        String language = request.getHeader("accept-language");
        //数据转换
        dataTransition(jsonObjectList, language, processDefinitionIds);
        String fileName = "ProcessDefinition.xlsx";
        String sheetName = "sheet";
        List<ExcelTitle> title = getTitle();
        JoyadataPoiUtil.downloadExcel(request, response, fileName, sheetName, title, jsonObjectList);
    }

    private void dataTransition(List<JSONObject> jsonObjectList, String languageCode, List<String> processDefinitionIds) {
        Map<String, String> processExecTimeMap = getProcessExecTimeMap(true, processDefinitionIds);
        DataConverter dataConverter = new DataConverter(languageCode);
        jsonObjectList.forEach(jsonObject -> {
            String scheduleFrequency = dataConverter.convertCronDescription(jsonObject.getString("scheduleStatus"), jsonObject.getString("crontab"), jsonObject.getInteger("scheduleFrequency"));
            String failureStrategy = dataConverter.convertFailureStrategy(jsonObject.getString("failureStrategy"));
            String executionType = dataConverter.convertExecutionType(jsonObject.getString("executionType"));
            String releaseState = dataConverter.convertReleaseState(jsonObject.getString("releaseState"));
            String processInstancePriority = dataConverter.convertProcessInstancePriority(jsonObject.getString("processInstancePriority"));
            String scheduleStatus = dataConverter.convertScheduleStatus(jsonObject.getString("scheduleStatus"));
            String processDefinitionId = jsonObject.getString("id");
            jsonObject.put("scheduleFrequency", scheduleFrequency);
            jsonObject.put("failureStrategy", failureStrategy);
            jsonObject.put("processInstancePriority", processInstancePriority);
            jsonObject.put("executionType", executionType);
            jsonObject.put("releaseState", releaseState);
            jsonObject.put("scheduleStatus", scheduleStatus);
            //将工作流名称的目录id后缀去掉
            String name = jsonObject.getString("name");
            if (StringUtils.isNotBlank(name)) {
                if (name.contains("__[") && name.contains("_copy")) {
                    //如果是复制的工作流，要截取中间的一节
                    jsonObject.put("name", Utils.removeBetween(name, "__", "_copy"));
                } else if (name.contains("__[")) {
                    jsonObject.put("name", name.substring(0, name.lastIndexOf("__[")));
                }
            }
            //赋值下一次执行时间
            jsonObject.put("cronNextExecTime", processExecTimeMap.get(processDefinitionId));
        });
    }

    private List<ExcelTitle> getTitle() {
        List<ExcelTitle> title = new ArrayList<>();
        ExcelTitle title1 = new ExcelTitle();
        title1.setName("工作流名称");
        title1.setCode("name");
        title1.setPos(1);
        ExcelTitle title2 = new ExcelTitle();
        title2.setName("状态");
        title2.setCode("releaseState");
        title2.setPos(2);
        ExcelTitle title3 = new ExcelTitle();
        title3.setName("定时状态");
        title3.setCode("scheduleStatus");
        title3.setPos(3);
        ExcelTitle title4 = new ExcelTitle();
        title4.setName("执行频率");
        title4.setCode("scheduleFrequency");
        title4.setPos(4);
        ExcelTitle title5 = new ExcelTitle();
        title5.setName("下一次执行时间");
        title5.setCode("cronNextExecTime");
        title5.setPos(5);
        ExcelTitle title6 = new ExcelTitle();
        title6.setName("执行策略");
        title6.setCode("executionType");
        title6.setPos(6);
        ExcelTitle title7 = new ExcelTitle();
        title7.setName("失败策略");
        title7.setCode("failureStrategy");
        title7.setPos(7);
        ExcelTitle title8 = new ExcelTitle();
        title8.setName("流程优先级");
        title8.setCode("processInstancePriority");
        title8.setPos(8);
        ExcelTitle title9 = new ExcelTitle();
        title9.setName("工作组");
        title9.setCode("workerGroup");
        title9.setPos(9);
        ExcelTitle title10 = new ExcelTitle();
        title10.setName("任务数量");
        title10.setCode("taskNum");
        title10.setPos(10);
        title.add(title1);
        title.add(title2);
        title.add(title3);
        title.add(title4);
        title.add(title5);
        title.add(title6);
        title.add(title7);
        title.add(title8);
        title.add(title9);
        title.add(title10);

        return title;
    }

    //获取工作流下次执行时间
    public Map<String, String> getProcessExecTimeMap(boolean hasCronNextExecTime, List<String> processDefinitionIds) {
        Map<String, String> processExecTimeMap = new HashMap<>();
        if (hasCronNextExecTime) {
            String sql = " SELECT " +
                    " process_definition_id, " +
                    " MIN( exec_time ) AS exec_time " +
                    " FROM" +
                    " scc_process_definition_exec_time " +
                    " WHERE" +
                    " exec_time >= now() " +
                    " AND process_definition_id in ('%s')" +
                    " GROUP BY" +
                    " process_definition_id";
            List<ProcessDefinitionExecTime> processDefinitionExecTimeList = processDefinitionExecTimeService.getSqlExecutor().excuteSelect(String.format(sql, StringUtils.join(processDefinitionIds, "','"))).list(ProcessDefinitionExecTime.class);
            if (null != processDefinitionExecTimeList && !CollectionUtils.isEmpty(processDefinitionExecTimeList)) {
                processExecTimeMap = processDefinitionExecTimeList.stream()
                        .collect(Collectors.toMap(
                                ProcessDefinitionExecTime::getProcessDefinitionId,
                                item -> {
                                    LocalDateTime dateTime = item.getExecTime().toInstant()
                                            .atZone(ZoneId.systemDefault())
                                            .toLocalDateTime();
                                    return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                }
                        ));
            }
        }
        return processExecTimeMap;
    }

    /**
     * 批量运行
     *
     * @param param
     * @return
     */
    public Map batchStartProcessInstance(String id, StartParam param, boolean returnInstanceId) throws UnsupportedEncodingException {
        ProcessDefinition processDefinition = this.getQuery().eq("id", id).one();
        initStartParam(processDefinition, param);
        String preUrl = dolphinscheduler + Constants.EXECUTORS_START_PROCESS_INSTANCE_BATCH.replace("{projectCode}", param.getProjectId());
        String scheduleTime = param.getScheduleTime();
        if (null != scheduleTime) {
            scheduleTime = URLEncoder.encode(scheduleTime, "UTF-8");
        }

        String url = preUrl + "?processDefinitionCode=" + param.getProcessDefinitionCode()
                + "&failureStrategy=" + param.getFailureStrategy()
                + "&warningType=" + param.getWarningType()
                + "&warningGroupId=" + param.getWarningGroupId()
                + "&execType=" + param.getExecType()
                + "&startNodeList=" + StringUtils.defaultIfBlank(param.getStartNodeList(), "")
                + "&taskDependType=" + param.getTaskDependType()
                + "&complementDependentMode=" + param.getComplementDependentMode()
                + "&runMode=" + param.getRunMode()
                + "&processInstancePriority=" + param.getProcessInstancePriority()
                + "&workerGroup=" + param.getWorkerGroup()
                + "&environmentCode=" + param.getEnvironmentCode()
                //+ "&startParams=" + param.getStartParamsList()
                + "&expectedParallelismNumber=" + Integer.parseInt(StringUtils.defaultIfBlank(param.getExpectedParallelismNumber(), "0"))
                + "&dryRun=" + param.getDryRun()
                + "&testFlag=" + param.getTestFlag()
                + "&scheduleTime=" + scheduleTime;
        if (returnInstanceId) {
            url = url + "&returnInstanceId=true";
        }
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.post4url(url, param.getStartParamsList(), headers, Map.class);
        return resultMap;
    }

}
