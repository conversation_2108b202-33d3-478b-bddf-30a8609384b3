package com.joyadata.scc.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.scc.enums.CatalogName;
import com.joyadata.scc.model.Catalog;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionCatalog;
import com.joyadata.scc.model.Task;
import com.joyadata.service.BaseService;
import com.joyadata.tms.model.Product;
import com.joyadata.tms.model.Project;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: CatalogService
 * @date 2023/11/3
 */
@Slf4j
@Service
public class CatalogService extends BaseService<Catalog> {

    private JoyaFeignService<Product> productJoyaFeignService = FeignFactory.make(Product.class);
    private JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);

    public String initDefaultCatalog(String productId, String projectId) {
        //初始化默认目录
        Catalog defaultCatalog = this.getQuery().eq("name", CatalogName.DEFAULT.getName()).eq("projectId", projectId).eq("level", 0).one();
        if (null == defaultCatalog) {
            Catalog catalog = new Catalog();
            catalog.setName(CatalogName.DEFAULT.getName());
            catalog.setProductId(productId);
            catalog.setProjectId(projectId);
            catalog.setType(CatalogName.DEFAULT.getType());
            catalog.setIsPublic(true);
            return this.add(catalog).getId();
        }
        return defaultCatalog.getId();
    }

    public Tuple3<String, String, String> initCustomCatalog(String productId, String projectId) {
        //初始化默认目录
        Catalog customCatalog = this.getQuery().eq("name", CatalogName.CUSTOM.getName()).eq("level", 0).eq("projectId", projectId).one();
        if (null == customCatalog) {
            Catalog catalog = new Catalog();
            catalog.setName(CatalogName.CUSTOM.getName());
            catalog.setProductId(productId);
            catalog.setProjectId(projectId);
            catalog.setType(CatalogName.CUSTOM.getType());
            catalog.setIsPublic(true);
            customCatalog = this.add(catalog);
        }
        //创建defult目录
        Catalog orCreateDefaultCatalog = findOrCreateDefaultCatalog(productId, projectId, customCatalog.getId(), 1, CatalogName.CUSTOM.getType());
        return new Tuple3<>(orCreateDefaultCatalog.getId(), orCreateDefaultCatalog.getParentIds(), customCatalog.getId());
    }

    /**
     * 初始化产品目录
     */
    private String initProductCatalog(String productId, String projectId, String defaultCatalogId) {
        if (StringUtils.isBlank(productId)) {
            return StringUtils.EMPTY;
        }
        String productName = productJoyaFeignService.getQuery()
                .eq("id", productId)
                .oneValue("name", String.class);
        if (StringUtils.isBlank(productName)) {
            throw new RuntimeException("产品不存在！");
        }
        return Optional.ofNullable(this.getQuery()
                        .eq("productId", productId)
                        .eq("projectId", projectId)
                        .eq("name", productName)
                        .eq("level", 1)
                        .one())
                .orElseGet(() -> createCatalog(productId, projectId, defaultCatalogId, productName, 1, CatalogName.DEFAULT.getType()))
                .getId();
    }

    /**
     * 初始化目录 ID
     *
     * @param productId    产品 ID
     * @param projectId    项目 ID
     * @param catalogNames 目录名称列表
     * @return Tuple2<目录 ID, 父目录 ID>
     */
    public Tuple2<String, String> initDefaultCatalogId(String productId, String projectId, List<String> catalogNames) {
        // 初始化默认目录
        String defaultCatalogId = initDefaultCatalog(productId, projectId);

        // 初始化产品目录
        String productCatalogId = initProductCatalog(productId, projectId, defaultCatalogId);

        // 初始化目标目录
        if (CollectionUtils.isNotEmpty(catalogNames)) {
            // 放入指定目录
            Catalog catalog = findOrCreateCatalog(productId, projectId, catalogNames, productCatalogId, CatalogName.DEFAULT.getType());
            return new Tuple2<>(catalog.getId(), catalog.getParentIds());
        } else {
            // 放入默认目录
            Catalog defaultCatalog = findOrCreateDefaultCatalog(productId, projectId, productCatalogId, 2, CatalogName.DEFAULT.getType());
            return new Tuple2<>(defaultCatalog.getId(), defaultCatalog.getParentIds());
        }
    }

    /**
     * 初始化自定义目录 ID
     *
     * @param productId    产品 ID
     * @param projectId    项目 ID
     * @param catalogNames 目录名称列表
     * @return Tuple2<目录 ID, 父目录 ID>
     */
    public Tuple2<String, String> initCustomCatalogId(String productId, String projectId, List<String> catalogNames) {
        // 初始化默认目录
        Tuple3<String, String, String> defaultCatalogId = initCustomCatalog(productId, projectId);

        // 初始化目标目录,导出时有可能在default目录
        if (CollectionUtils.isNotEmpty(catalogNames) && !CatalogName.CUSTOM.getName().equals(catalogNames.get(0))) {
            // 放入指定目录
            Catalog catalog = findOrCreateCatalog(productId, projectId, catalogNames, defaultCatalogId.getV3(), CatalogName.CUSTOM.getType());
            return new Tuple2<>(catalog.getId(), catalog.getParentIds());
        } else {
            // 放入默认目录
            Catalog defaultCatalog = findOrCreateDefaultCatalog(productId, projectId, defaultCatalogId.getV1(), 1, CatalogName.CUSTOM.getType());
            return new Tuple2<>(defaultCatalog.getId(), defaultCatalog.getParentIds());
        }
    }

    /**
     * 查找或创建defult目录（产品目录defult在level=2,自定义目录defult在level=1）
     */
    private Catalog findOrCreateDefaultCatalog(String productId, String projectId, String parentId, Integer level, Integer type) {
        IQueryWrapper<Catalog> queryWrapper = this.getQuery()
                .eq("projectId", projectId)
                .eq("name", "default")
                .eq("level", level)
                .eq("type", type);
        //自定义的default目录不需要productId区分，只会有一个，默认目录需要productId区分
        if (CatalogName.DEFAULT.getType().equals(type)) {
            queryWrapper.eq("productId", productId);
        }
        return Optional.ofNullable(
                        queryWrapper.one())
                .orElseGet(() -> createCatalog(productId, projectId, parentId, "default", level, type));
    }

    /**
     * 查找或创建指定目录
     */
    private Catalog findOrCreateCatalog(String productId, String projectId, List<String> catalogNames, String parentId, Integer type) {
        Catalog catalog = new Catalog();
        for (int i = 0; i < catalogNames.size(); i++) {
            String catalogName = catalogNames.get(i);
            //level从坐标0开始，level=2(level=0(默认目录), level=1(产品目录))
            int level = i;
            if (Objects.equals(CatalogName.CUSTOM.getType(), type)) {
                level = i + 1;
            } else if (Objects.equals(CatalogName.DEFAULT.getType(), type)) {
                level = i + 2;
            }
            catalog = this.getQuery().eq("productId", productId).eq("projectId", projectId).eq("pid", parentId).eq("name", catalogName).eq("level", level).one();
            if (null == catalog) {
                //如果产品下没有这个目录，数据库添加一条目录数据
                catalog = createCatalog(productId, projectId, parentId, catalogName, level, type);
            }
            parentId = catalog.getId();
        }

        return catalog;
    }

    /**
     * 创建目录
     */
    private Catalog createCatalog(String productId, String projectId, String parentId, String name, int level, Integer type) {
        Catalog catalog = new Catalog();
        catalog.setProjectId(projectId);
        catalog.setProductId(productId);
        catalog.setPid(parentId);
        catalog.setName(name);
        catalog.setType(type);
        catalog.setLevel(level);
        return this.add(catalog);
    }

    /**
     * 判断上级目录下是否有工作流，如果没有，删除，level
     *
     * @param id 目录id
     * @return
     */
    @Transactional
    public int delCaseCatalog(String id) {
        int delCount = 0;
        if (StringUtils.isNotBlank(id)) {
            Catalog catalog = this.getById(id);
            if (null != catalog && Objects.equals(CatalogName.DEFAULT.getType(), catalog.getType())) {
                String parentIds = catalog.getParentIds();
                String[] split = StringUtils.split(parentIds, ",");
                List<Catalog> catalogs = this.getQuery().in("id", split).ne("level", 0).list();
                if (null != catalogs && !catalogs.isEmpty()) {
                    for (Catalog c : catalogs) {
                        int taskTotal = getService(Task.class).getQuery().withs("catalogParentIds").startsWith("catalogParentIds", c.getParentIds()).total();
                        if (0 == taskTotal) {
                            c.setDelChildren(true);
                            delCount += this.delete(c.getId(), c);
                        }
                    }
                }
            }
        }
        return delCount;
    }

    //清除目录下没有任务的目录
    public void cleanEmptyCatalog() {
        List<Catalog> catalogList = this.getQuery().ignoreTenantCode().ignorePermissions().gt("level", 0).eq("taskCount", 0).withs("taskCount").list();
        if (null != catalogList && !catalogList.isEmpty()) {
            Map<String, List<Catalog>> tenantCatalogList = catalogList.stream()
                    .collect(Collectors.groupingBy(Catalog::getTenantCode));
            if (null != tenantCatalogList && !tenantCatalogList.isEmpty()) {
                for (String tenantCode : tenantCatalogList.keySet()) {
                    List<Catalog> catalogs = tenantCatalogList.get(tenantCode);
                    if (null != catalogs && !catalogs.isEmpty()) {
                        String systemToken = AuthUtil.getSystemToken(null);
                        ThreadLocalUserUtil.setCode("token", systemToken);
                        //加上超管的租户code
                        ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
                        //级联删除目录
                        catalogs.forEach(t -> this.delCaseCatalog(t.getId()));
                    }
                }
            }
        }
    }

    @Transactional
    public void updateTaskCatalogIdDefault() {
        // 更新非调度中心产品的工作流 isImport 字段
        Integer updateCount = this.getSqlExecutor().excuteUpdate(
                "UPDATE `business_scc`.`scc_task` SET `is_import` = 1 WHERE product_id <> '11575107048320'");
        log.info("修改 isImport 数量={}", updateCount);
        // 获取所有项目的项目层级目录
//        List<Catalog> catalogList = this.getSqlExecutor().excuteSelect(
//                "SELECT * FROM scc_catalog " +
//                        "WHERE project_id IN (SELECT id FROM joyadata.tms_project) " +
//                        "AND name IN (SELECT name FROM joyadata.tms_project) " +
//                        "AND type = 1 AND level = 2 AND product_id = '11053212232192' " +
//                        " AND project_id = '12913749529472'"
        List<Catalog> catalogList = this.getSqlExecutor().excuteSelect(
                "SELECT * FROM scc_catalog " +
                        "WHERE project_id IN (SELECT id FROM joyadata.tms_project) " +
                        "AND name IN (SELECT name FROM joyadata.tms_project) " +
                        "AND type = 1 AND level = 2"
        ).list(Catalog.class);
        if (CollectionUtils.isEmpty(catalogList)) {
            return;
        }
        // 按租户分组处理目录
        Map<String, List<Catalog>> tenantCatalogMap = catalogList.stream()
                .collect(Collectors.groupingBy(Catalog::getTenantCode));
        if (MapUtils.isEmpty(tenantCatalogMap)) {
            return;
        }
        tenantCatalogMap.forEach(this::processTenantCatalogs);
    }

    private void processTenantCatalogs(String tenantCode, List<Catalog> catalogs) {
        if (CollectionUtils.isEmpty(catalogs)) {
            return;
        }
        // 设置当前线程的租户信息和系统 Token
        String systemToken = AuthUtil.getSystemToken(null);
        ThreadLocalUserUtil.setCode("token", systemToken);
        ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
        catalogs.forEach(this::fixCatalog);
    }

    private void fixCatalog(Catalog catalog) {
        String productId = catalog.getProductId();
        String projectId = catalog.getProjectId();

        if (StringUtils.isBlank(productId) || StringUtils.isBlank(projectId)) {
            return;
        }

        Catalog defaultCatalog = this.getQuery().ignorePermissions().eq("productId", productId).eq("projectId", projectId).eq("type", CatalogName.DEFAULT.getType()).eq("level", 2).eq("name", "default").one();
        String defaultCatalogId;
        String defaultCatalogPid;
        if (defaultCatalog == null) {
            // 如果不存在 default 目录，将当前目录改为 default
            catalog.setName("default");
            this.update(catalog);
            defaultCatalogId = catalog.getId();
            defaultCatalogPid = catalog.getPid();
        } else {
            // 如果存在 default 目录，迁移任务
            handleExistingDefaultCatalog(catalog, defaultCatalog);
            defaultCatalogId = defaultCatalog.getId();
            defaultCatalogPid = defaultCatalog.getPid();
        }

        // 修复子目录层级
        fixChildrenCatalog(catalog.getParentIds(), defaultCatalogId, defaultCatalogPid);
    }

    private void handleExistingDefaultCatalog(Catalog catalog, Catalog defaultCatalog) {
        // 迁移任务到 default 目录并删除当前目录
        migrateTasksToDefaultCatalog(catalog.getId(), defaultCatalog.getId());
        this.delCaseCatalog(catalog.getId());
    }

    private void fixChildrenCatalog(String parentIds, String defaultCatalogId, String defaultCatalogPid) {
        List<Catalog> childCatalogs = this.getQuery()
                .startsWith("parentIds", parentIds)
                .ne("name", "default")
                .list();

        if (CollectionUtils.isEmpty(childCatalogs)) {
            return;
        }

        childCatalogs.forEach(t -> fixChildCatalog(t, defaultCatalogId, defaultCatalogPid));
    }

    private void fixChildCatalog(Catalog childCatalog, String defaultCatalogId, String defaultCatalogPid) {

        Catalog existingCatalog = this.getQuery()
                .eq("productId", childCatalog.getProductId())
                .eq("projectId", childCatalog.getProjectId())
                .eq("name", childCatalog.getName())
                .eq("level", childCatalog.getLevel() - 1)
                .eq("type", childCatalog.getType())
                .one();

        if (existingCatalog == null) {
            // 如果没有重复目录，提升层级
            String parentIds = childCatalog.getParentIds();
            if (defaultCatalogId.equals(childCatalog.getPid())) {
                //原来挂在项目一层的目录pid修改
                childCatalog.setPid(defaultCatalogPid);
            }
            childCatalog.setLevel(childCatalog.getLevel() - 1);
            childCatalog.setParentIds(subThirdLevelCatalog(parentIds));
            childCatalog.setParentNames(subThirdLevelCatalog(childCatalog.getParentNames()));
            this.update(childCatalog);
        } else {
            // 如果有重复目录，迁移任务并删除当前目录
            migrateTasksToDefaultCatalog(childCatalog.getId(), existingCatalog.getId());
            this.delCaseCatalog(childCatalog.getId());
        }
    }

    private void migrateTasksToDefaultCatalog(String oldCatalogId, String newCatalogId) {
        Task task = new Task();
        task.setCatalogId(newCatalogId);

        List<WhereCondition> conditions = new ArrayList<>();
        conditions.add(new EqCondition("catalogId", oldCatalogId));

        getService(Task.class).updateBy(conditions, task);
    }

    private static String subThirdLevelCatalog(String input) {
        int firstComma = input.indexOf(',');
        int secondComma = input.indexOf(',', firstComma + 1);
        int thirdComma = input.indexOf(',', secondComma + 1);

        return input.substring(0, secondComma + 1) + input.substring(thirdComma + 1);
    }
}
