package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.User;
import com.joyadata.exception.AppErrorException;
import com.joyadata.exception.AppWarningException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.IUser;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.poi.excel.model.ExcelTitle;
import com.joyadata.poi.excel.util.JoyadataPoiUtil;
import com.joyadata.scc.dto.ningbo.IntegrationCatalogDTO;
import com.joyadata.scc.dto.ningbo.ProcessInstanceDTO;
import com.joyadata.scc.enums.ReleaseState;
import com.joyadata.scc.mapper.ExternalExpansionMapper;
import com.joyadata.scc.model.ApiCallRecord;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessInstance;
import com.joyadata.scc.model.dto.StartParam;
import com.joyadata.scc.util.MyBase64;
import com.joyadata.scc.util.Utils;
import com.joyadata.tms.model.Project;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.JsonUtil;
import com.joyadata.util.ThreadLocalUserUtil;
import com.joyadata.util.constant.AppPropertiesConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by zhushipeng on 2024/8/28 11:08.
 */
@Slf4j
@Service
public class ExternalExpansionService {
    @Autowired
    private ExternalExpansionMapper externalExpansionMapper;

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private ProcessInstanceService processInstanceService;
    @Autowired
    private TaskInstanceService taskInstanceService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    @Autowired
    private ApiCallRecordService apiCallRecordService;
    private JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);

    @Autowired
    private RestTemplate restTemplate;

    //判断是否需要拼接日切时间（浙江农信需求）
    @Value("${cutoff_flag:false}")
    private boolean cutoffFlag;

    public Response<?> getProcessDefinitionList(String catalogId, String catalogName, String processDefinitionName, String sourceBusinessName, String targetBusinessName, String sourceSimpleName, String targetSimpleName, String sourceTableName, String targetTableName, Integer page, Integer pager, String tenantCode) {
        log.info("查询getProcessDefinitionList catalogId={}, catalogName={},processDefinitionName={},sourceBusinessName={},targetBusinessName={},sourceSimpleName={}," + "targetSimpleName={},sourceTableName={},targetTableName={},page={},pager={},tenantCode={}", catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, page, pager, tenantCode);
        //0是第一页
        Integer start = page * pager;
        List<JSONObject> list = null;
        Integer total = null;
        try {
            String appDbType = ApplicationContextHelp.getProperty(AppPropertiesConfig.app_db_type);
            if ("mysql".equalsIgnoreCase(appDbType)) {
                list = externalExpansionMapper.getProcessDefinitionList(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, start, pager, tenantCode);
                total = externalExpansionMapper.getProcessDefinitionListTotal(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, tenantCode);
            } else if ("postgresql".equalsIgnoreCase(appDbType)) {
                list = externalExpansionMapper.getProcessDefinitionListForPg(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, start, pager, tenantCode);
                total = externalExpansionMapper.getProcessDefinitionListTotalForPg(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, tenantCode);
            }

            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(json -> {
                    String projectId = json.getString("project_id");
                    String createByName = json.getString("create_by_name");
                    String createBy = json.getString("create_by");
                    //第一位tenantCode，第二位projectId，第三位username，第四位userId
                    String assetToken = tenantCode + "," + projectId + "," + createByName + "," + createBy;
                    json.put("assetToken", MyBase64.encoder(assetToken));
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppErrorException("查询已发布的调度任务列表失败，失败原因={}", e.getMessage());
        }
        return ResponseFactory.makeSuccess(list, page, pager, total);
    }

    public Response<?> getProcessDefinitionListAndRunState(String catalogId, String catalogName, String processDefinitionName, String sourceBusinessName, String targetBusinessName, String sourceSimpleName, String targetSimpleName, String sourceTableName, String targetTableName, String taskState, String taskStartTime, Integer page, Integer pager, String tenantCode) {
        //0是第一页
        String appDbType = ApplicationContextHelp.getProperty(AppPropertiesConfig.app_db_type);
        Integer start = page * pager;
        List<JSONObject> list = null;
        Integer total = null;
        try {
            if ("mysql".equalsIgnoreCase(appDbType)) {
                total = externalExpansionMapper.getProcessDefinitionListAndRunStateTotal(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, taskState, taskStartTime, tenantCode);
                list = externalExpansionMapper.getProcessDefinitionListAndRunState(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, taskState, taskStartTime, start, pager, tenantCode);
            } else if ("postgresql".equalsIgnoreCase(appDbType)) {
                total = externalExpansionMapper.getProcessDefinitionListAndRunStateTotalForPg(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, taskState, taskStartTime, tenantCode);
                list = externalExpansionMapper.getProcessDefinitionListAndRunStateForPg(catalogId, catalogName, processDefinitionName, sourceBusinessName, targetBusinessName, sourceSimpleName, targetSimpleName, sourceTableName, targetTableName, taskState, taskStartTime, start, pager, tenantCode);
            }
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(json -> {
                    String projectId = json.getString("project_id");
                    String createByName = json.getString("create_by_name");
                    String createBy = json.getString("create_by");
                    //第一位tenantCode，第二位projectId，第三位username，第四位userId
                    String assetToken = tenantCode + "," + projectId + "," + createByName + "," + createBy;
                    json.put("assetToken", MyBase64.encoder(assetToken));
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppErrorException("查询已发布的调度任务（包含运行状态）列表失败，失败原因={}", e.getMessage());
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<String> processInstanceIds = list.stream().map(i -> i.getString("process_instance_id")).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(processInstanceIds)) {
                //获取读写总数
                List<JSONObject> rwcountlist = null;
                if ("mysql".equalsIgnoreCase(appDbType)) {
                    rwcountlist = externalExpansionMapper.getRWCount(processInstanceIds);
                } else if ("postgresql".equalsIgnoreCase(appDbType)) {
                    rwcountlist = externalExpansionMapper.getRWCountForPg(processInstanceIds);
                }

                if (null != rwcountlist && rwcountlist.size() > 0) {
                    Map<String, JSONObject> rwcountmap = new HashMap<>();
                    rwcountlist.forEach(jsonObject -> {
                        if (null != jsonObject) {
                            rwcountmap.put(jsonObject.getString("process_instance_id"), jsonObject);
                        }
                    });
                    list.forEach(jsonObject -> {
                        JSONObject rwcount = rwcountmap.get(jsonObject.getString("process_instance_id"));
                        if (null != rwcount) {
                            jsonObject.put("read_row_count", rwcount.getLongValue("total_read_row_count"));
                            jsonObject.put("write_row_count", rwcount.getLongValue("total_write_row_count"));
                        }
                    });
                }
            }
        }
        return ResponseFactory.makeSuccess(list, page, pager, total);
    }

    public Response<?> getProcessDefinitionById(String processDefinitionCode, String tenantCode) {
        log.info("查询getProcessDefinitionById processDefinitionCode={},tenantCode={}", processDefinitionCode, tenantCode);
        JSONObject processDefinition = externalExpansionMapper.getProcessDefinitionById(processDefinitionCode, tenantCode);
        if (null == processDefinition) {
            return ResponseFactory.makeSuccess(null);
        }
        String taskIds = processDefinition.getString("task_ids");
        log.info("查询到的ids={}", taskIds);
        List<JSONObject> taskList = new ArrayList<>();
        if (StringUtils.isNotBlank(taskIds)) {
            String[] taskIdList = taskIds.split(",");
            if (taskIdList.length > 0) {
                taskList = externalExpansionMapper.getTaskList(taskIdList, tenantCode);
                if (!CollectionUtils.isEmpty(taskList)) {
                    taskList.forEach(task -> {
                        JSONObject pluginTableStructure = task.getJSONObject("plugin_table_structure");
                        String querySql = "";
                        if (null != pluginTableStructure) {
                            JSONArray sourceFields = pluginTableStructure.getJSONArray("sourceFields");
                            log.info("task_name={}", task.getString("task_name"));
                            log.info("plugin_table_structure={}", pluginTableStructure);
                            StringBuilder querySqlBuilder = new StringBuilder();
                            //根据字段和表名拼接查询sql
                            if (null != sourceFields && sourceFields.size() > 0) {
                                querySqlBuilder.append("SELECT ");
                                sourceFields.forEach(obj -> {
                                    JSONObject json = JsonUtil.toJSON(obj);
                                    querySqlBuilder.append(json.getString("fieldName")).append(", ");
                                });
                                log.info("plugin_basics_config={}", task.getJSONObject("plugin_basics_config"));
                                if (null != task.getJSONObject("plugin_basics_config")) {
                                    String tableName = task.getJSONObject("plugin_basics_config").getString("tableName");
                                    querySql = querySqlBuilder.substring(0, querySqlBuilder.lastIndexOf(","));
                                    querySql = querySql + " FROM " + tableName + " WHERE 1=1 ";
                                }
                            }
                        }
                        task.remove("plugin_table_structure");
                        task.remove("plugin_basics_config");
                        task.put("query_sql", querySql);
                        log.info("plugin_advanced_config={}", task.getJSONObject("plugin_advanced_config"));
                        task.put("where_sql", task.getJSONObject("plugin_advanced_config") == null ? null : task.getJSONObject("plugin_advanced_config").getString("customWhereSql"));
                        task.remove("plugin_advanced_config");
                    });
                }
            }
        }
        processDefinition.put("taskList", taskList);
        return ResponseFactory.makeSuccess(processDefinition);
    }

    public Response<?> getProcessInstanceById(String processDefinitionCode, String tenantCode, String startTime, String endTime, String state, Integer page, Integer pager) {
        //0是第一页
        Integer start = page * pager;
        log.info("查询getProcessInstanceById processDefinitionCode={},tenantCode={}", processDefinitionCode, tenantCode);
        Integer total = externalExpansionMapper.getProcessInstanceByIdTotal(processDefinitionCode, tenantCode, startTime, endTime, state);
        List<ProcessInstanceDTO> processInstanceList = externalExpansionMapper.getProcessInstanceById(processDefinitionCode, tenantCode, start, pager, startTime, endTime, state);
        return ResponseFactory.makeSuccess(processInstanceList, page, pager, total);
    }

    public void downloadProcessInstance(String processDefinitionCode, String tenantCode, String startTime, String endTime, String state, HttpServletResponse response) throws IOException {
        HttpServletRequest request = ApplicationContextHelp.getRequest();
        log.info("查询getProcessInstanceById processDefinitionCode={},tenantCode={}", processDefinitionCode, tenantCode);
        List<ProcessInstanceDTO> processInstanceList = externalExpansionMapper.getProcessInstanceById(processDefinitionCode, tenantCode, null, null, startTime, endTime, state);
        if (!CollectionUtils.isEmpty(processInstanceList)) {
            List<JSONObject> jsonObjects = JsonUtil.toBeanList(processInstanceList, JSONObject.class);
            jsonObjects.forEach(jsonObject -> jsonObject.put("state", getState(jsonObject.getInteger("state"))));
            String fileName = "ProcessInstanceList.xlsx";
            String sheelName = "sheel1";
            List<ExcelTitle> title = getTitle();
            JoyadataPoiUtil.downloadExcel(request, response, fileName, sheelName, title, jsonObjects);
        } else {
            List<JSONObject> jsonObjects = new ArrayList<>();
            String fileName = "ProcessInstanceList.xlsx";
            String sheelName = "sheel1";
            List<ExcelTitle> title = getTitle();
            JoyadataPoiUtil.downloadExcel(request, response, fileName, sheelName, title, jsonObjects);
        }
    }

    public String getState(Integer state) {
        String result = "";
        //数字和状态的对应是和前端的转换保持一致的，找前端要的对应关系
        switch (state) {
            case 0:
                result = "提交成功";
                break;
            case 1:
                result = "正在执行";
                break;
            case 2:
                result = "准备暂停";
                break;
            case 3:
                result = "暂停";
                break;
            case 4:
                result = "准备停止";
                break;
            case 5:
                result = "停止";
                break;
            case 6:
                result = "失败";
                break;
            case 7:
                result = "成功";
                break;
            case 8:
                result = "需要容错";
                break;
            case 9:
                result = "KILL";
                break;
            case 12:
                result = "延时执行";
                break;
            case 13:
                result = "强制成功";
                break;
            case 14:
                result = "串行等待";
                break;
            case 15:
                result = "准备阻断";
                break;
            case 16:
                result = "阻断";
                break;
            case 17:
                result = "派发";
                break;
            default:
                result = "未知状态！";
        }
        return result;
    }

    private List<ExcelTitle> getTitle() {
        List<ExcelTitle> result = new ArrayList<>();

        ExcelTitle excelTitle = new ExcelTitle();
        excelTitle.setCode("id");
        excelTitle.setName("id");
        excelTitle.setPos(0);
        ExcelTitle excelTitle1 = new ExcelTitle();
        excelTitle1.setCode("catalogueName");
        excelTitle1.setName("目录名称");
        excelTitle1.setPos(1);
        ExcelTitle excelTitle2 = new ExcelTitle();
        excelTitle2.setCode("processDefinitionName");
        excelTitle2.setName("工作流名称");
        excelTitle2.setPos(2);
        ExcelTitle excelTitle3 = new ExcelTitle();
        excelTitle3.setCode("state");
        excelTitle3.setName("任务状态");
        excelTitle3.setPos(3);
        ExcelTitle excelTitle4 = new ExcelTitle();
        excelTitle4.setCode("readRowCount");
        excelTitle4.setName("读取总数");
        excelTitle4.setPos(4);
        ExcelTitle excelTitle5 = new ExcelTitle();
        excelTitle5.setCode("writeRowCount");
        excelTitle5.setName("写入总数");
        excelTitle5.setPos(5);
        ExcelTitle excelTitle6 = new ExcelTitle();
        excelTitle6.setCode("startTime");
        excelTitle6.setName("开始时间");
        excelTitle6.setPos(6);
        ExcelTitle excelTitle7 = new ExcelTitle();
        excelTitle7.setCode("endTime");
        excelTitle7.setName("结束时间");
        excelTitle7.setPos(7);
        result.add(excelTitle);
        result.add(excelTitle1);
        result.add(excelTitle2);
        result.add(excelTitle3);
        result.add(excelTitle4);
        result.add(excelTitle5);
        result.add(excelTitle6);
        result.add(excelTitle7);


        return result;
    }

    public Response<?> getCatalogTree(String tenantCode) {

        //因为集成页面的接口查询时只能查到本账号创建的数据，所以在这里直接用sql查库
        List<IntegrationCatalogDTO> catalogList = externalExpansionMapper.getCatalogList(tenantCode);
        if (CollectionUtils.isEmpty(catalogList)) {
            return ResponseFactory.makeSuccess(null);
        }
        List<IntegrationCatalogDTO> integrationCatalogDTOS = buildTree(catalogList);
        Map<String, List<IntegrationCatalogDTO>> collect = integrationCatalogDTOS.stream().collect(Collectors.groupingBy(IntegrationCatalogDTO::getProjectId));
        List<IntegrationCatalogDTO> result = new ArrayList<>();
        for (String projectId : collect.keySet()) {
            IntegrationCatalogDTO project = new IntegrationCatalogDTO();
            List<IntegrationCatalogDTO> children = collect.get(projectId);
            project.setId(projectId);
            project.setName(children.get(0).getProjectName());
            project.setProjectId(projectId);
            project.setProjectName(children.get(0).getProjectName());
            project.setChildren(children);
            result.add(project);
        }
        return ResponseFactory.makeSuccess(result);

//        String systemToken = AuthUtil.getSystemToken(tenantCode);
//        ThreadLocalUserUtil.setCode("token", systemToken);
//        Map<String, Object> header = new HashMap<>();
//        header.put("token", systemToken);
//        header.put("projectId", projectId);
//        header.put("project", projectId);
//        JSONObject params = new JSONObject();
//        params.put("isLeaf", true);
//        params.put("pageSize", -1);
//        JSONObject jsonObject = null;
//        String url = "http://gateway.dsg.com:8858/api/integration/catalogue/tree";
//        try {
//            jsonObject = httpRequestFeignService.get4url(url, params, header, JSONObject.class);
//        } catch (Exception e) {
//            log.error("调用集成分类树接口失败，错误信息={}", e.getMessage());
//            e.printStackTrace();
//            throw new AppErrorException("调用集成分类树接口失败，错误信息={}", e.getMessage());
//        }
//        if (jsonObject.getInteger("code") == 200) {
//            return ResponseFactory.makeSuccess(jsonObject.getJSONArray("data"));
//        } else {
//            return ResponseFactory.makeSuccess("调用集成分类树接口失败，错误信息={}", jsonObject.getString("msg"));
//        }
    }

    public static List<IntegrationCatalogDTO> buildTree(List<IntegrationCatalogDTO> integrationCatalogDTOList) {
        if (integrationCatalogDTOList == null || integrationCatalogDTOList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, IntegrationCatalogDTO> nodeMap = new HashMap<>();
        for (IntegrationCatalogDTO integrationCatalogDTO : integrationCatalogDTOList) {
            nodeMap.put(integrationCatalogDTO.getId(), integrationCatalogDTO);
        }

        List<IntegrationCatalogDTO> result = new ArrayList<>();
        for (IntegrationCatalogDTO integrationCatalogDTO : integrationCatalogDTOList) {
            if ("0".equals(integrationCatalogDTO.getParentId())) {
                result.add(integrationCatalogDTO);
            } else {
                IntegrationCatalogDTO parent = nodeMap.get(integrationCatalogDTO.getParentId());
                if (parent != null) {
                    parent.getChildren().add(integrationCatalogDTO);
                }
            }
        }
        return result;
    }

    public void initToken(String assetToken, String token) {
        if (null != assetToken) {
            //设置当前线程的tenantCode
            String decoder = MyBase64.decoder(assetToken);
            //第一位tenantCode，第二位projectId，第三位username，第四位userId
            String[] split = decoder.split(",");
            String tenantCode = split[0];
            String projectId = split[1];
            String username = split[2];
            String userId = split[3];
            ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
            ThreadLocalUserUtil.setCode("permissions", "true");
            //设置当前线程user
            User user = new User();
            user.setId(userId);
            user.setUsername(username);
            user.setTenantCode(tenantCode);
            IUser iUser = new IUser();
            iUser.setId(userId);
            iUser.setUsername(username);
            iUser.setJsonUser(JsonUtil.toJSON(user));
            ThreadLocalUserUtil.setCurrentUser(iUser);
        } else if (null != token) {
            //有token时可以直接返回
        } else {
            //token和assetToken都没有就报错
            throw new AppErrorException("缺少token或assetToken参数！请求失败！");
        }
    }

    public Response<?> startProcessInstance(HttpServletRequest httpServletRequest, JSONObject body, String assetToken, String token) {
        initToken(assetToken, token);
        Date callTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String stringCallTime = formatter.format(callTime);
        String id = body.getString("id");
        JSONObject startParams = body.getJSONObject("startParams");
        StartParam startParam = new StartParam();
        ProcessDefinition processDefinition = processDefinitionService.getQuery().eq("id", id).one();
        if (null == processDefinition) {
            log.error("未找到ID为[" + id + "]的工作流");
            addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，未找到ID为[" + id + "]的工作流", "", startParams);
            return ResponseFactory.makeError("未找到ID为[" + id + "]的工作流");
        }
        if (processDefinition.getReleaseState().equals(ReleaseState.OFFLINE.getCode())) {
            log.error("{} 未上线，工作流上线后才可以运行！", processDefinition.getName());
            addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，工作流未上线，上线后才可以运行！", "", startParams);
            return ResponseFactory.makeError("[" + processDefinition.getName() + "]未上线，工作流上线后才可以运行！");
        }
        //判断是否需要拼接日切时间（浙江农信需求）
        if (cutoffFlag) {
            String oldCallTime = apiCallRecordService.getQuery().eq("processDefinitionId", id).eq("callResult", "成功").sortbyDesc("createTime").oneValue("callTime", String.class);
            if (StringUtils.isNotBlank(oldCallTime)) {
                startParams.put("ac_date_begin", oldCallTime);
                startParams.put("ac_date_end", stringCallTime);
            } else {
                String globalParamMap = processDefinition.getGlobalParamMap();
                String acDateBegin = "1970-01-01 00:00:00";
                if (StringUtils.isNotEmpty(globalParamMap)) {
                    JSONArray jsonArray = JsonUtil.toJSONArray(globalParamMap);
                    for (Object obj : jsonArray) {
                        JSONObject jsonObject = JsonUtil.toJSON(obj);
                        if (null != jsonObject && jsonObject.containsKey("prop") && "ac_date_begin_default".equals(jsonObject.getString("prop"))) {
                            acDateBegin = jsonObject.getString("value");
                        }
                    }
                }
                startParams.put("ac_date_begin", acDateBegin);
                startParams.put("ac_date_end", stringCallTime);
            }
            //同一个工作流在同一天，同样的会计日期，只能调一次，不传会计日期，就不限制
            String acDate = startParams.getString("ac_date");
            Boolean more = body.getBoolean("more");//此参数控制是否可以多次调用，true不进此逻辑
            if (StringUtils.isNotBlank(acDate) && !(null != more && more)) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String start = dateFormat.format(callTime) + " 00:00:00";
                String end = dateFormat.format(callTime) + " 23:59:59";
                List<ApiCallRecord> apiCallRecords = apiCallRecordService.getQuery().eq("processDefinitionId", id).eq("callResult", "成功").between("callTime", start, end).list();
                if (!CollectionUtils.isEmpty(apiCallRecords)) {
                    for (ApiCallRecord apiCallRecord : apiCallRecords) {
                        JSONObject params = JsonUtil.toJSON(apiCallRecord.getStartParams());
                        if (null != params && params.getString("ac_date").equals(acDate)) {
                            addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，[" + processDefinition.getName() + "]在今日使用会计日期[" + params.getString("ac_date") + "]的已调起，不允许再次调用", "", startParams);
                            return new Response<>(104, "[" + processDefinition.getName() + "]在今日使用会计日期[" + params.getString("ac_date") + "]的已调起，不允许再次调用");
                        }
                    }
                }
            }
        }
        //组装请求参数
        JSONObject scheduleTime = new JSONObject();
        String datestr = formatter.format(new Date());
        scheduleTime.put("complementStartDate", datestr);
        scheduleTime.put("complementEndDate", datestr);
        if (null != startParams && startParams.size() > 0) {
            startParam.setStartParams(startParams.toJSONString());
        }
        startParam.setExecType("START_PROCESS");
        startParam.setScheduleTime(JSONObject.toJSONString(scheduleTime));
        Map resultMap = null;
        try {
            resultMap = processDefinitionService.startProcessInstance(processDefinition.getId(), startParam, true);
        } catch (Exception e) {
            log.error("运行工作流失败！错误信息=", e);
            addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，运行工作流失败！错误信息=" + e.getMessage(), "", startParams);
            return ResponseFactory.makeError("运行工作流失败！错误信息=" + e.getMessage());
        }
        Response response = Utils.responseInfo(resultMap);
        if (response.getCode() == 0) {
            JSONObject result = JsonUtil.toJSON(response.getResult());
            addAprCallRecord(httpServletRequest, body, stringCallTime, "成功", result.getString("processInstanceId"), startParams);
        } else {
            addAprCallRecord(httpServletRequest, body, stringCallTime, "失败，运行工作流失败！错误信息=" + response.getMessage(), "", startParams);
        }
        return response;
    }

    public Response<?> getProcessInstanceState(Map<String, String> body, String assetToken, String token) {
        initToken(assetToken, token);
        String id = body.get("id");
        String processDefinitionName = body.get("processDefinitionName");
        if (StringUtils.isBlank(id) && StringUtils.isNotBlank(processDefinitionName)) {
            id = processDefinitionService.getQuery().eq("name", processDefinitionName).oneValue("id", String.class);
        }
        String processInstanceId = processInstanceService.getQuery().eq("processDefinitionId", id).sortbyDesc("createTime").oneValue("id", String.class);
        if (StringUtils.isBlank(processInstanceId)) {
            return ResponseFactory.makeSuccess("success", "未找到工作流实例！");
        }
        Integer state = processInstanceService.getQuery().eq("id", processInstanceId).oneValue("state", Integer.class);
        String result = getState(state);
        return ResponseFactory.makeSuccess("success", result);
    }

    public Response<?> stopProcessInstanceByProcessId(Map<String, String> body, String assetToken, String token) {
        initToken(assetToken, token);
        String errorMessage = "";
        String id = body.get("id");
        String processDefinitionName = body.get("processDefinitionName");
        //查询工作流时如果有id就用id查询，有name就用name查询
        //用name查询是浙江农信需要的
        IQueryWrapper<ProcessDefinition> query = processDefinitionService.getQuery();
        if (StringUtils.isNotBlank(id)) {
            query.eq("id", id);
            errorMessage = "未找到ID为[" + id + "]的工作流";
        }
        if (StringUtils.isNotBlank(processDefinitionName)) {
            query.eq("name", processDefinitionName);
            errorMessage = "未找到NAME为[" + processDefinitionName + "]的工作流";
        }
        ProcessDefinition processDefinition = query.one();
        if (null == processDefinition) {
            log.error(errorMessage);
            throw new AppErrorException(errorMessage);
        }
        Map result = processDefinitionService.forceStopProcessInstance(processDefinition.getId(), processDefinition.getProjectId());
        return Utils.responseInfo(result);
    }

    public void receiveEvent(String processName, String assetToken, JSONObject body) {
        initToken(assetToken, null);
        String eventId = body.getString("eventCode");
        int execStatus = -1;
        if ("B301006".equals(eventId)) {
            execStatus = 1;//执行中
        } else if ("B301005".equals(eventId)) {
            execStatus = 3;//执行失败
        } else if ("B301004".equals(eventId)) {
            execStatus = 2;//执行成功
        }
        String processDefinitionId = body.getJSONObject("evenData").getString("processDefinitionId");
        if (execStatus != -1) {
            JSONObject startParams = new JSONObject();
            startParams.put("execStatus", execStatus);//状态
            startParams.put("taskId", processDefinitionId);//任务id
            Map resultMap = null;
            try {
                resultMap = startProcess(startParams, processName, body.getString("projectId"));
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (Integer.parseInt(String.valueOf(resultMap.get("code"))) == 0) {
                log.info("修改状态成功！");
            } else {
                log.error("修改状态失败！");
            }
        }
    }

    private Map startProcess(JSONObject startParams, String processDefinitionName, String projectId) throws UnsupportedEncodingException {
        StartParam startParam = new StartParam();
        if (null != startParams && startParams.size() > 0) {
            startParam.setStartParams(startParams.toJSONString());
        }
        ProcessDefinition processDefinition = processDefinitionService.getQuery().eq("name", processDefinitionName).eq("projectId", projectId).one();
        if (null == processDefinition) {
            log.error("未找到名称为 {} 的工作流", processDefinitionName);
            throw new AppErrorException("未找到名称为" + processDefinitionName + "的工作流！");
        }
        if (processDefinition.getReleaseState().equals(ReleaseState.OFFLINE.getCode())) {
            log.error("{} 未上线，工作流上线后才可以运行！", processDefinitionName);
            throw new AppErrorException(processDefinitionName + "未上线，工作流上线后才可以运行！");
        }
        //组装请求参数
        JSONObject scheduleTime = new JSONObject();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String datestr = dateFormat.format(new Date());
        scheduleTime.put("complementStartDate", datestr);
        scheduleTime.put("complementEndDate", datestr);

        startParam.setExecType("START_PROCESS");
        startParam.setScheduleTime(JSONObject.toJSONString(scheduleTime));
        Map resultMap = processDefinitionService.startProcessInstance(processDefinition.getId(), startParam, false);
        return resultMap;
    }

    public Response<?> stopProcessInstanceByInstanceId(String processInstanceId, String assetToken, String token) {
        initToken(assetToken, token);
        String projectId = processInstanceService.getQuery().eq("id", processInstanceId).oneValue("projectId", String.class);
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/projects/" + projectId + "/executors/execute?processInstanceId=" +
                processInstanceId + "&executeType=STOP";
        Map result = httpRequestFeignService.post4url(url, null, headers, Map.class);
        if (Integer.parseInt(String.valueOf(result.get("code"))) == 0 || result.get("msg").toString().equals("execute process instance error") || Integer.parseInt(String.valueOf(result.get("code"))) == 50006) {
            //终止如果返回工作流错误 其实也是停止掉了返回成功状态
            return ResponseFactory.makeSuccess("success");
        }
        return ResponseFactory.makeError(result.get("msg").toString());
    }

    public Response<?> getProcessInstanceByInstanceId(String processInstanceId, String assetToken, String token) {
        initToken(assetToken, token);
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + "/process-instances/external/" + processInstanceId + "/custom";
//        String url = "http://127.0.0.1:12345/dolphinscheduler/process-instances/external/" + processInstanceId + "/custom";
        Map result = httpRequestFeignService.get4url(url, null, headers, Map.class);
        if (Integer.parseInt(String.valueOf(result.get("code"))) == 0) {
            return ResponseFactory.makeSuccess("success", result.get("data"));
        }
        return ResponseFactory.makeError(result.get("msg").toString());
    }

    public void addAprCallRecord(HttpServletRequest httpServletRequest, JSONObject body, String callTime, String resultMsg, String processInstanceId, JSONObject params) {
        User user = ThreadLocalUserUtil.getUser(User.class);
        new Thread(() -> {
            ThreadLocalUserUtil.setCurrentUser(user.toIUser());
            log.info("开始添加外部启动api调用记录。");
            String projectName = body.getString("projectName");
            String projectId = "";
            if (StringUtils.isNotBlank(projectName)) {
                projectId = projectJoyaFeignService.getQuery().eq("name", projectName).oneValue("id", String.class);
            }
            ApiCallRecord apiCallRecord = new ApiCallRecord();
            apiCallRecord.setCallIp(httpServletRequest.getRemoteHost());
            apiCallRecord.setCallTime(callTime);
            apiCallRecord.setCallResult(resultMsg);
            apiCallRecord.setCallUserId(user.getId());
            apiCallRecord.setCallUserName(user.getNickname());
            apiCallRecord.setProjectId(projectId);
            apiCallRecord.setProjectName(projectName);
            apiCallRecord.setProcessDefinitionId(body.getString("id"));
            apiCallRecord.setProcessDefinitionName(body.getString("processName"));
            apiCallRecord.setProcessInstanceId(processInstanceId);
            apiCallRecord.setAppId(body.getString("appid"));
            if (null != params) {
                apiCallRecord.setStartParams(params.toJSONString());
            }
            apiCallRecordService.add(apiCallRecord);
            log.info("添加外部启动api调用记录完成。");
        }).start();

    }

    public void sendTaskState(String toUrl, String requestType, String paramType, String scope, String getParamName) {
        String sql = getTaskStateSql(scope);
        JSONObject jsonObject = taskInstanceService.getSqlExecutor().excuteSelect(sql).one(JSONObject.class);
        JSONObject result;
        try {
            result = sendTaskStateToHttp(jsonObject, toUrl, requestType, paramType, getParamName);
        } catch (RestClientException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (AppWarningException e) {
            throw new AppWarningException("不支持" + requestType + "请求方式，只支持GET、POST！");
        }
        log.info("发送任务状态返回结果：{}", result);
    }

    private JSONObject sendTaskStateToHttp(JSONObject jsonObject, String toUrl, String requestType, String paramType, String getParamName) throws UnsupportedEncodingException {
        if ("post".equalsIgnoreCase(requestType.trim())) {
            if ("x-www-form-urlencoded".equalsIgnoreCase(paramType.trim())) {
                MultiValueMap<String, String> clientParam = new LinkedMultiValueMap<>();
                clientParam.add(getParamName, jsonObject.toJSONString());
                return sendFormDataRequest(toUrl, "application/x-www-form-urlencoded;charset=UTF-8", clientParam);
            } else if ("form-data".equalsIgnoreCase(paramType.trim())) {
                MultiValueMap<String, String> clientParam = new LinkedMultiValueMap<>();
                clientParam.add(getParamName, jsonObject.toJSONString());
                return sendFormDataRequest(toUrl, "multipart/form-data;charset=UTF-8", clientParam);
            } else if ("json".equalsIgnoreCase(paramType.trim())) {
                return restTemplate.postForObject(toUrl, jsonObject, JSONObject.class);
            } else {
                throw new AppWarningException("不支持" + paramType + "参数类型，只支持x-www-form-urlencoded、form-data、json！");
            }
        } else if ("get".equalsIgnoreCase(requestType.trim())) {
            toUrl = toUrl + "?" + getParamName + "=" + URLEncoder.encode(jsonObject.toJSONString(), "UTF-8");
            return restTemplate.getForObject(toUrl, JSONObject.class);
        } else {
            throw new AppWarningException("不支持" + requestType + "请求方式，只支持GET、POST！");
        }
    }

    private JSONObject sendFormDataRequest(String toUrl, String contentType, MultiValueMap<String, String> clientParam) {
        if (contentType.contains("application/x-www-form-urlencoded")) {
            // 对于 form-urlencoded，使用fromFormData
            return WebClient.create(toUrl)
                    .post()
                    .headers(header -> header.add("Content-Type", contentType))
                    .body(BodyInserters.fromFormData(clientParam))
                    .exchangeToMono(response -> response.bodyToMono(JSONObject.class))
                    .block();
        } else if (contentType.contains("multipart/form-data")) {
            // 对于 multipart/form-data，使用fromMultipartData
            return WebClient.create(toUrl)
                    .post()
                    // 不手动设置Content-Type，让WebClient自动处理multipart/form-data的boundary
                    .body(BodyInserters.fromMultipartData(clientParam))
                    .exchangeToMono(response -> response.bodyToMono(JSONObject.class))
                    .block();
        } else {
            // 其他情况，按原来的方式处理
            return WebClient.create(toUrl)
                    .post()
                    .headers(header -> header.add("Content-Type", contentType))
                    .body(BodyInserters.fromFormData(clientParam))
                    .exchangeToMono(response -> response.bodyToMono(JSONObject.class))
                    .block();
        }
    }

    private String getTaskStateSql(String scope) {
        String sql;
        if ("yesterday".equalsIgnoreCase(scope.trim())) {//查询昨天一整天的
            sql = "SELECT \n" +
                    "    COUNT(DISTINCT sptr.post_task_code) AS `作业总数`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 0 THEN 1 ELSE 0 END) AS `提交成功`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 1 THEN 1 ELSE 0 END) AS `正在执行`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 3 THEN 1 ELSE 0 END) AS `暂停`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 6 THEN 1 ELSE 0 END) AS `失败`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 7 THEN 1 ELSE 0 END) AS `成功`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 9 THEN 1 ELSE 0 END) AS `KILL`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 12 THEN 1 ELSE 0 END) AS `延时执行`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 13 THEN 1 ELSE 0 END) AS `强制成功`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 17 THEN 1 ELSE 0 END) AS `派发`,\n" +
                    "    COUNT(DISTINCT sptr.post_task_code) - COUNT(DISTINCT CASE WHEN latest_sti.state IS NOT NULL THEN sptr.post_task_code END) AS `未运行`\n" +
                    "FROM \n" +
                    "    scc_process_definition spd\n" +
                    "    INNER JOIN scc_process_task_relation sptr ON spd.id = sptr.process_definition_code \n" +
                    "    INNER JOIN joyadata.tms_project tp ON spd.project_id = tp.id\n" +
                    "    LEFT JOIN (\n" +
                    "        SELECT \n" +
                    "            sti.task_id,\n" +
                    "            sti.state\n" +
                    "        FROM \n" +
                    "            scc_task_instance sti\n" +
                    "            INNER JOIN (\n" +
                    "                SELECT \n" +
                    "                    task_id,\n" +
                    "                    MAX(submit_time) AS latest_submit_time\n" +
                    "                FROM \n" +
                    "                    scc_task_instance\n" +
                    "                WHERE \n" +
                    "                    submit_time >= CURDATE() - INTERVAL 1 day\n" +
                    "                    AND submit_time < CURDATE()\n" +
                    "                GROUP BY \n" +
                    "                    task_id\n" +
                    "            ) latest ON sti.task_id = latest.task_id AND sti.submit_time = latest.latest_submit_time\n" +
                    "    ) latest_sti ON sptr.post_task_code = latest_sti.task_id\n" +
                    "WHERE \n" +
                    "    spd.release_state = '上线';";
        } else if ("today".equalsIgnoreCase(scope.trim())) {//查询今天的，截止到下午六点
            sql = "SELECT \n" +
                    "    COUNT(DISTINCT sptr.post_task_code) AS `作业总数`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 0 THEN 1 ELSE 0 END) AS `提交成功`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 1 THEN 1 ELSE 0 END) AS `正在执行`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 3 THEN 1 ELSE 0 END) AS `暂停`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 6 THEN 1 ELSE 0 END) AS `失败`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 7 THEN 1 ELSE 0 END) AS `成功`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 9 THEN 1 ELSE 0 END) AS `KILL`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 12 THEN 1 ELSE 0 END) AS `延时执行`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 13 THEN 1 ELSE 0 END) AS `强制成功`,\n" +
                    "    SUM(CASE WHEN latest_sti.state = 17 THEN 1 ELSE 0 END) AS `派发`,\n" +
                    "    COUNT(DISTINCT sptr.post_task_code) - COUNT(DISTINCT CASE WHEN latest_sti.state IS NOT NULL THEN sptr.post_task_code END) AS `未运行`\n" +
                    "FROM \n" +
                    "    scc_process_definition spd\n" +
                    "    INNER JOIN scc_process_task_relation sptr ON spd.id = sptr.process_definition_code \n" +
                    "    INNER JOIN joyadata.tms_project tp ON spd.project_id = tp.id\n" +
                    "    LEFT JOIN (\n" +
                    "        SELECT \n" +
                    "            sti.task_id,\n" +
                    "            sti.state\n" +
                    "        FROM \n" +
                    "            scc_task_instance sti\n" +
                    "            INNER JOIN (\n" +
                    "                SELECT \n" +
                    "                    task_id,\n" +
                    "                    MAX(submit_time) AS latest_submit_time\n" +
                    "                FROM \n" +
                    "                    scc_task_instance\n" +
                    "                WHERE \n" +
                    "                    submit_time >= CURDATE() \n" +
                    "                    AND submit_time < CURDATE() + INTERVAL 18 hour \n" +
                    "                GROUP BY \n" +
                    "                    task_id\n" +
                    "            ) latest ON sti.task_id = latest.task_id AND sti.submit_time = latest.latest_submit_time\n" +
                    "    ) latest_sti ON sptr.post_task_code = latest_sti.task_id\n" +
                    "WHERE \n" +
                    "    spd.release_state = '上线';";
        } else {
            throw new AppErrorException("scope参数不支持！");
        }
        return sql;
    }


    public Response<?> batchStartProcessInstance(HttpServletRequest httpServletRequest, JSONObject body, String assetToken, String token) {
        initToken(assetToken, token);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String id = body.getString("id");
        JSONArray startParamsArr = body.getJSONArray("startParamsList");
        List<String> startParamsList = new ArrayList<>(startParamsArr.size());
        for (int i = 0; i < startParamsArr.size(); i++) {
            startParamsList.add(startParamsArr.getString(i));
        }

        StartParam startParam = new StartParam();
        ProcessDefinition processDefinition = processDefinitionService.getQuery().eq("id", id).one();
        if (null == processDefinition) {
            log.error("未找到ID为[" + id + "]的工作流");
            return ResponseFactory.makeError("未找到ID为[" + id + "]的工作流");
        }
        if (processDefinition.getReleaseState().equals(ReleaseState.OFFLINE.getCode())) {
            log.error("{} 未上线，工作流上线后才可以运行！", processDefinition.getName());
            return ResponseFactory.makeError("[" + processDefinition.getName() + "]未上线，工作流上线后才可以运行！");
        }

        //组装请求参数
        JSONObject scheduleTime = new JSONObject();
        String datestr = formatter.format(new Date());
        scheduleTime.put("complementStartDate", datestr);
        scheduleTime.put("complementEndDate", datestr);
        if (null != startParamsList && startParamsList.size() > 0) {
            startParam.setStartParamsList(startParamsList);
        }
        startParam.setExecType("START_PROCESS");
        startParam.setScheduleTime(JSONObject.toJSONString(scheduleTime));
        Map resultMap = null;
        try {
            resultMap = processDefinitionService.batchStartProcessInstance(processDefinition.getId(), startParam, true);
        } catch (Exception e) {
            log.error("运行工作流失败！错误信息=", e);
            return ResponseFactory.makeError("运行工作流失败！错误信息=" + e.getMessage());
        }
        Response response = Utils.responseInfo(resultMap);
        return response;
    }
}
