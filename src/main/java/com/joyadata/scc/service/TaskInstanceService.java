package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.exception.AppErrorException;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.enums.TaskExecutionStatus;
import com.joyadata.scc.model.*;
import com.joyadata.scc.model.dto.TaskMonitorDTO;
import com.joyadata.service.BaseService;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import com.joyadata.util.constant.AppPropertiesConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskInstanceService
 * @Description: TODO
 * @date 2024/4/18
 */
@Slf4j
@Service
public class TaskInstanceService extends BaseService<TaskInstance> {
    public void setTaskMonitor(List<TaskInstance> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<String> taskInstanceIds = result.stream().map(TaskInstance::getId).collect(Collectors.toList());
        List<TaskMonitor> taskMonitorList = getService(TaskMonitor.class).getSqlExecutor().excuteSelect(selectTaskMonitorByTaskInstanceIdSql(taskInstanceIds)).list(TaskMonitor.class);
        Map<String, TaskMonitor> taskMonitorMap = new HashMap<>();
        if (CollectionUtils.isEmpty(taskMonitorList)) {
            return;
        }
        taskMonitorList.forEach(taskMonitor -> taskMonitorMap.put(taskMonitor.getTaskInstanceId(), taskMonitor));
        for (TaskInstance taskInstance : result) {
            TaskMonitor taskMonitor = taskMonitorMap.get(taskInstance.getId());
            if (null == taskMonitor) {
                continue;
            }
            Long memoryUsage = taskMonitor.getMemoryUsage();//单位是kb需要除1024
            Double finalMemoryUsage = null;
            if (null != memoryUsage && memoryUsage > 0) {
                BigDecimal bigDecimal = BigDecimal.valueOf(memoryUsage);
                //四舍五入保留两位小数
                finalMemoryUsage = bigDecimal.divide(BigDecimal.valueOf(1024), 2, RoundingMode.HALF_UP).doubleValue();
            }
            Long totalMemory = taskMonitor.getTotalMemory();
            Double finalTotalMemory = null;
            if (null != totalMemory && totalMemory > 0) {//单位是kb需要除1024
                BigDecimal bigDecimal = BigDecimal.valueOf(totalMemory);
                //四舍五入保留两位小数
                finalTotalMemory = bigDecimal.divide(BigDecimal.valueOf(1024), 2, RoundingMode.HALF_UP).doubleValue();
            }
            taskInstance.setMemProportion((finalMemoryUsage == null ? 0 : finalMemoryUsage) + "/" + (finalTotalMemory == null ? 0 : finalTotalMemory));
            taskInstance.setCpuProportion((taskMonitor.getCpuUsage() == null ? 0.0 : taskMonitor.getCpuUsage()) + "%");
        }
    }

    private String selectTaskMonitorByTaskInstanceIdSql(List<String> taskInstanceIds) {
        String sql = "SELECT\n" +
                "    t1.* \n" +
                "FROM\n" +
                "    business_ds.t_ds_task_monitor t1\n" +
                "JOIN\n" +
                "    (SELECT\n" +
                "        task_instance_id,\n" +
                "        MAX(create_time) AS max_create_time \n" +
                "     FROM\n" +
                "        business_ds.t_ds_task_monitor\n" +
                "     GROUP BY\n" +
                "        task_instance_id\n" +
                "    ) t2 ON t1.task_instance_id = t2.task_instance_id AND t1.create_time = t2.max_create_time\n" +
                "WHERE\n" +
                "    t1.task_instance_id IN ('%s')";
        String join = String.join("','", taskInstanceIds);
        return String.format(sql, join, join);
    }

    public Response<?> cpuTendencyChart(String taskInstanceId, String cpuInterval, Integer page, Integer pager) {
        String appDbType = ApplicationContextHelp.getProperty(AppPropertiesConfig.app_db_type);
        String intervalMillis = getIntervalMillis(cpuInterval, appDbType);
        List<TaskMonitorDTO> taskMonitorList = getService(TaskMonitor.class).getSqlExecutor()
                .excuteSelect(getCpuTendencyChartSql(taskInstanceId, intervalMillis, page, pager, appDbType)).list(TaskMonitorDTO.class);
        return ResponseFactory.makeSuccess(taskMonitorList);
    }

    private String getCpuTendencyChartSql(String taskInstanceId, String intervalMillis, Integer page, Integer pager, String appDbType) {
        Integer startSize = page * pager;
        String sql = StringUtils.EMPTY;
        if ("mysql".equalsIgnoreCase(appDbType)) {
            sql = "SELECT\n" +
                    "\tDATE_FORMAT( create_time, '%s' ) AS create_time,\n" +
                    "\tROUND( AVG( cpu_usage ), 2 ) AS cpu_usage\n" +
                    "FROM\n" +
                    "\tscc_task_monitor \n" +
                    "WHERE\n" +
                    "\ttask_instance_id = '%s' \n" +
                    "GROUP BY\n" +
                    "\tDATE_FORMAT( create_time, '%s' ) \n" +
                    "ORDER BY\n" +
                    "\tcreate_time desc\n" +
                    "\tLIMIT %d,%d;";
        } else if ("postgresql".equalsIgnoreCase(appDbType)) {
            sql = "SELECT\n" +
                    "\tTO_CHAR( create_time, '%s' ) AS create_time,\n" +
                    "\tROUND( AVG( cpu_usage::numeric ), 2 ) AS cpu_usage\n" +
                    "FROM\n" +
                    "\tscc_task_monitor \n" +
                    "WHERE\n" +
                    "\ttask_instance_id = '%s' \n" +
                    "GROUP BY\n" +
                    "\tTO_CHAR( create_time, '%s' ) \n" +
                    "ORDER BY\n" +
                    "\tcreate_time desc\n" +
                    " LIMIT %d OFFSET %d";
            return String.format(sql, intervalMillis, taskInstanceId, intervalMillis, pager, startSize);
        }
        return String.format(sql, intervalMillis, taskInstanceId, intervalMillis, startSize, pager);
    }

    // 将时间间隔类型转换为毫秒数
    private String getIntervalMillis(String interval, String appDbType) {
        if ("postgresql".equalsIgnoreCase(appDbType)) {
            switch (interval) {
                case "minute":
                    return "YYYY-MM-DD HH24:MI:00";
                case "hour":
                    return "YYYY-MM-DD HH24:00:00";
                case "day":
                    return "YYYY-MM-DD 00:00:00";
                default:
                    throw new AppErrorException("无效的间隔！");
            }
        }

        //默认是mysql语法
        switch (interval) {
            case "minute":
                return "%Y-%m-%d %H:%i:00";
            case "hour":
                return "%Y-%m-%d %H:00:00";
            case "day":
                return "%Y-%m-%d 00:00:00";
            default:
                throw new AppErrorException("无效的间隔！");
        }
    }

    public Response<?> memTendencyChart(String taskInstanceId, String interval, Integer page, Integer pager) {
        String appDbType = ApplicationContextHelp.getProperty(AppPropertiesConfig.app_db_type);
        String intervalMillis = getIntervalMillis(interval, appDbType);
        List<TaskMonitorDTO> taskMonitorList = getService(TaskMonitor.class).getSqlExecutor()
                .excuteSelect(getMemTendencyChartSql(taskInstanceId, intervalMillis, page, pager, appDbType)).list(TaskMonitorDTO.class);
        return ResponseFactory.makeSuccess(taskMonitorList);
    }

    private String getMemTendencyChartSql(String taskInstanceId, String intervalMillis, Integer page, Integer pager, String appDbType) {
        Integer startSize = page * pager;
        String sql = StringUtils.EMPTY;
        if ("mysql".equalsIgnoreCase(appDbType)) {
            sql = "SELECT\n" +
                    "\tDATE_FORMAT( create_time, '%s' ) AS create_time,\n" +
                    "\tROUND( AVG( memory_usage / 1024.0 ), 2 ) AS memory_usage \n" +
                    "FROM\n" +
                    "\tscc_task_monitor \n" +
                    "WHERE\n" +
                    "\ttask_instance_id = '%s' \n" +
                    "GROUP BY\n" +
                    "\tDATE_FORMAT( create_time, '%s' ) \n" +
                    "ORDER BY\n" +
                    "\tcreate_time desc\n" +
                    "\tLIMIT %d,%d";
        } else if ("postgresql".equalsIgnoreCase(appDbType)) {
            sql = "SELECT\n" +
                    "\tTO_CHAR( create_time, '%s' ) AS create_time,\n" +
                    "\tROUND( AVG( memory_usage::numeric / 1024.0 ), 2 ) AS memory_usage \n" +
                    "FROM\n" +
                    "\tscc_task_monitor \n" +
                    "WHERE\n" +
                    "\ttask_instance_id = '%s' \n" +
                    "GROUP BY\n" +
                    "\tTO_CHAR( create_time, '%s' ) \n" +
                    "ORDER BY\n" +
                    "\tcreate_time desc\n" +
                    "\tLIMIT %d OFFSET %d";
            return String.format(sql, intervalMillis, taskInstanceId, intervalMillis, pager, startSize);
        }
        return String.format(sql, intervalMillis, taskInstanceId, intervalMillis, startSize, pager);
    }

    public Response<List<?>> getTaskInstanceList(HttpServletRequest request, Integer page, Integer pager) {
        String appDbType = ApplicationContextHelp.getProperty(AppPropertiesConfig.app_db_type);
        String sql = getRawSql(request, appDbType);
        Integer total = getTaskInstanceTotal(sql);
        List<TaskInstance> taskInstanceList = getTaskInstances(sql, page, pager, appDbType);
        if (!CollectionUtils.isEmpty(taskInstanceList)) {
            taskInstanceList.forEach(TaskInstance::afterDbInit);
        }
        return ResponseFactory.makeSuccess(taskInstanceList, page, pager, total);
    }

    private List<TaskInstance> getTaskInstances(String sql, Integer page, Integer pager, String appDbType) {
        Integer start = page * pager;
        if ("mysql".equalsIgnoreCase(appDbType)) {
            sql = sql + " limit " + start + "," + pager;
        } else if ("postgresql".equalsIgnoreCase(appDbType)) {
            sql = sql + " limit " + pager + " OFFSET " + start;
        }
        List<TaskInstance> taskInstanceList = getSqlExecutor().excuteSelect(sql).list(TaskInstance.class);
        return taskInstanceList;
    }

    private Integer getTaskInstanceTotal(String sql) {
        sql = "select count(1) from (" + sql + ") table_a";

        Integer total = getSqlExecutor().excuteSelect(sql).one(Integer.class);
        return total;
    }

    private String getRawSql(HttpServletRequest request, String appDbType) {

        String sql = "SELECT\n" +
                "\tt1.*,\n" +
                "\tt2.product_id AS productId,\n" +
                "\tp1.NAME AS processInstanceName \n" +
                "FROM\n" +
                "\tbusiness_ds.t_ds_task_instance t1\n" +
                "\tLEFT JOIN business_scc.scc_task t2 ON t1.task_code = t2.id\n" +
                "\tLEFT JOIN business_ds.t_ds_process_instance p1 ON t1.process_instance_id = p1.id \n" +
                "WHERE\n" +
                "\tt1.state = 1 \n" +
                "\tAND t2.product_id IS NOT NULL ";

        if ("postgresql".equalsIgnoreCase(appDbType)) {
            sql = "SELECT\n" +
                    "\tt1.*,\n" +
                    "\tt2.product_id AS productId,\n" +
                    "\tp1.NAME AS processInstanceName \n" +
                    "FROM\n" +
                    "\tbusiness_scc.scc_task_instance t1\n" +
                    "\tLEFT JOIN business_scc.scc_task t2 ON t1.task_id = t2.id\n" +
                    "\tLEFT JOIN business_ds.t_ds_process_instance p1 ON t1.process_instance_id = p1.id \n" +
                    "WHERE\n" +
                    "\tt1.state = 1 \n" +
                    "\tAND t2.product_id IS NOT NULL ";
        }
        String projectId = request.getHeader("projectId");
        if (StringUtils.isNotBlank(projectId)) {
            sql = sql + " AND t2.project_id = '" + projectId + "'";
        }
        String tenantCode = ThreadLocalUserUtil.getCode("tenantCode");
        if (StringUtils.isNotBlank(tenantCode)) {
            sql = sql + " AND t2.tenant_code = '" + tenantCode + "'";
        }
        String productId = request.getParameter("productId");
        if (StringUtils.isNotBlank(productId)) {
            sql = sql + " AND t2.product_id = '" + productId + "'";
        }
        String keywords = request.getParameter("keywords");
        if (StringUtils.isNotBlank(keywords)) {
            sql = sql + " AND ( t1.NAME LIKE '%" + keywords + "%' OR p1.NAME LIKE '%" + keywords + "%' )";
        }
        String frame = request.getParameter("frame");
        if (StringUtils.isNotBlank(frame)) {
            String[] split = frame.split(",");
            sql = sql + " AND t1.submit_time BETWEEN '" + split[1] + "' AND '" + split[2] + "'";
        }
        return sql;
    }

    public List<TaskInstance> initNotRunningTaskInstance(String processDefinitionId, List<TaskInstance> taskInstanceList, String keywords, String productId) {
        List<TaskInstance> result = new ArrayList<>();
        if (null != taskInstanceList) {
            result = taskInstanceList;
        }
        List<TaskInstance> finalResult = result;
        //每次执行应该产生多少条任务实例，如果少，则补全为未运行
        IQueryWrapper<ProcessTaskRelation> processTaskRelationIQueryWrapper = getService(ProcessTaskRelation.class).getQuery()
                .eq("processDefinitionCode", processDefinitionId)
                .groupMax("postTaskCode").withs("taskName", "taskProductId")
                .filters("postTaskCode", "taskName");
        List<ProcessTaskRelation> processTaskRelations;
        //产品搜索
        if (StringUtils.isNotBlank(productId)) {
            processTaskRelationIQueryWrapper.eq("taskProductId", productId);
        }
        //输入框搜索
        if (StringUtils.isNotBlank(keywords)) {
            processTaskRelationIQueryWrapper.searchby("taskName", keywords);
        }
        processTaskRelations = processTaskRelationIQueryWrapper.list();
        if (null != processTaskRelations && !processTaskRelations.isEmpty()) {
            //算出哪个任务没运行，补全数据
            processTaskRelations.forEach(processTaskRelation -> {
                String taskId = processTaskRelation.getPostTaskCode();
                Optional<TaskInstance> taskInstanceOptional = finalResult.stream().filter(t -> taskId.equals(String.valueOf(t.getTaskId()))).findAny();
                if (!taskInstanceOptional.isPresent()) {
                    TaskInstance taskInstance = new TaskInstance();
                    taskInstance.setTaskId(Long.valueOf(processTaskRelation.getPostTaskCode()));
                    taskInstance.setName(processTaskRelation.getTaskName());
                    taskInstance.setState(TaskExecutionStatus.NOTRUNNING.name());
                    finalResult.add(taskInstance);
                } else {
                    taskInstanceOptional.get().setName(processTaskRelation.getTaskName());
                }
            });
        }
        return finalResult;
    }

    // 分页方法
    public static List<TaskInstance> getPageData(List<TaskInstance> data, int currentPage, int pageSize) {
        if (null == data || data.isEmpty()) {
            return data;
        }
        int fromIndex = currentPage * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, data.size());
        return data.subList(fromIndex, toIndex);
    }

    /**
     * 根据 duration 字段排序
     *
     * @param pageData   待排序的集合
     * @param sortbyType 是否正序排序（asc：正序，desc：倒序）
     * @return 排序后的集合
     */
    public static List<TaskInstance> sortByDuration(List<TaskInstance> pageData, String sortbyField, String sortbyType) {
        Comparator<TaskInstance> comparator = Comparator.comparing(
                task -> {
                    try {
                        if (StringUtils.isNotBlank(sortbyField)) {
                            switch (sortbyField) {
                                case "submitTime":
                                    return task.getSubmitTime().getTime();
                                case "startTime":
                                    return task.getStartTime().getTime();
                                case "endTime":
                                    return task.getEndTime().getTime();
                                case "duration":
                                    return Math.abs(task.getStartTime().getTime() - task.getEndTime().getTime());
                            }
                        }
                        return 0L;
                    } catch (NumberFormatException | NullPointerException e) {
                        return 0L;
                    }
                }
        );

        // 根据 sortbyType 参数决定正序或倒序
        if ("desc".equals(sortbyType)) {
            comparator = comparator.reversed();
        }

        // 排序
        pageData.sort(comparator);
        return pageData;
    }

    public List<TaskInstance> getEndTaskInstances(String processInstanceId) {
        List<TaskInstance> taskInstanceList = this.getQuery().eq("processInstanceId", processInstanceId).filters("id", "taskId", "taskType", "name", "startTime", "runTimes").list();
        List<TaskInstance> result = new ArrayList<>();
        if (null != taskInstanceList && !taskInstanceList.isEmpty()) {
            // 按 taskId 分组，取每组中 startTime 最大的对象
            result = new ArrayList<>(taskInstanceList.stream()
                    .collect(Collectors.toMap(
                            TaskInstance::getTaskId,
                            Function.identity(),
                            (oldTask, newTask) -> {
                                Date oldTime = oldTask.getStartTime();
                                Date newTime = newTask.getStartTime();
                                if (oldTime == null && newTime == null) return oldTask;  // 都为 null 时保留旧值
                                if (oldTime == null) return newTask;
                                if (newTime == null) return oldTask;
                                return newTime.getTime() > oldTime.getTime() ? newTask : oldTask;
                            }
                    ))
                    .values());
        }
        return result;
    }

    public void forceTaskSuccess(String taskInstanceId, TaskInstance taskInstance, String projectId) {
        ForceSuccessRecord forceSuccessRecord = new ForceSuccessRecord();
        forceSuccessRecord.setType(ForceSuccessRecord.Type.taskInstance);
        forceSuccessRecord.setProjectId(projectId);
        forceSuccessRecord.setTaskId(taskInstance.getTaskId().toString());
        forceSuccessRecord.setTaskInstanceId(taskInstanceId);
        forceSuccessRecord.setSourceState(TaskExecutionStatus.valueOf(taskInstance.getState()).getCode());
        forceSuccessRecord.setTargetState(TaskExecutionStatus.SUCCESS.getCode());
        String processDefinitionId = getService(ProcessInstance.class).getQuery().eq("id", taskInstance.getProcessInstanceId()).oneValue("processDefinitionId", String.class);
        forceSuccessRecord.setProcessInstanceId(taskInstance.getProcessInstanceId());
        forceSuccessRecord.setProcessDefinitionId(processDefinitionId);
//        if (response.getCode() != 0) {
//            forceSuccessRecord.setErrorMessage(response.getMessage());
        //修改scc库中数据的状态
//            updateSccTaskInstanceState(taskInstanceId, TaskExecutionStatus.SUCCESS.getCode());
        //修改海豚ds库中数据的状态
        updateDsTaskInstanceState(taskInstanceId, TaskExecutionStatus.SUCCESS.getCode());
//        }
        getService(ForceSuccessRecord.class).add(forceSuccessRecord);
    }

    private void updateDsTaskInstanceState(String taskInstanceId, int ordinal) {
        this.getSqlExecutor().excuteUpdate(String.format("update `business_ds`.`t_ds_task_instance` set state=%s where id=%s", ordinal, taskInstanceId));
    }

    private void updateSccTaskInstanceState(String taskInstanceId, int ordinal) {
        this.getSqlExecutor().excuteUpdate(String.format("update `business_scc`.`scc_task_instance` set state=%s where id=%s", ordinal, taskInstanceId));

    }


    public Response<?> getTaskCountAndState(String startTime, String endTime, String projectIdCondition) {
        Map<String, Integer> result = new HashMap<>();
        if (projectIdCondition.startsWith("notIn")) {
            projectIdCondition = "not in " + projectIdCondition.substring(5);
        }
        String sql = "SELECT\n" +
                "    'taskTotal' AS state_name,\n" +
                "    COUNT(DISTINCT sptr.post_task_code) AS state_count\n" +
                "FROM\n" +
                "    scc_process_definition spd\n" +
                "    INNER JOIN scc_process_task_relation sptr \n" +
                "      ON spd.id = sptr.process_definition_code \n" +
                "WHERE\n" +
                "    spd.release_state = '上线'\n" +
                "    AND spd.project_id %s\n" +
                "UNION ALL\n" +
                "SELECT\n" +
                "    CASE latest_sti.state \n" +
                "        WHEN 1 THEN 'running'\n" +
                "        WHEN 6 THEN 'failure'\n" +
                "        WHEN 7 THEN 'success'\n" +
                "        ELSE 'otherState' \n" +
                "    END AS state_name,\n" +
                "    COUNT(*) AS state_count \n" +
                "FROM\n" +
                "    (\n" +
                "      SELECT \n" +
                "          sptr.post_task_code,\n" +
                "          sti.state,\n" +
                "          -- 为每个 task_id 的最新记录标记行号\n" +
                "          ROW_NUMBER() OVER (\n" +
                "              PARTITION BY sti.task_id \n" +
                "              ORDER BY sti.submit_time DESC\n" +
                "          ) AS rn\n" +
                "      FROM \n" +
                "          scc_process_definition spd \n" +
                "          INNER JOIN scc_process_task_relation sptr \n" +
                "            ON spd.id = sptr.process_definition_code \n" +
                "          INNER JOIN scc_task_instance sti \n" +
                "            ON sptr.post_task_code = sti.task_id \n" +
                "      WHERE \n" +
                "          spd.release_state = '上线'\n" +
                "          AND spd.project_id %s\n" +
                "          AND sti.submit_time >= '%s'\n" +
                "          AND sti.submit_time <= '%s'\n" +
                "    ) AS latest_sti\n" +
                "-- 只保留每个 task_id 的最新记录（rn=1）\n" +
                "WHERE latest_sti.rn = 1\n" +
                "-- 直接按 state 分组统计\n" +
                "GROUP BY latest_sti.state;";
        List<JSONObject> list = this.getSqlExecutor().excuteSelect(String.format(sql, projectIdCondition, projectIdCondition, startTime, endTime)).list(JSONObject.class);
        int taskCount = 0;
        int executedTaskCount = 0;
        result.put("taskTotal", 0);//上线工作流下的所有子任务（包括未运行的）
        result.put("running", 0);//运行中
        result.put("failure", 0);//失败
        result.put("success", 0);//成功
        result.put("otherState", 0);//其他状态
        result.put("notRunTaskCount", 0);//未运行（taskTotal减去已运行）
        result.put("executedTaskCount", 0);//已运行
        if (list != null && !list.isEmpty()) {
            for (JSONObject jsonObject : list) {
                String stateName = jsonObject.getString("stateName");
                Integer stateCount = jsonObject.getInteger("stateCount");
                // 防止空值导致的空指针异常
                if (stateCount == null) {
                    stateCount = 0;
                }
                //任务总数
                if ("taskTotal".equals(stateName)) {
                    taskCount = stateCount;
                } else {
                    //其他状态
                    executedTaskCount += stateCount;
                }
                //put正在执行、成功、失败
                result.put(stateName, stateCount);
            }
            // 计算未运行任务数
            int notRunTaskCount = taskCount - executedTaskCount;
            result.put("notRunTaskCount", Math.max(0, notRunTaskCount)); // 确保不会出现负数
            result.put("executedTaskCount", executedTaskCount);
        }
        return ResponseFactory.makeSuccess(result);
    }

}
