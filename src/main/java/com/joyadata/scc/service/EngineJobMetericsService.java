package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.interfaces.ISqlExecutor;
import com.joyadata.scc.enums.TaskExecutionStatus;
import com.joyadata.scc.model.EngineJobMetrics;
import com.joyadata.scc.model.Task;
import com.joyadata.scc.model.TaskInstance;
import com.joyadata.scc.util.EngineUtils;
import com.joyadata.service.BaseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Service
public class EngineJobMetericsService extends BaseService<EngineJobMetrics> {

    @Autowired
    private TaskService taskService;

    public List<EngineJobMetrics> getByInstanceIds(String processInstanceId, String processDefinitionId) {
        List<EngineJobMetrics> result = new ArrayList<>();
        //为了返回任务id
        ISqlExecutor sqlExecutor = this.getSqlExecutor();
        //2024-12-13 因为scc_task_instance视图太慢，直接查海豚原生表
        String taskInstanceSql = "select id,task_code AS task_id, state from business_ds.t_ds_task_instance where process_instance_id ='" + processInstanceId + "'";
        List<TaskInstance> taskInstanceList = sqlExecutor.excuteSelect(taskInstanceSql).list(TaskInstance.class);

        //查询工作流下有多少个任务，如果未运行的给初始化值
        List<Task> tasks = taskService.setIgnoreTenantCode().setIgnorePermissions().getQuery().eq("processDefinitionCode", processDefinitionId).list();
        Map<String, Integer> taskPipelines = new HashMap<>();
        tasks.forEach(t -> {
            int sourceCount = 0;
            if ("STX".equals(t.getTaskType())) {
                sourceCount = EngineUtils.getSourceSinkCount(t.getTaskParams()).getV1();
            }
            taskPipelines.put(t.getId(), sourceCount);
        });

        // 计算总和
        int pipelines = 0;
        for (Integer value : taskPipelines.values()) {
            pipelines += value;
        }

        if (null == taskInstanceList || taskInstanceList.size() == 0) {
            //所有任务未执行 给初始化结果
            initEngineJobMetrics(taskInstanceList, taskPipelines, result);
            return result;
        }

        String engineJobMetricsSql = "SELECT e1.* FROM scc_engine_job_metrics e1" +
                " INNER JOIN ( SELECT pipeline_id, instance_id, MAX( create_time ) AS mx_create_time FROM scc_engine_job_metrics " +
                " WHERE instance_id IN ('%s') GROUP BY pipeline_id,instance_id ) e2 ON e1.instance_id = e2" +
                ".instance_id AND e1.pipeline_id = e2.pipeline_id AND e1.create_time = e2.mx_create_time";
        List<EngineJobMetrics> engineJobMetricsList = sqlExecutor.excuteSelect(String.format(engineJobMetricsSql, StringUtils.join(taskInstanceList.stream().map(TaskInstance::getId).collect(Collectors.toList()), "','"))).list(EngineJobMetrics.class);
        if (null != engineJobMetricsList && engineJobMetricsList.size() > 0) {
            result = engineJobMetricsList;
        }
        if (result.size() > 0) {
            Map<String, Long> taskInstanceMap = taskInstanceList.stream().collect(Collectors.toMap(TaskInstance::getId, TaskInstance::getTaskId));
            result.forEach(t -> t.setTaskId(taskInstanceMap.get(t.getInstanceId()).toString()));

        }
        if (result.size() != pipelines) {
            initEngineJobMetrics(taskInstanceList, taskPipelines, result);
        }
        return result;
    }

    //所有任务未执行 给初始化结果
    public void initEngineJobMetrics(List<TaskInstance> taskInstanceList, Map<String, Integer> taskPipelines, List<EngineJobMetrics> result) {
        for (String taskId : taskPipelines.keySet()) {
            //该任务应该有多少通道
            Integer taskPipeline = taskPipelines.get(taskId);
            //用来判断是否执行过
            Optional<TaskInstance> taskInstanceOptional = taskInstanceList.stream().filter(t -> String.valueOf(t.getTaskId()).equals(taskId)).findAny();
            //用来判断一个任务下通道是否都开始执行
            Optional<EngineJobMetrics> engineJobMetricsOptional = result.stream().filter(t -> t.getTaskId().equals(taskId)).findAny();
            if (engineJobMetricsOptional.isPresent()) {
                List<EngineJobMetrics> taskEngineJobMetrics = result.stream().filter(t -> t.getTaskId().equals(taskId)).collect(Collectors.toList());
                //任务开始执行，但是通道未执行完
                if (taskEngineJobMetrics.size() != taskPipelines.get(taskId)) {
                    //运行结果不等于应该的通道数量，给初始化值
                    //当前已经执行的最大通道 + 1 给初始化未执行
                    int maxPipeline = taskEngineJobMetrics.stream().mapToInt(EngineJobMetrics::getPipelineId).max().getAsInt();
                    for (int i = maxPipeline + 1; i <= taskPipeline; i++) {
                        result.add(new EngineJobMetrics(taskEngineJobMetrics.get(0).getInstanceId(), i, "NOTRUNNING", null, null, null, taskId));
                    }
                }
            } else if (taskInstanceOptional.isPresent()) {
                TaskInstance taskInstance = taskInstanceOptional.get();
                //执行过了，但是没有EngineJobMetrics数据 说明都失败了
                String state = taskInstance.getState();
                if (TaskExecutionStatus.SUBMITTED_SUCCESS.equals(TaskExecutionStatus.of(Integer.parseInt(state))) || TaskExecutionStatus.DELAY_EXECUTION.equals(TaskExecutionStatus.of(Integer.parseInt(state)))) {
                    //没有运行实例 未运行
                    for (int i = 1; i <= taskPipeline; i++) {
                        result.add(new EngineJobMetrics(taskInstance.getId(), i, "NOTRUNNING", null, null, null, taskId));
                    }
                }
                if (TaskExecutionStatus.FAILURE.equals(TaskExecutionStatus.of(Integer.parseInt(state)))) {
                    for (int i = 1; i <= taskPipeline; i++) {
                        result.add(new EngineJobMetrics(taskInstance.getId(), i, "NOTRUNNING", taskInstance.getStartTime(), taskInstance.getSubmitTime(), taskInstance.getEndTime(), taskId));
                    }
                }
                if (TaskExecutionStatus.PAUSE.equals(TaskExecutionStatus.of(Integer.parseInt(state))) || TaskExecutionStatus.KILL.equals(TaskExecutionStatus.of(Integer.parseInt(state)))) {
                    for (int i = 1; i <= taskPipeline; i++) {
                        result.add(new EngineJobMetrics(taskInstance.getId(), i, "CANCELED", taskInstance.getStartTime(), taskInstance.getSubmitTime(), taskInstance.getEndTime(), taskId));
                    }
                }
            } else {
                //没有运行实例 未运行
                for (int i = 1; i <= taskPipeline; i++) {
                    result.add(new EngineJobMetrics(taskId, i, "NOTRUNNING", null, null, null, taskId));
                }
            }
        }
    }

    /**
     * 根据状态RUNNING-->NOTRUNNING-->FINISHED-->FAILED顺序排列
     *
     * @param engineJobMetricsList
     */
    public List<EngineJobMetrics> sortByStatus(List<EngineJobMetrics> engineJobMetricsList) {
        if (null != engineJobMetricsList && !engineJobMetricsList.isEmpty()) {
            // 自定义排序规则
            Comparator<EngineJobMetrics> statusComparator = new Comparator<EngineJobMetrics>() {
                @Override
                public int compare(EngineJobMetrics o1, EngineJobMetrics o2) {
                    // 定义优先级顺序
                    int priority1 = getPriority(o1.getStatus());
                    int priority2 = getPriority(o2.getStatus());
                    return Integer.compare(priority1, priority2);
                }

                private int getPriority(String status) {
                    switch (status) {
                        case "FINISHED":
                            return 1;
                        case "FAILED":
                            return 2;
                        case "RUNNING":
                            return 3;
                        case "NOTRUNNING":
                            return 4;
                        default:
                            return 5; // 如果有其他状态，可以放在最后
                    }
                }
            };
            // 排序
            engineJobMetricsList.sort(statusComparator);
        }
        return engineJobMetricsList;
    }

    public void initTotalCost(List<EngineJobMetrics> result) {
        if (null != result && !result.isEmpty()) {
            result.forEach(t -> {
                long totalCost = t.getTotalCost();
                if (totalCost < 0 && !"NOTRUNNING".equals(t.getStatus())) {
                    t.setTotalCost(1L);
                }
            });
        }
    }

//    //计算读取/写入速率（行/s）
//    public void initRowSpeed(List<EngineJobMetrics> list) {
//        if (null != list && list.size() > 0) {
//            list.forEach(t -> {
//                long readRowCount = t.getReadRowCount();
//                long writeRowCount = t.getWriteRowCount();
//                long totalCost = t.getTotalCost();
//                //计算读取/写入速率（行/s）
//                if (totalCost == 0) {
//                    t.setReadRowsSpeed(t.getReadQps());
//                    t.setWriteRowsSpeed(t.getWriteQps());
//                } else {
//                    t.setReadRowsSpeed(readRowCount / totalCost);
//                    t.setWriteRowsSpeed(writeRowCount / totalCost);
//                }
//            });
//        }
//    }


    public void figureSyncProgress(List<EngineJobMetrics> engineJobMetricsList) {
        if (null != engineJobMetricsList && !engineJobMetricsList.isEmpty()) {
            engineJobMetricsList.forEach(engineJobMetrics -> {
                Long sourceCount = engineJobMetrics.getSourceCount();
                if (null != sourceCount) {
                    if (sourceCount > 0) {
                        long readRowCount = engineJobMetrics.getReadRowCount();
                        BigDecimal numerator = new BigDecimal(readRowCount);
                        BigDecimal denominator = new BigDecimal(sourceCount);
                        engineJobMetrics.setSyncProgress(numerator.divide(denominator, 2, RoundingMode.HALF_UP));
                    }else if (sourceCount == 0){
                        //如果源端总数是0，同步进度直接返回1
                        engineJobMetrics.setSyncProgress(BigDecimal.ONE);
                    }
                }
            });
        }
    }
}
