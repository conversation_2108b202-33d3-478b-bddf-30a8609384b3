package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.dto.BaseDataSourceParamDTO;
import com.joyadata.scc.dto.DatasourceDTO;
import com.joyadata.scc.dto.DatasourceResponse;
import com.joyadata.scc.dto.ImportDatasourceDTO;
import com.joyadata.scc.model.dto.Datasource;
import com.joyadata.scc.util.JdbcUrlUtils;
import com.joyadata.scc.util.MyBase64;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.tms.model.Product;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DatasourceService
 * @date 2023/11/3
 */
@Slf4j
@Service
public class DatasourceService {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;
    private JoyaFeignService<Product> productJoyaFeignService = FeignFactory.make(Product.class);

    @Transactional
    public Response<?> datasourceImport(ImportDatasourceDTO importDatasourceDTO) {
        String projectId = importDatasourceDTO.getProjectId();
        String productId = importDatasourceDTO.getProductId();
        String url = ApplicationContextHelp.getAppGatewayHttp() + Constants.DATASOURCE_LIST + "?pageNum=-1";
        Map<String, Object> header = new HashMap<>();
        header.put("token", ThreadLocalUserUtil.getCurrentUserToken());
        JSONObject body = new JSONObject();
        body.put("datasourceInfoIdList", importDatasourceDTO.getDatasourceInfoIds());
        body.put("itemId", projectId);//项目id
        body.put("productId", productId);//产品id
        body.put("filterReference", true);//是否过滤当前产品或项目已引用数据源：false-不过滤，true-过滤（默认false，不过滤）
        body.put("status", 1);//正常状态
        body.put("useStatus", "1");//使用中
        body.put("dataType", importDatasourceDTO.getDataType());
        try {
            DatasourceResponse<JSONArray> datasourceResponse = httpRequestFeignService.post4url(url, body, header, DatasourceResponse.class, false);
            if (null != datasourceResponse && 200 == datasourceResponse.getCode()) {
                JSONArray data = datasourceResponse.getData();
                if (null != data && data.size() > 0) {
                    data.forEach(t -> {
                        DatasourceDTO datasourceDTO = JSONObject.parseObject(JSONObject.toJSONString(t), DatasourceDTO.class);
                        //添加海豚
                        createDolpDatasource(new Datasource(datasourceDTO.getDatasourceInfoId(),
                                datasourceDTO.getDataName() + "_" + projectId,
                                datasourceDTO.getDataType(),
                                datasourceDTO.getDbName(),
                                datasourceDTO.getDataJson(),
                                datasourceDTO.getDataDesc()));
                    });
                }
            }
            return ResponseFactory.makeSuccess("数据源引入成功！");
        } catch (
                Exception e) {
            return ResponseFactory.makeError("引入数据源失败，报错信息=" + e.getMessage());
        }

    }

    //添加海豚数据源
    public void createDolpDatasource(Datasource datasource) {
        String url = dolphinscheduler + Constants.DATASOURCE;
        Map<String, Object> header = Utils.getHeader();

        BaseDataSourceParamDTO body = new BaseDataSourceParamDTO();
        body.setId(datasource.getId());
        body.setName(datasource.getDataName());
        body.setNote(datasource.getDataDesc());
        //解密dataJson 获取ip port 传海豚
        JSONObject json = JSONObject.parseObject(MyBase64.decoder(datasource.getDataJson()));
        String jdbcUrl = json.getString("jdbcUrl");
        body.setHost(JdbcUrlUtils.getIp(jdbcUrl));
        body.setPort(JdbcUrlUtils.getPort(jdbcUrl));
        body.setUserName(json.getString("username"));
        body.setPassword(json.getString("password"));
        if (datasource.getDataType().equalsIgnoreCase("oracle")) {
            //2024-09-06 jdbc截取service写法，SID写法库名
            if (jdbcUrl.contains("/")) {
                String serviceName = jdbcUrl.substring(jdbcUrl.lastIndexOf("/") + 1);
                body.setDatabase(serviceName);
            } else {
                String dbName = jdbcUrl.substring(jdbcUrl.lastIndexOf(":") + 1);
                body.setDatabase(dbName);
            }
        } else {
            body.setDatabase(datasource.getDbName());
        }
        body.setType("MYSQL");

        Map saveMap = httpRequestFeignService.post4url(url, body, header, Map.class);
        if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
            log.error("数据源引入失败 {}", saveMap);
            throw new AppErrorException("数据源引入失败={" + saveMap.get("msg") + "}");
        }
    }

    @Transactional
    public Response<?> cancelDatasourceImport(ImportDatasourceDTO importDatasourceDTO) {
//        String projectId = importDatasourceDTO.getProjectId();
//        String productId = importDatasourceDTO.getProductId();
        //海豚header
        Map<String, Object> dolphinschedulerHeader = Utils.getHeader();
        List<String> datasourceInfoIdList = importDatasourceDTO.getDatasourceInfoIds();
        if (null != datasourceInfoIdList && datasourceInfoIdList.size() > 0) {
//            List<JSONObject> deleteDatasourceBody = new ArrayList<>();
            datasourceInfoIdList.forEach(id -> {
                //删除海豚数据源接口
                String delUrl = dolphinscheduler + Constants.DATASOURCE + "/" + id;
                Map delMap = httpRequestFeignService.delete4url(delUrl, new JSONObject(), dolphinschedulerHeader, Map.class);
                if (Integer.parseInt(delMap.get("code").toString()) != 0) {
                    log.error("数据源取消引入失败 {}", delMap);
                    throw new AppErrorException("数据源取消引入失败！");
                }
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("datasourceInfoId", id);
//                jsonObject.put("itemId", projectId);
//                jsonObject.put("productId", productId);
//                deleteDatasourceBody.add(jsonObject);
            });
            //调取消引入接口
//            String cancelUrl = ApplicationContextHelp.getAppGatewayHttp() + Constants.DELETE_DATASOURCE;
//            Map<String, Object> header = new HashMap<>();
//            header.put("token", ThreadLocalUserUtil.getCurrentUserToken());
//            DatasourceResponse datasourceResponse = httpRequestFeignService.post4url(cancelUrl, deleteDatasourceBody, header, DatasourceResponse.class, false);
//            if (datasourceResponse.getCode() != 200) {
//                return ResponseFactory.makeError("取消引入数据源失败，报错信息=" + datasourceResponse.getMsg());
//            }
        }
        return ResponseFactory.makeSuccess("数据源取消引入成功！");
    }
}
