package com.joyadata.scc.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.scc.enums.CatalogName;
import com.joyadata.scc.model.Catalog;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionCatalog;
import com.joyadata.service.BaseService;
import com.joyadata.tms.model.Product;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import groovy.lang.Tuple2;
import groovy.lang.Tuple4;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProcessDefinitionCatalogService extends BaseService<ProcessDefinitionCatalog> {

    private JoyaFeignService<Product> productJoyaFeignService = FeignFactory.make(Product.class);

    public String initDefaultCatalog(String productId, String projectId) {
        //初始化默认目录
        ProcessDefinitionCatalog defaultCatalog = this.getQuery().eq("name", CatalogName.DEFAULT.getName()).eq("projectId", projectId).eq("level", 0).one();
        if (null == defaultCatalog) {
            ProcessDefinitionCatalog catalog = new ProcessDefinitionCatalog();
            catalog.setName(CatalogName.DEFAULT.getName());
            catalog.setProductId(productId);
            catalog.setProjectId(projectId);
            catalog.setType(CatalogName.DEFAULT.getType());
            catalog.setIsPublic(true);
            return this.add(catalog).getId();
        }
        return defaultCatalog.getId();
    }

    public Tuple4<String, String, String, String> initCustomCatalog(String productId, String projectId) {
        //初始化默认目录
        ProcessDefinitionCatalog customCatalog = this.getQuery().eq("name", CatalogName.CUSTOM.getName()).eq("level", 0).eq("projectId", projectId).one();
        if (null == customCatalog) {
            ProcessDefinitionCatalog catalog = new ProcessDefinitionCatalog();
            catalog.setName(CatalogName.CUSTOM.getName());
            catalog.setProductId(productId);
            catalog.setProjectId(projectId);
            catalog.setType(CatalogName.CUSTOM.getType());
            catalog.setIsPublic(true);
            customCatalog = this.add(catalog);
        }
        //创建defult目录
        ProcessDefinitionCatalog orCreateDefaultCatalog = findOrCreateDefaultCatalog(productId, projectId, customCatalog.getId(), 1, CatalogName.CUSTOM.getType());
        return new Tuple4<>(orCreateDefaultCatalog.getId(), orCreateDefaultCatalog.getParentIds(), customCatalog.getId(), customCatalog.getParentIds());
    }

    /**
     * 初始化默认目录 ID
     *
     * @param productId    产品 ID
     * @param projectId    项目 ID
     * @param catalogNames 目录名称列表
     * @return Tuple2<目录 ID, 父目录 ID>
     */
    public Tuple2<String, String> initDefaultCatalogId(String productId, String projectId, List<String> catalogNames) {
        // 初始化默认目录
        String defaultCatalogId = initDefaultCatalog(productId, projectId);

        // 初始化产品目录
        String productCatalogId = initProductCatalog(productId, projectId, defaultCatalogId);

        // 初始化目标目录
        if (CollectionUtils.isNotEmpty(catalogNames)) {
            // 放入指定目录
            ProcessDefinitionCatalog catalog = findOrCreateCatalog(productId, projectId, catalogNames, productCatalogId, CatalogName.DEFAULT.getType());
            return new Tuple2<>(catalog.getId(), catalog.getParentIds());
        } else {
            // 放入默认目录
            ProcessDefinitionCatalog defaultCatalog = findOrCreateDefaultCatalog(productId, projectId, productCatalogId, 2, CatalogName.DEFAULT.getType());
            return new Tuple2<>(defaultCatalog.getId(), defaultCatalog.getParentIds());
        }
    }

    /**
     * 初始化自定义目录 ID
     *
     * @param productId    产品 ID
     * @param projectId    项目 ID
     * @param catalogNames 目录名称列表
     * @return Tuple2<目录 ID, 父目录 ID>
     */
    public Tuple2<String, String> initCustomCatalogId(String productId, String projectId, List<String> catalogNames) {
        // 初始化默认目录
        Tuple4<String, String, String, String> defaultCatalogId = initCustomCatalog(productId, projectId);

        // 初始化目标目录,导出时有可能在default目录
        if (CollectionUtils.isNotEmpty(catalogNames) && !CatalogName.CUSTOM.getName().equals(catalogNames.get(0))) {
            // 放入指定目录
            ProcessDefinitionCatalog catalog = findOrCreateCatalog(productId, projectId, catalogNames, defaultCatalogId.getV3(), CatalogName.CUSTOM.getType());
            return new Tuple2<>(catalog.getId(), catalog.getParentIds());
        } else {
            // 放入默认目录
            ProcessDefinitionCatalog defaultCatalog = findOrCreateDefaultCatalog(productId, projectId, defaultCatalogId.getV3(), 1, CatalogName.CUSTOM.getType());
            return new Tuple2<>(defaultCatalog.getId(), defaultCatalog.getParentIds());
        }
    }

    /**
     * 初始化产品目录
     */
    private String initProductCatalog(String productId, String projectId, String defaultCatalogId) {
        if (StringUtils.isBlank(productId)) {
            return StringUtils.EMPTY;
        }
        String productName = productJoyaFeignService.getQuery()
                .eq("id", productId)
                .oneValue("name", String.class);
        if (StringUtils.isBlank(productName)) {
            throw new RuntimeException("产品不存在！");
        }
        return Optional.ofNullable(this.getQuery()
                .eq("productId", productId)
                .eq("projectId", projectId)
                .eq("name", productName)
                .eq("level", 1)
                .one())
                .orElseGet(() -> createCatalog(productId, projectId, defaultCatalogId, productName, 1, CatalogName.DEFAULT.getType()))
                .getId();
    }

    /**
     * 查找或创建指定目录
     */
    private ProcessDefinitionCatalog findOrCreateCatalog(String productId, String projectId, List<String> catalogNames, String parentId, Integer type) {
        ProcessDefinitionCatalog processDefinitionCatalog = new ProcessDefinitionCatalog();
        for (int i = 0; i < catalogNames.size(); i++) {
            String catalogName = catalogNames.get(i);
            //level从坐标0开始，level=2(level=0(默认目录), level=1(产品目录))
            int level = i;
            if (Objects.equals(CatalogName.CUSTOM.getType(), type)) {
                level = i + 1;
            } else if (Objects.equals(CatalogName.DEFAULT.getType(), type)) {
                level = i + 2;
            }
            processDefinitionCatalog = this.getQuery().eq("productId", productId).eq("projectId", projectId).eq("pid", parentId).eq("name", catalogName).eq("level", level).one();
            if (null == processDefinitionCatalog) {
                //如果产品下没有这个目录，数据库添加一条目录数据
                processDefinitionCatalog = createCatalog(productId, projectId, parentId, catalogName, level, type);
            }
            parentId = processDefinitionCatalog.getId();
        }

        return processDefinitionCatalog;
    }

    /**
     * 查找或创建defult目录（产品目录defult在level=2,自定义目录defult在level=1）
     */
    private ProcessDefinitionCatalog findOrCreateDefaultCatalog(String productId, String projectId, String parentId, Integer level, Integer type) {
        IQueryWrapper<ProcessDefinitionCatalog> queryWrapper = this.getQuery()
                .eq("projectId", projectId)
                .eq("name", "default")
                .eq("level", level)
                .eq("type", type);
        //自定义的default目录不需要productId区分，只会有一个，默认目录需要productId区分
        if (CatalogName.DEFAULT.getType().equals(type)) {
            queryWrapper.eq("productId", productId);
        }
        return Optional.ofNullable(
                queryWrapper.one())
                .orElseGet(() -> createCatalog(productId, projectId, parentId, "default", level, type));
    }

    /**
     * 创建目录
     */
    private ProcessDefinitionCatalog createCatalog(String productId, String projectId, String parentId, String name, int level, Integer type) {
        ProcessDefinitionCatalog catalog = new ProcessDefinitionCatalog();
        catalog.setProjectId(projectId);
        catalog.setProductId(productId);
        catalog.setPid(parentId);
        catalog.setName(name);
        catalog.setType(type);
        catalog.setLevel(level);
        return this.add(catalog);
    }

    /**
     * 判断上级目录下是否有工作流，如果没有，删除，level
     *
     * @param id 目录id
     * @return
     */
    public int delCaseCatalog(String id) {
        int delCount = 0;
        if (StringUtils.isNotBlank(id)) {
            ProcessDefinitionCatalog processDefinitionCatalog = this.getById(id);
            if (null != processDefinitionCatalog && Objects.equals(CatalogName.DEFAULT.getType(), processDefinitionCatalog.getType())) {
                String parentIds = processDefinitionCatalog.getParentIds();
                String[] split = StringUtils.split(parentIds, ",");
                List<ProcessDefinitionCatalog> processDefinitionCatalogs = this.getQuery().in("id", split).ne("level", 0).list();
                if (null != processDefinitionCatalogs && !processDefinitionCatalogs.isEmpty()) {
                    for (ProcessDefinitionCatalog catalog : processDefinitionCatalogs) {
                        int processDefinitionTotal = getService(ProcessDefinition.class).getQuery().startsWith("catalogParentIds", catalog.getParentIds()).total();
                        if (0 == processDefinitionTotal) {
                            catalog.setDelChildren(true);
                            delCount += this.delete(catalog.getId(), catalog);
                        }
                    }
                }
            }
        }
        return delCount;
    }

    //清除目录下没有任务的目录
    public void cleanEmptyProcessDefinitionCatalog() {
        List<ProcessDefinitionCatalog> catalogList = this.getQuery().ignoreTenantCode().ignorePermissions().gt("level", 0).eq("processDefinitionCount", 0).withs("processDefinitionCount").list();
        if (null != catalogList && !catalogList.isEmpty()) {
            Map<String, List<ProcessDefinitionCatalog>> tenantCatalogList = catalogList.stream()
                    .collect(Collectors.groupingBy(ProcessDefinitionCatalog::getTenantCode));
            if (null != tenantCatalogList && !tenantCatalogList.isEmpty()) {
                for (String tenantCode : tenantCatalogList.keySet()) {
                    List<ProcessDefinitionCatalog> catalogs = tenantCatalogList.get(tenantCode);
                    if (null != catalogs && !catalogs.isEmpty()) {
                        String systemToken = AuthUtil.getSystemToken(null);
                        ThreadLocalUserUtil.setCode("token", systemToken);
                        //加上超管的租户code
                        ThreadLocalUserUtil.setCode("tenantCode", tenantCode);
                        //级联删除目录
                        catalogs.forEach(t -> this.delCaseCatalog(t.getId()));
                    }
                }
            }
        }
    }
}
