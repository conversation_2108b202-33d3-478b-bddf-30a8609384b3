package com.joyadata.scc.service;

import com.alibaba.fastjson.TypeReference;
import com.joyadata.cms.model.User;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessDefinitionExecTime;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.util.CronUtils;
import com.joyadata.service.BaseService;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Year;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProcessDefinitionExecTimeService extends BaseService<ProcessDefinitionExecTime> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;

    public void initProcessExecTimes(String calendarId, Integer calendarStatus) {
        if (ProcessDefinitionExecTime.CalendarStatus.isSubmit.equals(calendarStatus)) {
            //提交日历，计算该日历下工作流后30次执行时间

            //1、获取该日历下工作流
            List<String> processDefinitionIds = getService(ProcessDefinition.class).getQuery().eq("calendarId", calendarId).withs("calendarId").listValue("id", String.class);
            if (null != processDefinitionIds && !processDefinitionIds.isEmpty()) {
                List<ProcessDefinition> processDefinitions = getProcessDefinitionWiths(processDefinitionIds);
                if (null != processDefinitions && !processDefinitions.isEmpty()) {
                    //计算入库
                    calculate30Times(processDefinitions, ThreadLocalUserUtil.getUser(User.class).getTenantCode());
                }
            }
        } else if (ProcessDefinitionExecTime.CalendarStatus.isRevoke.equals(calendarStatus)) {
            //撤销日历，删除该日历下所有下次执行时间
            Integer delCount = this.deleteBy(new EqCondition("calendarId", calendarId));
            log.info("撤销日历删除下次执行时间数量 = {}", delCount);
        }
    }

    public void calculate30Times(List<ProcessDefinition> processDefinitions, String tenantCode) {
        if (CollectionUtils.isEmpty(processDefinitions)) {
            return;
        }

        Date now = new Date();
        processDefinitions.forEach(processDefinition -> {
            //计算接下来30次执行时间
            List<Date> execTimes = calculateExecutionTimes(processDefinition, now);
            if (!execTimes.isEmpty()) {
                //入库
                saveExecutionTimes(execTimes, processDefinition, tenantCode);
            }
        });
    }

    private List<Date> calculateExecutionTimes(ProcessDefinition processDefinition, Date now) {
        String cron = processDefinition.getCrontab();
        Date startTime = processDefinition.getScheduleStartTime();
        Date endTime = processDefinition.getScheduleEndTime();
        String scheduleTimezoneId = processDefinition.getScheduleTimezoneId();
        String calendarId = processDefinition.getCalendarId();

        if (StringUtils.isNotBlank(calendarId)) {
            //需要过滤日历计算
            return calculateWithCalendar(cron, startTime, endTime, scheduleTimezoneId, now, calendarId);
        } else {
            //直接计算
            return CronUtils.getNextExecTime(cron, 30);
        }
    }

    private List<Date> calculateWithCalendar(String cron, Date startTime, Date endTime,
                                             String scheduleTimezoneId, Date now, String calendarId) {
        //获取工作日
        String url = String.format("%s/dedp/v1/cms/calendar/%s/workdays?times=%d",
                ApplicationContextHelp.getAppGatewayHttp(),
                calendarId,
                Year.now().length());
        List<String> workDays = httpRequestFeignService.get4url(url,
                new TypeReference<List<String>>() {
                }, true);
        if (CollectionUtils.isEmpty(workDays)){
            return new ArrayList<>();
        }
        return CronUtils.filterWorkDays(cron, startTime, endTime, scheduleTimezoneId,
                now, calendarId, workDays, 30);
    }

    private void saveExecutionTimes(List<Date> execTimes, ProcessDefinition processDefinition, String tenantCode) {
        List<ProcessDefinitionExecTime> addList = execTimes.stream()
                .map(execTime -> createProcessDefinitionExecTime(execTime, processDefinition, tenantCode))
                .collect(Collectors.toList());

        this.add(addList);
    }

    private ProcessDefinitionExecTime createProcessDefinitionExecTime(Date execTime, ProcessDefinition processDefinition, String tenantCode) {
        ProcessDefinitionExecTime processDefinitionExecTime = new ProcessDefinitionExecTime();
        processDefinitionExecTime.setProcessDefinitionId(processDefinition.getId());
        processDefinitionExecTime.setExecTime(execTime);
        processDefinitionExecTime.setAddOrUpdate(true);
        processDefinitionExecTime.setCalendarId(processDefinition.getCalendarId());
        processDefinitionExecTime.setTenantCode(tenantCode);
        return processDefinitionExecTime;
    }

    public List<ProcessDefinition> getProcessDefinitionWiths(List<String> processDefinitionIds) {
        List<ProcessDefinition> result = new ArrayList<>();
        if (null != processDefinitionIds && !processDefinitionIds.isEmpty()) {
            result = getService(ProcessDefinition.class).setIgnoreTenantCode().setIgnorePermissions().getQuery()
                    .eq("scheduleStatus", Scheduler.ReleaseState.online)
                    .in("id", processDefinitionIds)
//                    .isNotNull("calendarId")
                    .withs("scheduleStatus", "crontab", "calendarId", "scheduleEndTime", "scheduleStartTime", "scheduleTimezoneId")
                    .filters("id", "tenantCode", "crontab", "calendarId", "scheduleEndTime", "scheduleStartTime", "scheduleTimezoneId").list();
        }
        return result;
    }
}
