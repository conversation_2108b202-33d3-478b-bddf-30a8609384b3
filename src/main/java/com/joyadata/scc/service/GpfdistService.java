package com.joyadata.scc.service;

import com.joyadata.exception.AppWarningException;
import com.joyadata.scc.model.Gpfdist;
import com.joyadata.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/14 19:15.
 */
@Service
public class GpfdistService extends BaseService<Gpfdist> {

    public boolean addCheck(String gpfdistPath, String projectId, Boolean isAdd) {
        List<Gpfdist> list = this.getQuery().eq("projectId", projectId).list();
        if (isAdd) {
            if (null != list && !list.isEmpty()) {
                if (!gpfdistPath.equals(list.get(0).getGpfdistPath())) {
                    return false;
                }
            }
        } else {
            if (null != list && list.size() > 1) {
                if (!gpfdistPath.equals(list.get(0).getGpfdistPath())) {
                    return false;
                }
            }
        }
        return true;
    }
}
