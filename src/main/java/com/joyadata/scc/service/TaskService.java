package com.joyadata.scc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.dto.TaskDTO;
import com.joyadata.scc.dto.TestTask;
import com.joyadata.scc.model.*;
import com.joyadata.scc.model.dto.Release;
import com.joyadata.scc.model.dto.StartParam;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import com.joyadata.util.GeneratorUtil;
import com.joyadata.util.JsonUtil;
import com.joyadata.util.TreeNodeUtil;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: TaskService
 * @date 2023/11/3
 */
@Slf4j
@Service
public class TaskService extends BaseService<Task> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private CatalogService catalogService;
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Transactional
    @Override
    public Task add(String id, Task bean) {
        //根据类型修改参数
        fixTaskParams(bean);
        String productId = bean.getProductId();
        String projectId = bean.getProjectId();
        Assert.notNull(productId, "产品id不能为空");
        Assert.notNull(projectId, "项目id不能为空");
        bean.setTimeout(0L);
        bean.setTimeoutFlag("CLOSE");
        //2025-03-10 初始化目录id
        if (null != bean.getIsImport() && bean.getIsImport()) {
            //说明是外部使用，需要获取目录id
            Tuple2<String, String> catalogTuple = catalogService.initDefaultCatalogId(productId, projectId, bean.getCatalogNames());
            bean.setCatalogId(catalogTuple.getV1());
        }

        //工作流添加已存任务，如果任务本身存在更新
        Task task = getById(id);
        if (null != task) {
            this.update(id, bean, true);
            return bean;
        }
        Task taskAdd = super.add(id, bean);
        //需要调海豚的任务新增接口
        List<TaskDTO> taskDTOList = new ArrayList<>();
        taskDTOList.add(new TaskDTO(bean));
        createDsTask(taskAdd.getProjectId(), taskDTOList);
//        addTaskLog(taskAdd, id);
        return taskAdd;
    }

    private void fixTaskParams(Task task) {
        String taskType = task.getTaskType();
        //海豚所需要的数据库类型全部是大写
        if ("SQL".equals(taskType)) {
            initTaskParams(task.getTaskParams());
//            if (null != bean.getIsImport() && bean.getIsImport()) {
//                JSONObject taskParams = bean.getTaskParams();
//                String datasource = taskParams.getString("datasource");
//                //调用引入数据源接口
//                ImportDatasourceDTO importDatasourceDTO = new ImportDatasourceDTO();
//                importDatasourceDTO.setDataType("project");
//                importDatasourceDTO.setProductId(bean.getProductId());
//                importDatasourceDTO.setProjectId(bean.getProjectId());
//                importDatasourceDTO.setDatasourceInfoIds(Collections.singletonList(datasource));
//                datasourceService.datasourceImport(importDatasourceDTO);
//            }
        } else if ("STX".equals(taskType) || "SEATUNNEL".equals(taskType)) {
            //引擎默认用：JOYADATA_ENGINE
            task.getTaskParams().put("engine", "JOYADATA_ENGINE");
        }
    }

    /**
     * @param id
     * @param bean
     * @param addTaskDolp 是否修改海豚，true，是;false。否
     * @return
     */
    @Transactional
    public Task addTask(String id, Task bean, Boolean addTaskDolp) {
        //根据类型修改参数
        fixTaskParams(bean);
        String projectId = bean.getProjectId();
        Assert.notNull(projectId, "项目id不能为空");
        bean.setTimeout(0L);
        bean.setTimeoutFlag("CLOSE");
        //工作流添加已存任务，如果任务本身存在更新
        Task task = getById(id);
        if (null != task) {
            this.update(id, bean, true);
            return bean;
        }
        //如果没传目录id，归类到默认目录下
        if (StringUtils.isBlank(bean.getCatalogId())) {
            Tuple2<String, String> catalog = catalogService.initDefaultCatalogId(bean.getProductId(), bean.getProjectId(), bean.getCatalogNames());
            bean.setCatalogId(catalog.getV1());
            bean.setCatalogParentIds(catalog.getV2());
        }
        Task taskAdd = super.add(id, bean);
        //需要调海豚的任务新增接口
        List<TaskDTO> taskDTOList = new ArrayList<>();
        taskDTOList.add(new TaskDTO(bean));
        if (addTaskDolp) {
            createDsTask(taskAdd.getProjectId(), taskDTOList);
        }

        return taskAdd;
    }


    /**
     * 新增海豚任务
     *
     * @param taskDTOList
     */
    private void createDsTask(String projectId, List<TaskDTO> taskDTOList) {
        String url = dolphinscheduler + Constants.TASK_DEFINITION.replace("{projectCode}",
                projectId);
        Map<String, Object> header = Utils.getHeader();
        Map body = new HashMap();
        body.put("taskDefinitionJson", JSONObject.toJSONString(taskDTOList));
        Map saveMap = Utils.webClientPost(url, body, header.get(Constants.SESSION_ID).toString());
        if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
            log.error("新增任务失败...");
            throw new AppErrorException("新增任务失败！");
        }
    }

    public void initTaskParams(JSONObject taskParams) {
        //海豚所需要的数据库类型全部是大写
        taskParams.put("type", taskParams.getString("type").toUpperCase());
    }

    @Transactional
    @Override
    public Integer update(String id, Task bean) {
        String projectId = bean.getProjectId();
        String productId = bean.getProductId();
        //根据类型修改参数
        fixTaskParams(bean);
        Task task = getQuery().eq("id", id).withs("catalogParentIds").one();
        //2025-03-10 初始化目录id
        if (null != bean.getIsImport() && bean.getIsImport()) {
            //说明是外部使用，需要获取目录id
            Tuple2<String, String> catalogTuple = catalogService.initDefaultCatalogId(productId, projectId, bean.getCatalogNames());
            bean.setCatalogId(catalogTuple.getV1());
        }
        //工作流修改时添加任务，如果任务本身不存在，先添加
        if (null == task) {
            this.add(bean);
            return 1;
        }
        //一公局开发中心修改下线任务重新推送任务没有直接修改到海豚，2024-10-30给放开代码
        if (StringUtils.isNotBlank(task.getProcessDefinitionCode())) {
            Integer total = getService(ProcessDefinition.class).getQuery().eq("id", task.getProcessDefinitionCode()).total();
            if (total > 0) {
                //修改海豚
                String url = dolphinscheduler + Constants.TASK_DEFINITION.replace("{projectCode}", task.getProjectId()) + "/" + task.getId();
                Map<String, Object> headers = Utils.getHeader();
                Map<String, String> params = new HashMap<>();
                params.put("updateVersion", "0");//任务单个编辑不升级，升级版本和工作流保持一一致
                params.put("taskDefinitionJsonObj", JSONObject.toJSONString(bean));
                Map saveMap = Utils.webClientPut(url, params, headers.get(Constants.SESSION_ID).toString());
                if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
                    throw new AppErrorException("修改任务失败！");
                }
            }
        }
        //添加日志表 用于版本控制
//        if (bean.getUpdateVersion() == 1) {
//            addTaskLog(bean, id);
//        }
        Integer update = super.update(id, bean);
        int delCatalogCount = catalogService.delCaseCatalog(task.getCatalogId());
        log.info("删除目录数量:{}", delCatalogCount);
        return update;
    }

    /**
     * @param id
     * @param bean
     * @param updateTaskDolp 是否修改海豚，true，是;false。否
     * @return
     */
    @Transactional
    public Integer updateTask(String id, Task bean, Boolean updateTaskDolp) {
        //根据类型修改参数
        fixTaskParams(bean);
        Task task = getById(id);
        //如果没传目录id，归类到默认目录下
        if (StringUtils.isBlank(bean.getCatalogId())) {
            Tuple2<String, String> catalog = catalogService.initDefaultCatalogId(bean.getProductId(), bean.getProjectId(), bean.getCatalogNames());
            bean.setCatalogId(catalog.getV1());
            bean.setCatalogParentIds(catalog.getV2());
        }
        //工作流修改时添加任务，如果任务本身不存在，先添加
        if (null == task) {
            this.add(bean);
            return 1;
        }
        //一公局开发中心修改下线任务重新推送任务没有直接修改到海豚，2024-10-30给放开代码
        if (StringUtils.isNotBlank(task.getProcessDefinitionCode()) && updateTaskDolp) {
            Integer total = getService(ProcessDefinition.class).getQuery().eq("id", task.getProcessDefinitionCode()).total();
            if (total > 0) {
                //修改海豚
                String url = dolphinscheduler + Constants.TASK_DEFINITION.replace("{projectCode}", task.getProjectId()) + "/" + task.getId();
                Map<String, Object> headers = Utils.getHeader();
                Map<String, String> params = new HashMap<>();
                params.put("updateVersion", "0");//任务单个编辑不升级，升级版本和工作流保持一一致
                params.put("taskDefinitionJsonObj", JSONObject.toJSONString(bean));
                Map saveMap = Utils.webClientPut(url, params, headers.get(Constants.SESSION_ID).toString());
                if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
                    throw new AppErrorException("修改任务失败！");
                }
            }
        }
        return super.update(id, bean);
    }

//    /**
//     * 工作流编辑、切换工作流版本  不用再调用海豚接口
//     *
//     * @param id
//     * @param bean
//     * @param initFlag
//     * @return
//     */
//    @Override
//    public Integer update(String id, Task bean, boolean initFlag) {
//        Task task = getById(id);
//        //工作流修改时添加任务，如果任务本身不存在，先添加
//        if (null == task) {
//            this.add(id, bean);
//            return 1;
//        }
//        //添加日志表 用于版本控制
//        if (bean.getUpdateVersion() == 1) {
//            addTaskLog(bean, id);
//        } else {
//            //对应的版本也要更新
//            updateTaskLog(bean, id);
//        }
//        return super.update(id, bean, initFlag);
//    }
//
//    private void updateTaskLog(Task task, String id) {
//        List<WhereCondition> conditionList = new ArrayList<>();
//        conditionList.add(new EqCondition("taskId", id));
//        conditionList.add(new EqCondition("version", task.getVersion()));
//
//        TaskLog taskLog = new TaskLog();
//        BeanUtils.copyProperties(task, taskLog);
//        taskLog.setUpdateNull(true);
//        taskLogService.updateBy(conditionList, taskLog);
//    }
//
//    //添加任务日志表 用于版本控制
//    private void addTaskLog(Task task, String id) {
//        TaskLog taskLog = new TaskLog();
//        BeanUtils.copyProperties(task, taskLog);
//        taskLog.setTaskId(id);
//        taskLog.setId(null);
//        taskLogService.add(taskLog);
//    }

    @Transactional
    @Override
    public Integer delete(String id, Task bean) {
        Task task = this.getById(id);
        if (null == task) {
            return super.delete(id, bean);
        }
        if (null != task && StringUtils.isNotBlank(task.getProcessDefinitionCode())) {
            //先删除海豚任务
            String url = dolphinscheduler + Constants.TASK_DEFINITION.replace("{projectCode}",
                    task.getProjectId()) + "/" + task.getId();
            Map<String, Object> header = Utils.getHeader();
            Map map = httpRequestFeignService.delete4url(url, null, header, Map.class);
            if (Integer.parseInt(map.get("code").toString()) != 0
                    && !map.get("msg").toString().contains("does not exist")) {//忽略海豚报任务不存在的错误
                log.error("删除任务失败{}", map);
                throw new AppErrorException("删除任务失败！");
            }
        }
        Integer delete = super.delete(id, bean);

        new Thread(() -> {
            int delCatalogCount = catalogService.delCaseCatalog(task.getCatalogId());
            log.info("删除目录数量:{}", delCatalogCount);
            //删除taskLog表
//        taskLogService.deleteBy(new EqCondition("taskId", id));
            //删除检查记录表
            Integer delCheckTaskRecordCount = getService(CheckTaskRecord.class).deleteBy(new EqCondition("taskId", id));
            log.info("删除检查记录数量:{}", delCheckTaskRecordCount);
        }).start();

        return delete;
    }

    @Override
    public List<Task> getList(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        //赋值最新执行状态
        String[] withs = queryFilter.getWiths();
        boolean hasExecState = false;
        boolean hasStartTime = false;
        if (null != withs) {
            //with join处理效率慢 execState,startTime
            HashSet<String> hashSet = new HashSet(Arrays.asList(withs));
            if (hashSet.contains("exec_state")) {
                hashSet.remove("exec_state");
                hasExecState = true;
            }
            if (hashSet.contains("start_time")) {
                hashSet.remove("start_time");
                hasStartTime = true;
            }
            queryFilter.setWiths(hashSet.toArray(new String[]{}));
        }
        List<Task> list = this.getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter).page(page, pager).list();
        if (null != list && !list.isEmpty()) {
            if (hasExecState || hasStartTime) {
                setExecState(list, hasExecState, hasStartTime);
            }
        }
        return list;
    }

    private void setExecState(List<Task> list, boolean hasExecState, boolean hasStartTime) {
        Set<String> taskIds = list.stream().map(Task::getId).collect(Collectors.toSet());
        Map<String, TaskInstance> taskInstanceMap = getTaskInstanceMap(taskIds);
        list.forEach(t -> {
            TaskInstance taskInstance = taskInstanceMap.get(t.getId());
            if (null != taskInstance) {
                if (hasExecState) {
                    t.setExecState(taskInstance.getState());
                }
                if (hasStartTime) {
                    t.setStartTime(taskInstance.getStartTime());
                }
            }
        });
    }

    private Map<String, TaskInstance> getTaskInstanceMap(Set<String> taskIds) {
        Map<String, TaskInstance> result = new HashMap<>();
        String sql = "SELECT" +
                " t1.task_code AS task_id," +
                " any_value ( t1.state ) AS state," +
                " any_value ( t1.start_time ) AS start_time" +
                " FROM" +
                " business_ds.t_ds_task_instance t1" +
                " INNER JOIN ( SELECT task_code, MAX( start_time ) AS mx_start_time FROM business_ds.t_ds_task_instance WHERE task_code IN ( '%s' ) GROUP BY task_code ) t2 ON t1.task_code = t2.task_code" +
                " AND t1.start_time = t2.mx_start_time" +
                " GROUP BY" +
                " t1.task_code";
        List<TaskInstance> taskInstanceList = getSqlExecutor().excuteSelect(String.format(sql, StringUtils.join(taskIds, "','"))).list(TaskInstance.class);
        if (null != taskInstanceList && taskInstanceList.size() > 0) {
            taskInstanceList.forEach(TaskInstance::afterDbInit);
            result = taskInstanceList.stream().collect(Collectors.toMap(t -> String.valueOf(t.getTaskId()), t -> t));
        }
        return result;
    }

    public JSONArray getTaskCatalogTree(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter,
                                        Integer page,
                                        Integer pager,
                                        String productId,
                                        String projectId,
                                        Integer type,
                                        String sortby) {
        //获取任务列表
        List<Task> taskList = this.getList(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        //获取目录列表
        IQueryWrapper<Catalog> catalogIQueryWrapper = getService(Catalog.class).getQuery().eq("projectId", projectId);
        if (null != type) {
            catalogIQueryWrapper.eq("type", type);
        }
        //根据name排序
        String sortbyField = sortby;
        String sortbyType = "asc";
        if (StringUtils.isNotBlank(sortby) && sortby.contains("_")) {
            String[] split = sortbyField.split("_");
            sortbyField = split[0];
            sortbyType = split[1];
        }
        if (StringUtils.isNotBlank(sortby) && "name".equals(sortbyField)) {
            if ("asc".equals(sortbyType)) {
                catalogIQueryWrapper.sortbyAsc("name");
            } else if ("desc".equals(sortbyType)) {
                catalogIQueryWrapper.sortbyDesc("name");
            }
        }
        List<Catalog> catalogList = catalogIQueryWrapper.list();
//        catalogService.initTaskCount(catalogList);
        List<JSONObject> jsonObjectList = new ArrayList<>();
        //构建树
        if (null != catalogList && catalogList.size() > 0) {
            if (null != taskList && taskList.size() > 0) {
                catalogList.forEach(c -> {
                    //20240713 树查询关联任务时不关联工作流id为0的数据
//                    List<Task> tasks = taskList.stream().filter(t1 -> !"0".equals(t1.getProcessDefinitionCode())).filter(t2 -> t2.getCatalogId().equals(c.getId())).collect(Collectors.toList());
                    List<Task> tasks = taskList.stream().filter(t2 -> null != t2.getCatalogId() && t2.getCatalogId().equals(c.getId())).collect(Collectors.toList());
                    if (null != tasks && !tasks.isEmpty()) {
                        tasks.forEach(task -> {
                            JSONObject jsonObject = JsonUtil.toJSON(task);
                            String name = jsonObject.getString("name");
                            if (name.contains("__[") && name.contains("_copy")) {
                                //如果是复制的工作流，要截取中间的一节
                                jsonObject.put("name", Utils.removeBetween(name, "__", "_copy"));
                            } else if (name.contains("__[")) {
                                jsonObject.put("name", name.substring(0, name.lastIndexOf("__[")));
                            }
                            jsonObject.put("pid", c.getId());
                            jsonObject.put("level", c.getLevel() + 1);
                            jsonObject.put("isCatalog", false);
                            jsonObjectList.add(jsonObject);
                        });
                    }
                    JSONObject jsonObject = JsonUtil.toJSON(c);
                    jsonObject.put("isCatalog", true);
                    jsonObjectList.add(jsonObject);
                });
            } else {
                catalogList.forEach(c -> {
                    JSONObject jsonObject = JsonUtil.toJSON(c);
                    jsonObject.put("isCatalog", true);
                    jsonObjectList.add(jsonObject);
                });
            }
        }

        TreeNodeUtil treeNodeUtil = new TreeNodeUtil(jsonObjectList);
        return treeNodeUtil.getTree();
    }

    /**
     * 新增工作流
     *
     * @param process
     * @return
     */
    public Map createProcessDefinition(ProcessDefinition process) throws UnsupportedEncodingException {
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", process.getProjectId());
        String check = preUrl + Constants.PROCESS_VERIFY_NAME + "?name=" + URLEncoder.encode(process.getName(), "UTF-8");
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.get4url(check, null, headers, Map.class);
        if (Integer.valueOf(resultMap.get("code").toString()) == 0) {
            Map<String, String> params = new HashMap<>();
            params.put("code", process.getId());
            params.put("name", process.getName());
            params.put("description", process.getDescription());
            params.put("globalParams", process.getGlobalParamMap());
            params.put("locations", process.getLocations());
            params.put("timeout", "0");
            params.put("tenantCode", "default");
            params.put("taskRelationJson", JSONObject.toJSONString(process.getTaskRelationList()));
            params.put("taskDefinitionJson", JSONObject.toJSONString(process.getTaskList()));
//            params.put("otherParamsJson", process.getOtherParamsJson());
            params.put("executionType", process.getExecutionType());
            log.info("新增工作流参数是{}", params);
            Map saveMap = Utils.webClientPost(preUrl, params, headers.get(Constants.SESSION_ID).toString());
            return saveMap;
        } else {
            return resultMap;
        }
    }

    public Map updateProcessDefinition(ProcessDefinition process) {
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", process.getProjectId());
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> params = new HashMap<>();
        params.put("name", process.getName());
        params.put("description", process.getDescription());
        params.put("globalParams", process.getGlobalParamMap());
        params.put("locations", process.getLocations());
        params.put("timeout", "0");
        params.put("tenantCode", "default");
        params.put("taskRelationJson", JSONObject.toJSONString(process.getTaskRelationList()));
        params.put("taskDefinitionJson", JSONObject.toJSONString(process.getTaskList()));
//        params.put("otherParamsJson", process.getOtherParamsJson());
        params.put("executionType", process.getExecutionType());
        params.put("updateVersion", "0");
        return Utils.webClientPut(preUrl + "/" + process.getId(), params, headers.get(Constants.SESSION_ID).toString());
    }

    /**
     * 上线或者下线
     *
     * @param release
     * @param code
     * @return
     */
    public Map onlineOrOffline(Release release, String code) {
        String releaseState = release.getReleaseState();
        Map<String, Object> headers = Utils.getHeader();
        String preUrl = dolphinscheduler + Constants.PROCESS_CREATE.replace("{projectCode}", release.getProjectCode());
        String uri = Constants.PROCESS_RELEASE.replace("{code}", code) + "?releaseState=" + releaseState;
        Map resultMap = httpRequestFeignService.post4url(preUrl + uri, null, headers, Map.class);
        return resultMap;
    }

    /**
     * 运行任务
     *
     * @param param
     * @return
     */
    public Map runTask(StartParam param) throws UnsupportedEncodingException {
        String preUrl = dolphinscheduler + Constants.EXECUTORS_START_PROCESS_INSTANCE.replace("{projectCode}", param.getProjectId());
        String scheduleTime = param.getScheduleTime();
        if (null != scheduleTime) {
            scheduleTime = URLEncoder.encode(scheduleTime, "UTF-8");
        }
        String url = preUrl + "?processDefinitionCode=" + param.getProcessDefinitionCode()
                + "&failureStrategy=" + param.getFailureStrategy()
                + "&warningType=" + param.getWarningType()
                + "&warningGroupId=" + param.getWarningGroupId()
                + "&execType=" + param.getExecType()
                + "&startNodeList=" + StringUtils.defaultIfBlank(param.getStartNodeList(), "")
                + "&taskDependType=" + param.getTaskDependType()
                + "&complementDependentMode=" + param.getComplementDependentMode()
                + "&runMode=" + param.getRunMode()
                + "&processInstancePriority=" + param.getProcessInstancePriority()
                + "&workerGroup=" + param.getWorkerGroup()
                + "&environmentCode=" + param.getEnvironmentCode()
                + "&startParams=" + URLEncoder.encode(StringUtils.defaultIfBlank(param.getStartParams(), ""), "UTF-8")
                + "&expectedParallelismNumber=" + param.getExpectedParallelismNumber()
                + "&dryRun=" + param.getDryRun()
                + "&testFlag=" + param.getTestFlag()
                + "&scheduleTime=" + scheduleTime;
        Map<String, Object> headers = Utils.getHeader();
        Map resultMap = httpRequestFeignService.post4url(url, null, headers, Map.class);
        return resultMap;
    }

    public Map getProcessInstanceId(String processDefineCode, String projectCode) throws UnsupportedEncodingException {
        String url = dolphinscheduler + "/projects/" + projectCode + "/process-instances" +
                "?processDefineCode=" + processDefineCode + "&pageNo=1&pageSize=10&searchVal=&executorName=&stateType=&host=&startTime=&endTime=&otherParamsJson=";
        Map<String, Object> headers = Utils.getHeader();
        return httpRequestFeignService.get4url(url, headers, Map.class);
    }

    public Map getTaskInstance(String processInstanceId, String projectCode) {
        String url = dolphinscheduler + "/projects/" + projectCode + "/task-instances?pageNo=1&pageSize=10" +
                "&searchVal=&host=&host=&stateType=&startDate=&endDate=&executorName=&processInstanceName=&processInstanceId=" + processInstanceId;
        Map<String, Object> headers = Utils.getHeader();
        return httpRequestFeignService.get4url(url, headers, Map.class);
    }

    /**
     * 查看日志
     *
     * @param taskInstanceId
     * @param skipLineNum
     * @param limit
     * @return
     */
    public Map showLogger(String taskInstanceId, String skipLineNum, String limit) {
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.LOG_DETAIL + "?taskInstanceId=" + taskInstanceId + "&limit=" + limit + "&skipLineNum=" + skipLineNum;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        JSONObject data = JsonUtil.toJSON(map.get("data"));
        if (null != data) {
            data.put("taskInstanceId", taskInstanceId);
            map.put("data", data);
        }
        return map;
    }

    /**
     * 查看引擎日志
     *
     * @param taskInstanceId
     * @param skipLineNum
     * @param limit
     * @return
     */
    public Map showEngineLogger(String taskInstanceId, String skipLineNum, String limit) {
        String jobId = getService(EngineJobMetrics.class).setIgnorePermissions().getQuery().eq("instanceId", taskInstanceId).sortbyDesc("startDate").oneValue("jobId", String.class);
        if (StringUtils.isBlank(jobId)) {
            Map map = new HashMap();
            map.put("code", 0);
            map.put("msg", "success");
            return map;
        }
        Map<String, Object> headers = Utils.getHeader();
        String url = dolphinscheduler + Constants.LOG_ENGINE_DETAIL + "?taskInstanceId=" + taskInstanceId + "&limit=" + limit + "&skipLineNum=" + skipLineNum + "&jobId=" + jobId;
        Map map = httpRequestFeignService.get4url(url, headers, Map.class);
        JSONObject data = JsonUtil.toJSON(map.get("data"));
        if (null != data) {
            data.put("taskInstanceId", taskInstanceId);
            map.put("data", data);
        }
        return map;
    }

    /**
     * 手动任务发布
     *
     * @return
     */
    public Integer manualTaskPublishing(Task task) {
        String id = task.getId();
        if (StringUtils.isBlank(id)) {
            id = GeneratorUtil.genCode();
        }
        return this.update(id, task);
    }


    public void initTestTaskId(TestTask testTask, String id) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String format = formatter.format(new Date());


        //2024-07-18 试运行工作流名 任务名加上任务id，做试运行记录用
        String processDefinitionId = testTask.getProcessDefinition().getId();
        String projectId = testTask.getProcessDefinition().getProjectId();
        testTask.getProcessDefinition().setName("试运行-" + testTask.getProcessDefinition().getName() + "-" + format + "-" + processDefinitionId + "-[temp]");
        //赋值工作流名 工作流id
        testTask.getProcessDefinition().setId(id);
        //赋值工作流location
        String locations = testTask.getProcessDefinition().getLocations();
        List<JSONObject> locationsJson = JSONArray.parseArray(locations, JSONObject.class);
        locationsJson.forEach(t -> t.put("taskCode", id));
        testTask.getProcessDefinition().setLocations(JSONObject.toJSONString(locationsJson));
        //赋值任务名 任务id
        testTask.getProcessDefinition().getTaskList().forEach(t -> {
            t.setName("试运行-" + t.getName() + "-" + format + "-" + processDefinitionId + "-[temp]");
            t.setId(id);
            t.setCode(id);
            //2024-09-05试运行任务不加重试
            t.setFailRetryTimes(0);
            t.setFailRetryInterval(0);
            if (2 == t.getTaskTimeoutStrategy() || 3 == t.getTaskTimeoutStrategy()) {
                t.setTimeout(Task.conversionUnit(t.getTaskTimeout(), t.getTimeoutUnit()));
            }
        });
        //赋值任务关系任务code
        List<ProcessTaskRelation> taskRelationList = testTask.getProcessDefinition().getTaskRelationList();
        taskRelationList.forEach(t -> t.setPostTaskCode(id));
        //赋值运行工作流id
        testTask.getStartParam().setProcessDefinitionCode(id);
        //赋值项目id
        testTask.getStartParam().setProjectId(projectId);
    }

    public Response<?> previewFile(String filePath) {
        try {
            Map<String, Object> headers = Utils.getHeader();
            String url = dolphinscheduler + Constants.PREVIEW_FILE;
            JSONObject params = new JSONObject();
            params.put("filePath", filePath);
            log.info("预览文件接口url={}", url);
            log.info("预览文件接口headers={}", headers);
            Map saveMap = httpRequestFeignService.get4url(url, params, headers, Map.class);
            if (Integer.parseInt(saveMap.get("code").toString()) != 0) {
                return ResponseFactory.makeError(saveMap.get("msg").toString());
            }
            return ResponseFactory.makeSuccess(saveMap.get("data"));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseFactory.makeError("预览文件失败！" + e.getMessage());
        }
    }

    public Response<String> previewStx(String projectId, JSONObject body) {
        String url = dolphinscheduler + Constants.TASK_DEFINITION.replace("{projectCode}", projectId) + "/precompile/stx?ignorePermissions=true";
        Map map = httpRequestFeignService.post4url(url, body, Map.class);
        if (Integer.parseInt(map.get("code").toString()) != 0) {
            log.error("预览失败...");
            return ResponseFactory.makeError("预览失败:" + map.get("msg"));
        }
        return ResponseFactory.makeSuccess(map.get("data").toString());
    }


}