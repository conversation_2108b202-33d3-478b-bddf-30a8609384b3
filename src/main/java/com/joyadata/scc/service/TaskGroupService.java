package com.joyadata.scc.service;

import com.joyadata.exception.AppErrorException;
import com.joyadata.scc.model.TaskGroup;
import com.joyadata.scc.util.Utils;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/21 14:04.
 */
@Slf4j
@Service
public class TaskGroupService extends BaseService<TaskGroup> {
    @Value("${dolphinscheduler}")
    private String dolphinscheduler;

    @Override
    public TaskGroup add(String id, TaskGroup bean) {
        String url = dolphinscheduler + Constants.CREATE_TASK_GROUP;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("name", bean.getName());
        data.put("projectCode", bean.getProjectId());
        data.put("description", bean.getDescription());
        data.put("groupSize", Integer.toString(bean.getGroupSize()));
        data.put("datasourceInfoId", bean.getDatasourceInfoId());
        data.put("dataName", bean.getDataName());
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("新增任务组失败，错误信息是{}", saveMap);
                throw new AppErrorException("新增任务组失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("新增任务组失败，错误信息是{}", e.getMessage());
            throw new AppErrorException("新增任务组失败！报错信息=" + e.getMessage());
        }
        return null;
    }

    @Override
    public Integer update(String id, TaskGroup bean) {
        String url = dolphinscheduler + Constants.UPDATE_TASK_GROUP;
        Map<String, Object> headers = Utils.getHeader();
        Map<String, String> data = new HashMap<>();
        data.put("id", bean.getId());
        data.put("name", bean.getName());
        data.put("projectCode", bean.getProjectId());
        data.put("description", bean.getDescription());
        data.put("status", Integer.toString(bean.getStatus()));
        data.put("groupSize", Integer.toString(bean.getGroupSize()));
        data.put("datasourceInfoId", bean.getDatasourceInfoId());
        data.put("dataName", bean.getDataName());
        try {
            Map saveMap = Utils.webClientPost(url, data, headers.get(Constants.SESSION_ID).toString());
            if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0 &&
                    Integer.valueOf(String.valueOf(saveMap.get("code"))) == 130003) {
                log.error("任务组已经被关闭,mag={}", saveMap);
                throw new AppErrorException("任务组已经被关闭，不可修改！");
            } else if (Integer.valueOf(String.valueOf(saveMap.get("code"))) != 0) {
                log.error("修改任务组失败,mag={}", saveMap);
                throw new AppErrorException("修改任务组失败！报错信息=" + saveMap.get("msg").toString());
            }
        } catch (Exception e) {
            log.error("修改任务组失败,mag={}", e.getMessage());
            throw new AppErrorException("修改任务组失败！报错信息=" + e.getMessage());
        }
        return 1;
    }


}
