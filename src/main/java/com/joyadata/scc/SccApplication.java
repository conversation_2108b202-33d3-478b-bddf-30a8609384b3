package com.joyadata.scc;

import com.joyadata.AutoApp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/2.
 */
@Slf4j
@SpringBootApplication
@ComponentScan(basePackageClasses = {AutoApp.class, SccApplication.class})
public class SccApplication {

    public static void main(String[] args) {
        try {
            SpringApplication.run(SccApplication.class, args);
        } catch (Exception e) {
            log.error("启动失败:{}", e.getMessage(), e);
        }

    }
}
