package com.joyadata.scc.dto;

import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.ProcessTaskRelation;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.model.Task;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/27 18:05.
 */
@Data
public class ProcessExportDTO {
    private ProcessDefinition processDefinition;
    private List<Task> taskDefinitionList;
    private List<ProcessTaskRelation> processTaskRelationList;
    private Scheduler schedule;
}
