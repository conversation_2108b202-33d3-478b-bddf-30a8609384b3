package com.joyadata.scc.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DatasourceDTO
 * 成都数据源返回
 * @date 2024/1/12
 */
@Data
public class DatasourceDTO {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 数据源信息业务主键（UUID）
     */
    private String datasourceInfoId;

    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String dataType;

    /**
     * 数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号
     */
    private String dataVersion;

    /**
     * 数据源名称
     */
    private String dataName;

    /**
     * 数据源描述
     */
    private String dataDesc;

    /**
     * 数据源填写的表单信息, 保存为json, key键要与表单的name相同
     */
    private String dataJson;

    /**
     * 连接状态 0-连接失败, 1-正常
     */
    private Integer status;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;
}
