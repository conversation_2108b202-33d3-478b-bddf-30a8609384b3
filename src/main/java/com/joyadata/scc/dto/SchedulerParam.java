package com.joyadata.scc.dto;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.scc.model.Scheduler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/22.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SchedulerParam {
    private String id;
    private String projectCode;
    private String processDefinitionCode;
    private String schedule;
    private String warningType = "NONE";
    private String warningGroupId = "1";
    private String failureStrategy = "CONTINUE";
    private String workerGroup = "default";
    private String environmentCode;
    private String processInstancePriority;
    private String calendarId;

    public SchedulerParam(Scheduler schedule, String dbid) {
        this.id = dbid;
        this.projectCode = schedule.getProjectId();
        this.processDefinitionCode = schedule.getProcessDefinitionCode();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("startTime", schedule.getStartTime());
        jsonObject.put("endTime", schedule.getEndTime());
        jsonObject.put("crontab", schedule.getCrontab());
        jsonObject.put("timezoneId", schedule.getTimezoneId());
        this.failureStrategy = schedule.getFailureStrategy();
        this.environmentCode = schedule.getEnvironmentCode();
        this.processInstancePriority = schedule.getProcessInstancePriority();
        this.calendarId = schedule.getCalendarId();
        this.schedule = jsonObject.toJSONString();
    }

}
