package com.joyadata.scc.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: BaseDataSourceParamDTO
 * 海豚数据源新增dto
 * @date 2024/1/12
 */
@Data
public class BaseDataSourceParamDTO {
    //id
    private String id;

    //数据源名称
    private String name;

    //描述
    private String note;

    //数据库ip
    private String host;

    //数据库port
    private Integer port;

    //数据库库名
    private String database;

    //用户名
    private String userName;

    //密码
    private String password;
    
    //类型
    private String type;
}
