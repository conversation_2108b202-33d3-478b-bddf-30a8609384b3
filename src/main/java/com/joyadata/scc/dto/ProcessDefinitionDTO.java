package com.joyadata.scc.dto;

import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/7.
 */
@Data
public class ProcessDefinitionDTO {
    private String projectCode;
    private String name;
    private String code;
    private String description;
    private String globalParams;
    private String locations;
    private String taskRelationJson;
    private String taskDefinitionJson;
    private String otherParamsJson;
    private String executionType;
    //OFFLINE、ONLINE
    private String releaseState;
    private String tenantCode;
    private String updateVersion;
    private String catalogId;
    private String catalogParentIds;
}
