package com.joyadata.scc.dto.ningbo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/10/22 11:23.
 */
@Data
public class ProcessInstanceDTO {
    @JsonInclude
    private String id;
    @JsonInclude
    private String processDefinitionName;
    @JsonInclude
    private Integer state;
    @JsonInclude
    private Integer writeRowCount;
    @JsonInclude
    private Integer readRowCount;
    @JsonInclude
    private String catalogueName;
    @JsonInclude
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonInclude
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
