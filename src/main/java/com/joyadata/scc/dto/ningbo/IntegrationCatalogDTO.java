package com.joyadata.scc.dto.ningbo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/10/22 9:36.
 */
@Data
public class IntegrationCatalogDTO {
    @JsonInclude
    private List<IntegrationCatalogDTO> children = new ArrayList<>();
    @JsonInclude
    private String createDeptId;
    @JsonInclude
    private String createUserId;
    @JsonInclude
    private Boolean optionsId;
    @JsonInclude
    private String parentIds;
    @JsonInclude
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    @JsonInclude
    private String tenantCode;
    @JsonInclude
    private Boolean isLeaf;
    @JsonInclude
    private String parentId;
    @JsonInclude
    private String createBy;
    @JsonInclude
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonInclude
    private String updateBy;
    @JsonInclude
    private String name;
    @JsonInclude
    private String id;
    @JsonInclude
    private String projectId;
    @JsonInclude
    private String projectName;

}
