package com.joyadata.scc.dto;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.joyadata.scc.model.Task;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by zhus<PERSON><PERSON> on 2022/11/17 16:33.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskDTO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * code
     */
    private String code;

    /**
     * name
     */
    private String name;

    /**
     * version
     */
    private Integer version;

    /**
     * description
     */
    private String description;

    /**
     * project code
     */
    private String projectCode;

    /**
     * task user id
     */
    private String userId;

    /**
     * task type
     */
    private String taskType;

    /**
     * user defined parameters
     */
//    @JsonDeserialize(using = JSONUtils.JsonDataDeserializer.class)
//    @JsonSerialize(using = JSONUtils.JsonDataSerializer.class)
    private JSONObject taskParams;

    /**
     * task priority
     */
    private String taskPriority;

    /**
     * worker group
     */
    private String workerGroup;

    /**
     * environment code
     */
    private long environmentCode;

    /**
     * fail retry times
     */
    private int failRetryTimes;

    /**
     * fail retry interval
     */
    private int failRetryInterval;

    /**
     * task warning time out. unit: minute
     */
    private long timeout;

    /**
     * delay execution time.
     */
    private int delayTime;

    /**
     * resource ids
     */
    private String resourceIds;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * modify user name
     */
    @TableField(exist = false)
    private String modifyBy;

    /**
     * task group id
     */
    private String taskGroupId;
    /**
     * task group id
     */
    private int taskGroupPriority;

    /**
     * cpu quota
     */
    private Integer cpuQuota;

    /**
     * max memory
     */
    private Integer memoryMax;

    /**
     * task execute type
     */
    private String taskExecuteType;

    /**
     * task is valid: yes/no
     */
    private String flag;

    /**
     * task execute type
     */
//    private TaskExecuteType taskExecuteType;

    /**
     * 任务默认没有版本
     */
    private int updateVersion;

    //失败停止策略:stop/continue
    private String errorStrategy;

    /**
     * 文件路径参数，需要将文件内容放入到任务中
     */
    private String fileParamsPath;

    public TaskDTO(Task task) {
        this.id = task.getDbid();
        this.code = task.getId();
        this.name = task.getName();
        this.version = task.getVersion();
        this.description = task.getDescription();
        this.projectCode = task.getProjectId();
//        this.userId=task.getCreateBy();
        this.taskType = task.getTaskType();
        this.taskParams = task.getTaskParams();
        this.taskPriority = task.getTaskPriority();
        this.environmentCode = Long.parseLong(task.getEnvironmentCode());
        this.failRetryInterval = task.getFailRetryInterval();
        this.failRetryTimes = task.getFailRetryTimes();
        this.delayTime = task.getDelayTime();
        this.resourceIds = task.getResourceIds();
        this.cpuQuota = task.getCpuQuota();
        this.memoryMax = task.getMemoryMax();
        this.workerGroup = task.getWorkerGroup();
        this.taskExecuteType = task.getTaskExecuteType();
        this.flag = task.getFlag();
        this.errorStrategy = task.getErrorStrategy();
        this.taskGroupId = task.getTaskGroupId();
        this.taskGroupPriority = task.getTaskGroupPriority();
        this.timeout = task.getTimeout();
		this.fileParamsPath = task.getFileParamsPath();
    }

}
