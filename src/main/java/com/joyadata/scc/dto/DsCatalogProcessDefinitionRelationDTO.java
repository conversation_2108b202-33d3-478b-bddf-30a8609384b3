package com.joyadata.scc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class DsCatalogProcessDefinitionRelationDTO {
    /**
     * id
     */
    private String id;

    /**
     * 目录id
     */
    private String catalogId;

    /**
     * 上级id 以逗号线分割 （含自己）
     */
    private String catalogParentIds;
    /**
     * 工作流id
     */
    private long processDefinitionCode;
    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;
}
