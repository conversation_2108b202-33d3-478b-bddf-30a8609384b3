package com.joyadata.scc.dto;

import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.Scheduler;
import com.joyadata.scc.model.dto.StartParam;
import lombok.Data;

/**
 * Created by lihongjun on 2022/12/5.
 */
@Data
public class TestTask {
    /**
     * 创建工作流编排所需参数
     */
    private ProcessDefinition processDefinition;

    /**
     * 运行参数
     */
    private StartParam startParam;

    /**
     * 查看日志
     */
    private String taskInstanceId;
    private String skipLineNum = "0";
    private String limit = "100";

    /**
     * 定时策略参数
     */
    private Scheduler scheduleParam;
}
