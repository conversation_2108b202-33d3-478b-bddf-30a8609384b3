package com.joyadata.scc.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 导入任务请求DTO
 */
@Data
public class ImportTaskRequest {
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 导入文件（tar包）
     */
    private MultipartFile file;
    
    /**
     * 是否覆盖已存在的工作流
     */
    private Boolean overwrite = false;
    
    /**
     * 导入模式：FULL-完整导入，SELECTIVE-选择性导入
     */
    private String importMode = "FULL";
    
    /**
     * 选择性导入时指定的工作流代码列表（逗号分隔）
     */
    private String selectedWorkflows;
    
    /**
     * 备注
     */
    private String remark;
}
