package com.joyadata.scc.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.quartz.TriggerUtils;
import org.quartz.impl.triggers.CronTriggerImpl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/24 16:23.
 */
@Slf4j
public class CronUtils {

    /**
     * @param cronExpression cron表达式
     * @param numTimes       下一(几)次运行的时间
     * @return
     */
    public static List<Date> getNextExecTime(String cronExpression, Integer numTimes) {
        CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
        try {
            cronTriggerImpl.setCronExpression(cronExpression);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // 这个是重点，一行代码搞定
        List<Date> dates = TriggerUtils.computeFireTimes(cronTriggerImpl, null, numTimes);
        return dates;
    }

    /**
     * 是否在生效时间段内
     *
     * @param cronExpression cron表达式
     * @param endTime        结束时间
     * @param timezoneId     时区
     * @return
     */
    public static String getNextExecTime(String cronExpression, Date startTime, Date endTime, String timezoneId) {
        try {
            // 解析Cron表达式
            CronExpression cron = new CronExpression(cronExpression);
            cron.setTimeZone(java.util.TimeZone.getTimeZone(timezoneId));

            // 计算下一次执行时间
            Date nextExecutionTime = getNextExecutionTime(cron, startTime, new Date(), 0);

            // 检查下一次执行时间是否在结束时间之前
            while (nextExecutionTime != null && nextExecutionTime.getTime() <= endTime.getTime()) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return dateFormat.format(nextExecutionTime);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 是否在生效时间段内
     *
     * @param cronExpression cron表达式
     * @param endTime        结束时间
     * @param timezoneId     时区
     * @return
     */
    public static Date getNextExecTime(String cronExpression, Date startTime, Date endTime, String timezoneId, Date now, int times) {
        try {
            // 解析Cron表达式
            CronExpression cron = new CronExpression(cronExpression);
            cron.setTimeZone(java.util.TimeZone.getTimeZone(timezoneId));

            // 计算下一次执行时间
            Date nextExecutionTime = getNextExecutionTime(cron, startTime, now, times);

            // 检查下一次执行时间是否在结束时间之前
            while (nextExecutionTime != null && nextExecutionTime.getTime() <= endTime.getTime()) {
                return nextExecutionTime;
            }
            return null;
        } catch (Exception e) {
            log.error("获取下次执行时间错误={}", e);
            return null;
        }
    }

    /**
     * 获取下一次执行时间
     *
     * @param cronExpression cron表达式
     * @param startTime      生效时间
     * @param now            当前时间
     * @param times          第几次获取，如果是:0（第一次，是临界点直接返回）
     * @return
     */
    private static Date getNextExecutionTime(CronExpression cronExpression, Date startTime, Date now, int times) {
        // 计算下一次执行时间
        Date nextExecutionTime = null;
        if (startTime.getTime() >= now.getTime() && cronExpression.isSatisfiedBy(startTime) && times == 0) {
            //如果是:0（第一次，是临界点直接返回）直接使用 startTime
            nextExecutionTime = startTime;
        } else if (startTime.getTime() > now.getTime()) {
            nextExecutionTime = cronExpression.getNextValidTimeAfter(startTime);
        } else {
            nextExecutionTime = cronExpression.getNextValidTimeAfter(now);
        }
        return nextExecutionTime;
    }

    public static List<Date> filterWorkDays(String cronExpression, Date startTime, Date endTime, String timezoneId, Date now, String calendarId, List<String> workDays, int times) {
        List<Date> result = new ArrayList<>();
        Date nextExecTime = CronUtils.getNextExecTime(cronExpression, startTime, endTime, timezoneId, now, 0);
        //工作日一年最多每天都是，为了防止死循环，加最大循环次数
        int maxAttempts = Year.now().length() * 24 * 60 * 60;
        int attempts = 1;
        while (null != nextExecTime && result.size() < times && attempts <= maxAttempts && nextExecTime.getTime() <= endTime.getTime()) {
            if (isDateInWorkDays(nextExecTime, workDays, calendarId)) {
                result.add(nextExecTime);
            }
            nextExecTime = CronUtils.getNextExecTime(cronExpression, startTime, endTime, timezoneId, nextExecTime, attempts);
            attempts++;
        }
        return result;
    }


    private static boolean isDateInWorkDays(Date nextExecTime, List<String> workDays, String calendarId) {
        if (StringUtils.isBlank(calendarId)) {
            return true;
        }
        // 1. 将 Date 转换为 LocalDate（只保留年月日）
        LocalDate date = nextExecTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 2. 格式化为字符串并判断
        String dateStr = date.toString();
        return workDays.contains(dateStr);
    }
}
