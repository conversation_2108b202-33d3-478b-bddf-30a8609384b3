package com.joyadata.scc.util;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.feign.service.HttpRequestFeignService;
import groovy.lang.Tuple2;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class EngineUtils {

    public static Map<String, Object> getMetericsMap(String appid, String engineIp, HttpRequestFeignService httpRequestFeignService) {
        String url = "http://" + engineIp + ":5801/hazelcast/rest/maps/running-job/" + appid + "?token=cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w";
        try {
            Map<String, Object> map = httpRequestFeignService.get4url(url, Map.class);
            if (null != map && map.size() > 3) {
                return map;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyMap();
    }

    public static Map<String, Object> getMetericsMap(String appid, List<String> engineIps, HttpRequestFeignService httpRequestFeignService) {
        for (int i = 0; i < engineIps.size(); i++) {
            String ip = engineIps.get(i);
            String url = "http://" + ip + "/hazelcast/rest/maps/running-job/" + appid + "?token=cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w";
            try {
                Map<String, Object> map = httpRequestFeignService.get4url(url, Map.class);
                if (null != map && map.size() > 3) {
                    return map;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return Collections.emptyMap();
    }

    public static Tuple2<Integer, Integer> getSourceSinkCount(JSONObject taskInfo) {
        //如果是shell任务 执行参数不一定是JSONObject
        JSONObject config;
        try {
            config = taskInfo.getJSONObject("rawScript");
        } catch (Exception e) {
            return new Tuple2<>(0, 0);
        }
        int sourceCount = config.getJSONArray("source").size();
        int sinkCount = config.getJSONArray("sink").size();
        return new Tuple2<>(sourceCount, sinkCount);
    }
}
