package com.joyadata.scc.util;

import com.joyadata.scc.enums.Language;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class DataConverter {
    private final Language language;

    // 构造函数支持语言代码字符串
    public DataConverter(String languageCode) {
        this.language = Language.fromCode(languageCode);
    }

    // 构造函数支持Language枚举
    public DataConverter(Language language) {
        this.language = language;
    }

    private static final Map<String, String> cronTabKeys = new HashMap<>();

    static {
        cronTabKeys.put("0 0 ? * SAT *", "cornDay.type3");
        cronTabKeys.put("0 0 1 * ? *", "cornDay.type2");
        cronTabKeys.put("0 0 1/1 * ? *", "cornDay.type1");
        cronTabKeys.put("0 0/12 * * ? *", "cornHour.type5");
        cronTabKeys.put("0 0/6 * * ? *", "cornHour.type4");
        cronTabKeys.put("0 0/3 * * ? *", "cornHour.type3");
        cronTabKeys.put("0 0/2 * * ? *", "cornHour.type2");
        cronTabKeys.put("0 0/1 * * ? *", "cornHour.type1");
        cronTabKeys.put("0/30 * * * ? *", "cornMin.type5");
        cronTabKeys.put("0/10 * * * ? *", "cornMin.type4");
        cronTabKeys.put("0/5 * * * ? *", "cornMin.type3");
        cronTabKeys.put("0/2 * * * ? *", "cornMin.type2");
        cronTabKeys.put("0/1 * * * ? *", "cornMin.type1");
    }

    public String convertScheduleStatus(String scheduleStatus) {
        if (scheduleStatus == null) return null;
        if (language == Language.ZH_CN) {
            if ("上线".equals(scheduleStatus)) {
                return "打开";
            }
            return null;
        } else {
            if ("上线".equals(scheduleStatus)) {
                return "Enabled";
            }
            return null;
        }
    }

    public String convertReleaseState(String releaseState) {
        if (releaseState == null) return null;
        if (language == Language.EN_US) {
            switch (releaseState.toUpperCase()) {
                case "上线":
                    return "Online";
                case "下线":
                    return "Offline";
                default:
                    return releaseState;
            }
        }
        return releaseState;
    }

    public String convertExecutionType(String executionType) {
        if (executionType == null) return null;

        if (language == Language.ZH_CN) {
            switch (executionType.toUpperCase()) {
                case "PARALLEL":
                    return "并行";
                case "SERIAL_WAIT":
                    return "串行等待";
                case "SERIAL_DISCARD":
                    return "串行抛弃";
                case "SERIAL_PRIORITY":
                    return "串行优先";
                default:
                    return executionType;
            }
        } else {
            switch (executionType.toUpperCase()) {
                case "PARALLEL":
                    return "Parallel";
                case "SERIAL_WAIT":
                    return "Serial Wait";
                case "SERIAL_DISCARD":
                    return "Serial Discard";
                case "SERIAL_PRIORITY":
                    return "Serial Priority";
                default:
                    return executionType;
            }
        }
    }

    public String convertProcessInstancePriority(String priority) {
        if (priority == null) return null;

        if (language == Language.ZH_CN) {
            switch (priority.toUpperCase()) {
                case "HIGHEST":
                    return "最高级";
                case "HIGH":
                    return "高级";
                case "MEDIUM":
                    return "中级";
                case "LOW":
                    return "低级";
                case "LOWEST":
                    return "最低级";
                default:
                    return priority;
            }
        } else {
            switch (priority.toUpperCase()) {
                case "HIGHEST":
                    return "Highest";
                case "HIGH":
                    return "High";
                case "MEDIUM":
                    return "Medium";
                case "LOW":
                    return "Low";
                case "LOWEST":
                    return "Lowest";
                default:
                    return priority;
            }
        }
    }

    public String convertFailureStrategy(String failureStrategy) {
        if (failureStrategy == null) return null;

        if (language == Language.ZH_CN) {
            switch (failureStrategy.toUpperCase()) {
                case "END":
                    return "结束";
                case "CONTINUE":
                    return "继续";
                default:
                    return null;
            }
        } else {
            switch (failureStrategy.toUpperCase()) {
                case "END":
                    return "End";
                case "CONTINUE":
                    return "Continue";
                default:
                    return null;
            }
        }
    }

    public String convertCronDescription(String scheduleStatus, String crontab, int scheduleFrequency) {
        // 多语言状态检查
        if (isOfflineStatus(scheduleStatus)) {
            return null;
        }

        String cronType = null;
        if (crontab != null && !crontab.trim().isEmpty()) {
            String[] parts = crontab.trim().split("\\s+");
            if (parts.length > 1) {
                cronType = String.join(" ", Arrays.copyOfRange(parts, 1, parts.length));
            }
        }

        switch (scheduleFrequency) {
            case 0:
                if (cronType != null) {
                    String key = cronTabKeys.get(cronType);
                    if (key != null) {
                        String[] parts = key.split("\\.");
                        return CronI18n.getText(language, parts[0], parts[1]);
                    }
                }
                return null;
            case 1:
                return getCornType2Description();
            default:
                return null;
        }
    }

    private boolean isOfflineStatus(String status) {
        if (status == null) return false;
        return language == Language.ZH_CN ?
                "下线".equals(status) :
                "Offline".equalsIgnoreCase(status);
    }

    private String getCornType2Description() {
        return language == Language.ZH_CN ?
                "自定义" :
                "Custom";
    }

    /**
     * 根据前端发的页面显示转换来处理
     * @param state
     * @return
     */
    public String convertProcessInstanceState(String state) {
        if (state == null) return null;
        if (language == Language.ZH_CN) {
            switch (state) {
                case "SUBMITTED_SUCCESS":
                    return "提交成功";
                case "RUNNING_EXECUTION":
                    return "正在执行";
                case "READY_PAUSE":
                    return "准备暂停";
                case "PAUSE":
                    return "暂停";
                case "READY_STOP":
                    return "准备停止";
                case "STOP":
                    return "停止";
                case "FAILURE":
                    return "失败";
                case "SUCCESS":
                    return "成功";
                case "NEED_FAULT_TOLERANCE":
                    return "需要容错";
                case "KILL":
                    return "KILL";
                case "DELAY_EXECUTION":
                    return "延迟执行";
                case "SERIAL_WAIT":
                    return "串行等待,";
                case "DISPATCH":
                    return "派发";
                case "PENDING":
                    return "挂起";
                default:
                    return state;
            }
        } else {
            switch (state) {
                case "SUBMITTED_SUCCESS":
                    return "Submitted Success";
                case "RUNNING_EXECUTION":
                    return "Running Execution";
                case "READY_PAUSE":
                    return "Ready Pause";
                case "PAUSE":
                    return "Pause";
                case "READY_STOP":
                    return "Ready Stop";
                case "STOP":
                    return "Stop";
                case "FAILURE":
                    return "Failure";
                case "SUCCESS":
                    return "Success";
                case "NEED_FAULT_TOLERANCE":
                    return "Need Fault Tolerance";
                case "KILL":
                    return "Kill";
                case "DELAY_EXECUTION":
                    return "Delay Execution";
                case "SERIAL_WAIT":
                    return "Serial Wait,";
                case "DISPATCH":
                    return "Dispatch";
                case "PENDING":
                    return "Pending";
                default:
                    return state;
            }
        }
    }

    /**
     * 根据前端发的页面显示转换来处理
     * @param commandType
     * @return
     */
    public String convertCommandType(String commandType) {
        if (commandType == null) return null;
        if (language == Language.ZH_CN) {
            switch (commandType) {
                case "START_PROCESS":
                    return "启动工作流";
                case "START_CURRENT_TASK_PROCESS":
                    return "从当前节点开始执行";
                case "RECOVER_TOLERANCE_FAULT_PROCESS":
                    return "恢复被容错的工作流";
                case "RECOVER_SUSPENDED_PROCESS":
                    return "恢复运行流程";
                case "START_FAILURE_TASK_PROCESS":
                    return "从失败节点开始执行";
                case "COMPLEMENT_DATA":
                    return "补数";
                case "SCHEDULER":
                    return "调度执行";
                case "OLD_RUNNING":
                case "REPEAT_RUNNING":
                    return "重跑";
                case "PAUSE":
                    return "暂停";
                case "STOP":
                    return "停止";
                case "RECOVER_WAITING_THREAD":
                    return "恢复等待线程,";
                case "RECOVER_SERIAL_WAIT":
                    return "串行恢复";
                default:
                    return commandType;
            }
        } else {
            switch (commandType) {
                case "START_PROCESS":
                    return "Start Workflow";
                case "START_CURRENT_TASK_PROCESS":
                    return "Start from Current Node";
                case "RECOVER_TOLERANCE_FAULT_PROCESS":
                    return "Resume Fault-Tolerated Workflow";
                case "RECOVER_SUSPENDED_PROCESS":
                    return "Resume Execution Flow";
                case "START_FAILURE_TASK_PROCESS":
                    return "Start from Failure Node";
                case "COMPLEMENT_DATA":
                    return "Supplement";
                case "SCHEDULER":
                    return "Scheduling Execution";
                case "OLD_RUNNING":
                case "REPEAT_RUNNING":
                    return "Retry";
                case "PAUSE":
                    return "Pause";
                case "STOP":
                    return "Stop";
                case "RECOVER_WAITING_THREAD":
                    return "Resume Waiting Threads,";
                case "RECOVER_SERIAL_WAIT":
                    return "Serial Recovery";
                default:
                    return commandType;
            }
        }
    }
}