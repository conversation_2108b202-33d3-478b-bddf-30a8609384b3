package com.joyadata.scc.util;

import freemarker.template.Configuration;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
public class ExportFile {

    private Configuration config = new Configuration(Configuration.VERSION_2_3_28);

    public ExportFile() throws IOException {
        config.setDefaultEncoding("utf-8");
        config.setDirectoryForTemplateLoading(new File("/"));
        config.setClassicCompatible(true);
    }

    public File createFile(String fileContent, String saveFilePath) {
        File outFile = new File(saveFilePath);
        if (!outFile.getParentFile().exists()) {
            outFile.getParentFile().mkdirs();
        }
        //创建文件
        try {
            new FileOutputStream(outFile);
        } catch (FileNotFoundException e) {
            log.info("输出文件时未找到文件", e);
            e.printStackTrace();
        }
        //写入内容
        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(saveFilePath), StandardCharsets.UTF_8)) {
            writer.write(fileContent);
            log.info("写入内容：" + fileContent);
        } catch (IOException e) {
            e.printStackTrace();
            log.info("内容写入异常", e);
        }
        return outFile;
    }
}
