package com.joyadata.scc.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Base64;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/14.
 */
public class MyBase64 {

    public static String decoder(String str) {
        if (StringUtils.isNotBlank(str)) {
            return new String(Base64.getDecoder().decode(str.getBytes()));
        } else {
            return str;
        }
    }

    public static String encoder(String str) {
        if (StringUtils.isNotBlank(str)) {
            return new String(Base64.getEncoder().encode(str.getBytes()));
        } else {
            return str;
        }
    }
}
