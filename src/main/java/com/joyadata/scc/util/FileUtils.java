package com.joyadata.scc.util;

import com.joyadata.exception.AppErrorException;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @Date 2025/8/28 10:24
 */
public class FileUtils {

    public static String readFileToString(File file) {
        try (InputStream inputStream = new FileInputStream(file)) {
            return IOUtils.toString(inputStream, "UTF-8");
        } catch (IOException e) {
            throw new AppErrorException("读取JSON文件失败，原因：{}", e.getMessage());
        }
    }
}
