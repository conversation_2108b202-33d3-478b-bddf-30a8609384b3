package com.joyadata.scc.util;

import com.joyadata.cms.model.User;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.util.constant.Constants;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by lihongjun on 2022/11/9.
 */
@Slf4j
public class Utils {

    /**
     * 需要将我们的token转换为ds的token，我们的token在header中是token，ds header中是sessionId
     *
     * @return
     */
    public static Map<String, Object> getHeader() {
        Map<String, Object> headers = new HashMap<>();
        User user = ThreadLocalUserUtil.getUser(User.class);
        if (null == user) {
            log.error("离线开发平台调度到此项目获取用户为空");
            return headers;
        }
//        user.setUsername("admin");
//        user.setPassword("dolphinscheduler123");
//        user.setIsAdmin(false);
//        user.setIsSuperAdmin(false);
        String dsToken = RedisUtils.setTokenInfo(user);
        headers.put(Constants.SESSION_ID, dsToken);
        return headers;
    }

    public static Map<String, Object> getHeader(User user) {
        Map<String, Object> headers = new HashMap<>();
        String dsToken = RedisUtils.setTokenInfo(user);
        headers.put(Constants.SESSION_ID, dsToken);
        return headers;
    }

    public static File downloadFile(String urlPath, String fileName, String tmpPath) {
        File file = null;
        try {
            // 统一资源
            URL url = new URL(urlPath);
            // 连接类的父类，抽象类
            URLConnection urlConnection = url.openConnection();
            // http的连接类
            HttpURLConnection httpURLConnection = (HttpURLConnection) urlConnection;
            //设置超时
            httpURLConnection.setConnectTimeout(1000 * 5);
            //设置请求方式，默认是GET
            httpURLConnection.setRequestMethod("GET");
            // 设置字符编码
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            // 打开到此 URL引用的资源的通信链接（如果尚未建立这样的连接）。
            httpURLConnection.connect();
            // 文件大小
            int fileLength = httpURLConnection.getContentLength();

            // 控制台打印文件大小
            System.out.println("您要下载的文件大小为:" + fileLength / (1024 * 1024) + "MB");

            // 建立链接从请求中获取数据
            URLConnection con = url.openConnection();
            BufferedInputStream bin = new BufferedInputStream(httpURLConnection.getInputStream());
            // 指定存放位置(有需求可以自定义)
            String path = tmpPath + File.separatorChar + fileName;
            file = new File(path);
            // 校验文件夹目录是否存在，不存在就创建一个目录
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            OutputStream out = new FileOutputStream(file);
            int size = 0;
            int len = 0;
            byte[] buf = new byte[2048];
            while ((size = bin.read(buf)) != -1) {
                len += size;
                out.write(buf, 0, size);
                // 控制台打印文件下载的百分比情况
                System.out.println("下载了-------> " + len * 100 / fileLength + "%\n");
            }
            // 关闭资源
            bin.close();
            out.close();
            System.out.println("文件下载成功！");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return file;
        }
    }

    public static String uploadFile(String url, MultipartFile file, String fileParamName, Map<String, Object> headerParams, Map<String, String> otherParams) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String result = "";
        try {
            String fileName = file.getOriginalFilename();
            HttpPost httpPost = new HttpPost(url);
            //添加header
            for (Map.Entry<String, Object> e : headerParams.entrySet()) {
                httpPost.addHeader(e.getKey(), String.valueOf(e.getValue()));
            }
            MultipartEntityBuilder builder = MultipartEntityBuilder.create().setMode(HttpMultipartMode.RFC6532);
            builder.setCharset(Charset.forName("utf-8"));
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);//加上此行代码解决返回中文乱码问题
            builder.addBinaryBody(fileParamName, file.getInputStream(), ContentType.MULTIPART_FORM_DATA, fileName);// 文件流
            for (Map.Entry<String, String> e : otherParams.entrySet()) {
                builder.addTextBody(e.getKey(), e.getValue(), ContentType.APPLICATION_JSON);// 类似浏览器表单提交，对应input的name和value
            }
            HttpEntity entity = builder.build();
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);// 执行提交
            log.info("http返回内容{}", response.getStatusLine().toString());
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                // 将响应内容转换为字符串
                result = EntityUtils.toString(responseEntity, Charset.forName("UTF-8"));
            }
            log.info("上传文件返回内容是{}", result);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static void downloadFile(String url, Map<String, Object> headerParams) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpGet httpGet = new HttpGet(url);
            for (Map.Entry<String, Object> e : headerParams.entrySet()) {
                httpGet.addHeader(e.getKey(), String.valueOf(e.getValue()));
            }
            HttpResponse response = httpClient.execute(httpGet);
            System.out.println(response.getStatusLine().getStatusCode());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Response responseInfo(Map map) {
        if (Integer.valueOf(String.valueOf(map.get("code"))) == 0) {
            return ResponseFactory.makeSuccess(map.get("msg").toString(), map.get("data"));
        } else {
            return ResponseFactory.makeError(map.get("msg").toString());
        }
    }

    public static Map webClientPost(String url, Map<String, String> map, String sessionId) {
        /*拼接表单参数*/
        MultiValueMap<String, String> clientParam = new LinkedMultiValueMap<>();
        if (map.keySet().size() > 0) {
            for (String key : map.keySet()) {
                clientParam.add(key, map.get(key));
            }
        }
        /*发起请求*/
        Mono<Map> mono = WebClient.create(url)
                .post().headers(header -> {
                    header.add("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
                    header.add("sessionId", sessionId);
                    return;
                }).body(BodyInserters.fromFormData(clientParam)).exchangeToMono(response -> response.bodyToMono(Map.class));
        return mono.block();
    }

    public static Map webClientPut(String url, Map<String, String> map, String sessionId) {
        /*拼接表单参数*/
        MultiValueMap<String, String> clientParam = new LinkedMultiValueMap<>();
        for (String key : map.keySet()) {
            clientParam.add(key, map.get(key));
        }
        /*发起请求*/
        Mono<Map> mono = WebClient.create(url)
                .put().headers(header -> {
                    header.add("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
                    header.add("sessionId", sessionId);
                    return;
                }).body(BodyInserters.fromFormData(clientParam)).exchangeToMono(response -> response.bodyToMono(Map.class));
        return mono.block();
    }

    /**
     * 复制工作流或者导入工作流时生成新的名字
     *
     * @param originalName
     * @param suffix
     * @return
     */
    public static String getNewName(String originalName, String suffix) {
        StringBuilder newName = new StringBuilder();
        String regex = String.format(".*%s\\d{17}$", suffix);
        if (originalName.matches(regex)) {
            // replace timestamp of originalName
            return newName.append(originalName, 0, originalName.lastIndexOf(suffix))
                    .append(suffix)
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")))
                    .toString();
        }
        return newName.append(originalName)
                .append(suffix)
                .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")))
                .toString();
    }

    /**
     * 获取今天的凌晨  例如今天2022-11-23  获取到2022-11-23 00:00:00
     *
     * @return
     */
    public static String getDay() {
        Calendar now = Calendar.getInstance();
        //获取当前日期
        Date date = new Date();
        //将时间格式化成yyyy-MM-dd 的格式
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //设置当前时间
        now.setTime(date);
        String format2 = format.format(now.getTime());
        return format2.concat(" 00:00:00");
    }

    public static String extractPluginInfo(String input) {
        // 正则表达式，匹配以空格开始，然后是方括号包围的内容，直到字符串结束
        String regex = "\\s\\[(.*?)\\]$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (input.startsWith("pipeline-") && matcher.find()) {
            return matcher.group(1);
        } else {
            return input;
        }
    }

    //去除两个字符之间的内容
    public static String removeBetween(String input, String start, String end) {
        int startIndex = input.indexOf(start);
        int endIndex = input.indexOf(end);
        if (startIndex != -1 && endIndex != -1) {
            return input.substring(0, startIndex) + input.substring(endIndex);
        }
        return input;
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
