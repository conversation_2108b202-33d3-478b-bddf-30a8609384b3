package com.joyadata.scc.util;

import org.apache.commons.lang3.StringUtils;

/**
 * jdbcUrl工具类
 *
 * <AUTHOR>
 * @date 2022/11/8 15:58
 */
public class JdbcUrlUtils {
    /**
     * 根据jdbcUrl返回端口号port
     * 注意：SqlServer的jdbc连接格式如下，需要特殊处理，
     * ***************************************************
     * 或 *******************************************    这种格式可以走通用的解析
     *
     * @param url jdbc连接信息
     * @return 端口号
     */
    public static int getPort(String url) {
        int port = 0;
        if (StringUtils.isNotBlank(url)) {
            int begin = url.indexOf("//");
            int end;
            if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sqlserver")) {
                end = url.lastIndexOf(";");
            } else if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                //oracle sid连接方式
                begin = url.indexOf("@");
                end = url.lastIndexOf(":");
            } else if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:pivotal:greenplum")) {
                //greenplum连接方式
                end = url.lastIndexOf(";");
            } else {
                end = url.lastIndexOf("/");
            }

            if (begin > -1 && end > -1 && end > begin) {
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                    return Integer.parseInt(url.substring(begin + 1, end).split(":")[1]);
                }
                String hostStr = url.substring(begin + 2, end + 1);
                int index = hostStr.indexOf(":");
                int indexE;
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sqlserver") || StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:pivotal:greenplum")) {
                    indexE = hostStr.indexOf(";");
                } else {
                    indexE = hostStr.indexOf("/");
                }
                if (index > 0) {
                    String substring = hostStr.substring(index + 1, indexE);
                    if (substring.contains(",")) {
                        substring = substring.substring(0, substring.indexOf(","));
                    }
                    port = Integer.parseInt(substring);
                } else {
                    port = Integer.parseInt(hostStr);
                }

            }
        }
        return port;
    }

    /**
     * 根据jdbcUrl返回ip
     * 注意：SqlServer的jdbc连接格式如下，需要特殊处理，
     * ***************************************************
     * 或 *******************************************    这种格式可以走通用的解析
     *
     * @param url jdbc连接信息
     * @return ip
     */
    public static String getIp(String url) {
        String ip = null;
        if (StringUtils.isNotBlank(url)) {
            int begin = url.indexOf("//");
            int end;
            if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                //oracle sid连接方式
                begin = url.indexOf("@");
                end = url.substring(0, url.lastIndexOf(":")).lastIndexOf(":");
            } else {
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sqlserver") || StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:pivotal:greenplum")) {
                    end = url.lastIndexOf(";");
                } else {
                    end = url.lastIndexOf("/");
                }
            }
            if (begin > -1 && end > -1 && end > begin) {
                String hostStr = url.substring(begin + 2, end);
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                    hostStr = url.substring(begin + 1, end);
                }
                int index = hostStr.indexOf(":");
                if (index > 0) {
                    ip = hostStr.substring(0, index);
                } else {
                    ip = hostStr;
                }

            }
        }
        return ip;
    }
}
