package com.joyadata.scc.util.constant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/2.
 */
public class Constants {

    /**
     * date format of yyyy-MM-dd HH:mm:ss
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * date format of yyyyMMddHHmmssSSS
     */
    public static final String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";

    public static final String SESSION_ID = "sessionId";
    //public static final String DOLPHINSCHEDULER = ApplicationContextHelp.getAppGatewayHttp() + "/dolphinscheduler";
    //登录
    public static final String LOGIN = "/login";
    //获取所有的工作组
    public static final String WORKER_GROUPS_ALL = "/worker-groups/all";
    //分页查询工作组列表
    public static final String WORKER_GROUPS_LIST = "/worker-groups";
    public static final String WORKER_GROUPS_ADDRESS_LIST = "/worker-groups/worker-address-list";
    //获取环境列表
    public static final String ENVIRONMENT_LIST = "/environment/query-environment-list";
    //获取资源列表
    public static final String RESOURCES_LIST = "/resources/list";
    //获取主程序包
    public static final String RESOURCES_JAR_LIST = "/resources/query-by-type";
    //验证工作流名称是否存在
    public static final String PROCESS_VERIFY_NAME = "/verify-name";
    //添加工作流
    public static final String PROCESS_CREATE = "/projects/{projectCode}/process-definition";
    //删除工作流
    public static final String PROCESS_DELETE = "/projects/{projectCode}/process-definition/{code}";
    //切换工作流版本
    public static final String SWITCH_PROCESS_VERSION = "/projects/{projectCode}/process-definition";
    //复制工作流
    public static final String PROCESS_COPY = "/projects/{projectCode}/process-definition/batch-copy";
    //复制工作流时用来生成新的名字
    public static final String COPY_SUFFIX = "_copy_";
    //工作流上线下线
    public static final String PROCESS_RELEASE = "/{code}/release";
    //运行任务
    public static final String EXECUTORS_START_PROCESS_INSTANCE = "/projects/{projectCode}/executors/start-process-instance";
    //添加任务
    public static final String TASK_DEFINITION = "/projects/{projectCode}/task-definition";
    //上线工作流定时策略
    public static final String PROCESS_SCHEDULE_ONLINE = "/projects/{projectCode}/schedules/{id}/online";
    //下线工作流定时策略
    public static final String PROCESS_SCHEDULE_OFFLINE = "/projects/{projectCode}/schedules/{id}/offline";
    //工作流树形图
    public static final String PROCESS_VIEW_TREE = "/view-tree";
    //批量删除工作流实例
    public static final String PROCESS_INSTANCES_BATCH_DELETE = "/projects/{projectCode}/process-instances/batch-delete";
    //工作流关系
    public static final String LINEAGES = "/projects/{projectCode}/lineages";
    //工作流关系
    public static final String LINEAGES_QUERY_BY_NAME = "/projects/{projectCode}/lineages/query-by-name";
    //任务实例
    public static final String TASK_INSTANCES = "/projects/{projectCode}/task-instances";
    //任务实例强制成功
    public static final String FORCE_SUCCESS = "/{id}/force-success";
    //查看日志
    public static final String LOG_DETAIL = "/log/detail";
    //查看引擎日志
    public static final String LOG_ENGINE_DETAIL = "/log/engineDetail";
    //工作流实例
    public static final String PROCESS_INSTANCES = "/projects/{projectCode}/process-instances";
    //导入工作流时用来生成新的名字
    public static final String IMPORT_SUFFIX = "_import_";
    //海豚数据源新增
    public static final String DATASOURCE = "/datasources";
    //数据源列表
    public static final String DATASOURCE_LIST = "/api/database/datasource/external/list";
    //引入数据源
    public static final String IMPORT_DATASOURCE = "/api/database/datasource/external";
    //批量删除引用数据源
    public static final String DELETE_DATASOURCE = "/api/database/datasource/external/delete/batch";
    //数据源类型条件
    public static final String DATASOURCE_DATATYPE = "/api/database/datasource/info/datatype/condition";
    //停止工作流
    public static final String STOP_PROCESS = "DS_STOP_PROCESS_V1_R2P1";
    //清理试运行工作流
    public static final String CLEAN_TEMP_PROCESS = "/projects/projectCode/process-definition/clean?ignorePermissions=true";
    //添加任务组
    public static final String CREATE_TASK_GROUP = "/task-group/create";
    //编辑任务组
    public static final String UPDATE_TASK_GROUP = "/task-group/update";
    //编辑任务组队列中的优先级
    public static final String UPDATE_TASK_GROUP_QUEUE_PRIORITY = "/task-group/modifyPriority";
    //启动任务组队列中的任务
    public static final String TASK_GROUP_QUEUE_FORCE_START = "/task-group/forceStart";
    //启动任务组队列中的任务
    public static final String GET_GROUP_QUEUE_LIST = "/task-group/query-list-by-group-ids";
    //添加工作组
    public static final String CREATE_WORKER_GROUP = "/worker-groups";
    //开启工作组
    public static final String START_TASK_GROUP = "/task-group/start-task-group";
    //关闭工作组
    public static final String CLOSE_TASK_GROUP = "/task-group/close-task-group";
    //删除工作组
    public static final String DELETE_WORKER_GROUP = "/worker-groups/{id}";
    //添加环境管理
    public static final String CREATE_ENVIRONMENT = "/environment/create";
    //编辑环境管理
    public static final String UPDATE_ENVIRONMENT = "/environment/update";
    //删除环境管理
    public static final String DELETE_ENVIRONMENT = "/environment/delete";
    //预览文件
    public static final String PREVIEW_FILE = "/taskExternal/task-definition/previewFile";
    //预览文件
    public static final String MONITOR_MASTERS = "/monitor/masters";
    //预览文件
    public static final String MONITOR_WORKERS = "/monitor/workers";
    //批量运行工作流
    public static final String EXECUTORS_START_PROCESS_INSTANCE_BATCH = "/projects/{projectCode}/executors/start-process-instance-batch";

}
