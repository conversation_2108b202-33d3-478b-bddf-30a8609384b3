package com.joyadata.scc.util;

import com.alibaba.fastjson.JSON;
import com.joyadata.cms.model.User;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.scc.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * Created by l<PERSON><PERSON>jun on 2022/11/23.
 */
@Slf4j
@Component
public class RedisUtils {

    @Value("${dolphinscheduler}")
    public void inject(String s) {
        dolphinscheduler = s;
    }

    private static String dolphinscheduler;
    private static String DS_NAME_KEY = "CINDASC:DS:TOKEN:NAME:";
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    HttpRequestFeignService httpRequestFeignService;
    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);

    public static RedisUtils redisUtils;
    public static JoyaFeignService<User> userService;

    @PostConstruct
    public void init() {
        redisUtils = this;
        redisUtils.redisTemplate = this.redisTemplate;
        redisUtils.httpRequestFeignService = this.httpRequestFeignService;
        userService = this.userJoyaFeignService;
    }

//    public static User getUserById(String userId) {
//        String token = login("admin", "dolphinscheduler123");
//        String url = dolphinscheduler + "/users/" + userId;
//        Map<String, Object> headers = new HashMap<>();
//        headers.put(Constants.SESSION_ID, token);
//        JSONObject json = redisUtils.httpRequestFeignService.get4url(url, null, headers, JSONObject.class);
//        if (json.getInteger("code") == 0) {
//            JSONObject data = json.getJSONObject("data");
//            if (null == data) {
//                return null;
//            }
//            User user = new User();
//            user.setId(data.getString("id"));
//            user.setUsername(data.getString("userName"));
//            return user;
//        }
//        return null;
//    }

    public static String setTokenInfo(User user) {
        String password = userService.setIgnoreTenantCode().setIgnorePermissions().getQuery().eq("id", user.getId()).fixeds("password").oneValue("password", String.class);
        String dsToken = login(user.getUsername(), password);
        return dsToken;
    }

    public static String login(String userName, String password) {
        /*String token = redisUtils.redisTemplate.opsForValue().get(DS_NAME_KEY + userName);
        if (null != token) {
            return token;
        }*/
        String url = null;
        try {
            url = dolphinscheduler + Constants.LOGIN + "?userName=" + URLEncoder.encode(userName, "UTF-8") + "&userPassword=" + URLEncoder.encode(password, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("调度服务调用失败{}", e.getMessage());
            throw new AppErrorException("调度服务调用失败! errorMsg=" + e);
        }
        Map loginResult;
        try {
            loginResult = redisUtils.httpRequestFeignService.post4url(url, null, null, Map.class);
        } catch (Exception e) {
            log.error("调度服务调用失败{}", e.getMessage());
            throw new AppErrorException("调度服务调用失败!");
        }
        if (Integer.valueOf(String.valueOf(loginResult.get("code"))) == 0) {
            Map map = JSON.parseObject(loginResult.get("data").toString(), Map.class);
            String ctoken = map.get("sessionId").toString();
            //redisUtils.redisTemplate.opsForValue().set(DS_NAME_KEY + userName, ctoken, redisUtils.timeout, TimeUnit.MINUTES);
            return ctoken;
        }
        return null;
    }


}
