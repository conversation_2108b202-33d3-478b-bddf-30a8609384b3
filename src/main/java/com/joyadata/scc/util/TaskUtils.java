package com.joyadata.scc.util;

import com.joyadata.scc.model.ProcessDefinition;
import com.joyadata.scc.model.Task;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
public class TaskUtils {
    public static String TASK_BINDING_INFO = "SCC_TASK_BINDING_INFO_V1_R2P1";

    public static Map<String, List<String>> getTaskIds(ProcessDefinition orgProcessDefinition, ProcessDefinition destProcessDefinition) {
        if (null != orgProcessDefinition && null == destProcessDefinition) {//删除
            List<String> list = orgProcessDefinition.getTaskList().stream().map(Task::getId).collect(Collectors.toList());
            Map<String, List<String>> taskIds = new HashMap<>();
            taskIds.put("delete", list);
            return taskIds;
        } else if (null == orgProcessDefinition && null != destProcessDefinition) {//新增
            List<String> list = destProcessDefinition.getTaskList().stream().map(Task::getId).collect(Collectors.toList());
            Map<String, List<String>> taskIds = new HashMap<>();
            taskIds.put("insert", list);
            return taskIds;
        } else if (null != orgProcessDefinition && null != destProcessDefinition) {//更新
            List<String> orgList = orgProcessDefinition.getTaskList().stream().map(Task::getId).collect(Collectors.toList());//1,2,3

            List<String> destList = destProcessDefinition.getTaskList().stream().map(Task::getId).collect(Collectors.toList());//2,3,4
            List<String> updateList = removeDuplicatesFromDest(orgList, destList);
            Map<String, List<String>> taskIds = new HashMap<>();
            taskIds.put("insert", destList);
            taskIds.put("update", updateList);
            taskIds.put("delete", orgList);
            return taskIds;
        }
        return null;
    }

    public static List<String> removeDuplicatesFromDest(List<String> orgList, List<String> destList) {
        /*List<String> duplicatesInDest = orgList.stream()
                .filter(destList::contains)
                .collect(Collectors.toList());
        duplicatesInDest.forEach(destList::remove);*/
        List<String> commonElements = orgList.stream()
                .filter(destList::contains)
                .collect(Collectors.toList());
        orgList.removeAll(commonElements);
        destList.removeAll(commonElements);

        return commonElements;
    }
}
