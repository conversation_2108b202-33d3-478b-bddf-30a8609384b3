package com.joyadata.scc.util;

import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * MDC工具类，用于在不同层之间传递MDC上下文
 */
public class MDCUtil {
    
    /**
     * 获取当前MDC上下文
     */
    public static Map<String, String> getCurrentMDC() {
        return MDC.getCopyOfContextMap();
    }
    
    /**
     * 设置MDC上下文
     */
    public static void setMDC(Map<String, String> contextMap) {
        if (contextMap != null) {
            MDC.setContextMap(contextMap);
        }
    }
    
    /**
     * 在指定的MDC上下文中执行代码块
     */
    public static void runWithMDC(Map<String, String> contextMap, Runnable runnable) {
        Map<String, String> originalContext = getCurrentMDC();
        try {
            setMDC(contextMap);
            runnable.run();
        } finally {
            if (originalContext != null) {
                setMDC(originalContext);
            } else {
                MDC.clear();
            }
        }
    }
    
    /**
     * 在指定的MDC上下文中执行代码块并返回结果
     */
    public static <T> T callWithMDC(Map<String, String> contextMap, Callable<T> callable) throws Exception {
        Map<String, String> originalContext = getCurrentMDC();
        try {
            setMDC(contextMap);
            return callable.call();
        } finally {
            if (originalContext != null) {
                setMDC(originalContext);
            } else {
                MDC.clear();
            }
        }
    }
    
    /**
     * 检查当前是否有MDC上下文
     */
    public static boolean hasMDC() {
        return MDC.getCopyOfContextMap() != null && !MDC.getCopyOfContextMap().isEmpty();
    }
    
    /**
     * 获取当前批次号
     */
    public static String getCurrentBatchNo() {
        return MDC.get("batchNo");
    }
    
    /**
     * 获取当前租户代码
     */
    public static String getCurrentTenantCode() {
        return MDC.get("tenantCode");
    }
    
    /**
     * 打印当前MDC信息（用于调试）
     */
    public static void printCurrentMDC() {
        Map<String, String> contextMap = getCurrentMDC();
        if (contextMap != null && !contextMap.isEmpty()) {
            System.out.println("当前MDC上下文: " + contextMap);
        } else {
            System.out.println("当前没有MDC上下文");
        }
    }
}
