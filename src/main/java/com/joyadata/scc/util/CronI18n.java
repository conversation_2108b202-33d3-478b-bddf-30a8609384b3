package com.joyadata.scc.util;

import com.joyadata.scc.enums.Language;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class CronI18n {
    private static final Map<Language, Map<String, Map<String, String>>> resources = new HashMap<>();
    
    static {
        // 中文(简体)资源
        Map<String, Map<String, String>> zhResources = new HashMap<>();
        
        Map<String, String> zhCornMin = new HashMap<>();
        zhCornMin.put("type1", "每分钟一次");
        zhCornMin.put("type2", "每两分钟一次");
        zhCornMin.put("type3", "每五分钟一次");
        zhCornMin.put("type4", "每十分钟一次");
        zhCornMin.put("type5", "每三十分钟一次");
        
        Map<String, String> zhCornHour = new HashMap<>();
        zhCornHour.put("type1", "每小时一次");
        zhCornHour.put("type2", "每两小时一次");
        zhCornHour.put("type3", "每三小时一次");
        zhCornHour.put("type4", "每六小时一次");
        zhCornHour.put("type5", "每十二小时一次");
        
        Map<String, String> zhCornDay = new HashMap<>();
        zhCornDay.put("type1", "每天一次");
        zhCornDay.put("type2", "一月一次");
        zhCornDay.put("type2Desc", "每月1号0点：0 0 0 1 * ?每月最后一天0点：0 0 0 L * ?");
        zhCornDay.put("type3", "一周一次");
        zhCornDay.put("type3Desc", "每周天0点");
        zhCornDay.put("type2Desc1", "自每月第一天凌晨零点起，启动执行相关任务");
        zhCornDay.put("type3Desc1", "自每周一凌晨零点起，启动执行相关任务");
        
        zhResources.put("cornMin", zhCornMin);
        zhResources.put("cornHour", zhCornHour);
        zhResources.put("cornDay", zhCornDay);
        
        // 英文(美国)资源
        Map<String, Map<String, String>> enResources = new HashMap<>();
        
        Map<String, String> enCornMin = new HashMap<>();
        enCornMin.put("type1", "Once a minute");
        enCornMin.put("type2", "Every two minutes");
        enCornMin.put("type3", "Every five minutes");
        enCornMin.put("type4", "Every ten minutes");
        enCornMin.put("type5", "Every thirty minutes");
        
        Map<String, String> enCornHour = new HashMap<>();
        enCornHour.put("type1", "Once an hour");
        enCornHour.put("type2", "Every two hours");
        enCornHour.put("type3", "Every three hours");
        enCornHour.put("type4", "Every six hours");
        enCornHour.put("type5", "Every twelve hours");
        
        Map<String, String> enCornDay = new HashMap<>();
        enCornDay.put("type1", "Once a day");
        enCornDay.put("type2", "Once a month");
        enCornDay.put("type2Desc", "Midnight on the 1st of each month: 0 0 0 1 * ? Midnight on the last day of each month: 0 0 0 L * ?");
        enCornDay.put("type3", "Once a week");
        enCornDay.put("type3Desc", "Midnight every Sunday");
        enCornDay.put("type2Desc1", "Starting from midnight on the first day of each month, execute related tasks");
        enCornDay.put("type3Desc1", "Starting from midnight every Monday, execute related tasks");
        
        enResources.put("cornMin", enCornMin);
        enResources.put("cornHour", enCornHour);
        enResources.put("cornDay", enCornDay);
        
        resources.put(Language.ZH_CN, zhResources);
        resources.put(Language.EN_US, enResources);
    }
    
    public static String getText(Language language, String category, String key) {
        return resources.getOrDefault(language, resources.get(Language.EN_US))
                       .getOrDefault(category, Collections.emptyMap())
                       .getOrDefault(key, "");
    }
}