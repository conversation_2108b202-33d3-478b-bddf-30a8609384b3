package com.joyadata.scc.util;

import ch.qos.logback.classic.LoggerContext;
import com.joyadata.util.ApplicationContextHelp;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 日志读取工具类
 */
@Slf4j
public class LogReaderUtil {

    /**
     * 获取logback配置的日志路径
     */
    public static String getLogBasePath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (null != os && !os.contains("win")) {
            return ApplicationContextHelp.getRuntimeWorkDir() + "/logs";
        }
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        String configuredPath = loggerContext.getProperty("log.path");

        if (configuredPath != null) {
            return configuredPath;
        } else {
            // 如果没有配置，使用默认路径
            return System.getProperty("user.dir") + "/logs";
        }
    }

    /**
     * 构建批次日志文件路径
     */
    public static String getBatchLogPath(String tenantCode, String batchNo) {
        return String.format("%s/%s/%s.log", getLogBasePath(), tenantCode, batchNo);
    }

    /**
     * 根据行号范围读取日志
     *
     * @param filePath  日志文件路径
     * @param startLine 开始行号（从1开始）
     * @param endLine   结束行号（包含）
     * @return 日志行列表
     */
    public static List<String> readLogByLineRange(String filePath, int startLine, int endLine) {
        List<String> result = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            int currentLine = 1;

            while ((line = reader.readLine()) != null) {
                if (currentLine >= startLine && currentLine <= endLine) {
                    result.add(line);
                }
                if (currentLine > endLine) {
                    break;
                }
                currentLine++;
            }

        } catch (IOException e) {
            log.error("读取日志文件失败: {}", filePath, e);
        }

        return result;
    }

    /**
     * 获取日志文件总行数
     *
     * @param filePath 日志文件路径
     * @return 总行数
     */
    public static int getTotalLines(String filePath) {
        int count = 0;

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            while (reader.readLine() != null) {
                count++;
            }
        } catch (IOException e) {
            log.error("读取日志文件行数失败: {}", filePath, e);
        }

        return count;
    }

    /**
     * 检查任务是否已结束（查找结束标识）
     *
     * @param filePath 日志文件路径
     * @return 是否已结束
     */
    public static boolean isTaskCompleted(String filePath) {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 检查明确的结束标识
                if (line.contains("批次处理完成") ||
                        line.contains("tar包创建成功") ||
                        line.contains("临时文件已清理")) {
                    return true;
                }
            }
        } catch (IOException e) {
            log.error("检查任务完成状态失败: {}", filePath, e);
        }

        return false;
    }

    /**
     * 获取日志文件信息
     *
     * @param filePath 日志文件路径
     * @return 文件信息
     */
    public static LogFileInfo getLogFileInfo(String filePath) {
        File file = new File(filePath);
        LogFileInfo info = new LogFileInfo();

        if (!file.exists()) {
            info.setExists(false);
            return info;
        }

        info.setExists(true);
        info.setFilePath(filePath);
        info.setFileSize(file.length());
        info.setLastModified(file.lastModified());
        info.setTotalLines(getTotalLines(filePath));
        info.setCompleted(isTaskCompleted(filePath));

        return info;
    }

    /**
     * 日志文件信息类
     */
    public static class LogFileInfo {
        private boolean exists;
        private String filePath;
        private long fileSize;
        private long lastModified;
        private int totalLines;
        private boolean completed;

        // Getters and Setters
        public boolean isExists() {
            return exists;
        }

        public void setExists(boolean exists) {
            this.exists = exists;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }

        public long getLastModified() {
            return lastModified;
        }

        public void setLastModified(long lastModified) {
            this.lastModified = lastModified;
        }

        public int getTotalLines() {
            return totalLines;
        }

        public void setTotalLines(int totalLines) {
            this.totalLines = totalLines;
        }

        public boolean isCompleted() {
            return completed;
        }

        public void setCompleted(boolean completed) {
            this.completed = completed;
        }
    }
}
