spring:
  main:
    allow-bean-definition-overriding: true
  aop:
    proxy-target-class: true
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      enabled: true
mybatis-plus:
  mapperLocations: classpath*:mapper/**/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    autoMappingBehavior: PARTIAL
    autoMappingUnknownColumnBehavior: NONE
    logImpl: ${app_db_mybatis_sql_log_impl}
  global-config:
    banner: false
    dbConfig:
      idType: NONE

management:
  endpoints:
    web:
      exposure:
        include: "*"