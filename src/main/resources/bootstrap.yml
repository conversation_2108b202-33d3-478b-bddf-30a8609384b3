# 引用的插件
plugins: @app.plugins@
# 额外暴露的端口
exports: @app.exports@
# 服务入口类
main-calss: @app.main.class@
#服务端口
server:
  port: @app.port@
#spring 相关配置
spring:
  main:
    allow-bean-definition-overriding: true
  aop:
    proxy-target-class: true
  application:
    name: @spring.application.name@
    version: @spring.application.version@
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      server-addr: @nacos.server-addr@:@nacos.server-port@
      discovery:
        # 注册组
        namespace: @nacos.namespace@
        group: @nacos.group@
        username: @nacos.username@
        password: @nacos.password@
        enabled: @nacos.discovery.enabled@
        register-enabled: @nacos.discovery.enabled@
        heart-beat-timeout: 15000
      config:
        timeout: 15000
        namespace: @nacos.namespace@
        group: @nacos.group@
        username: @nacos.username@
        password: @nacos.password@
        enabled: @nacos.config.enabled@
        refresh-enabled: @nacos.config.enabled@
        # default
        shared-configs[0]:
          order: 0
          group: @nacos.group@
          data-id: dedp-default.yml
          refresh: @nacos.config.enabled@
        # app
        shared-configs[1]:
          order: 1
          group: @nacos.group@
          data-id: @spring.application.name@.yml
          refresh: @nacos.config.enabled@
        # comm
        shared-configs[2]:
          order: 2
          group: @nacos.group@
          data-id: dedp-comm.yml
          refresh: @nacos.config.enabled@
jasypt:
  encryptor:
    # 加密算法
    algorithm: @app.jasypt.encryptor.algorithm@
    # 加密使用的盐
    password: @app.jasypt.encryptor.password@