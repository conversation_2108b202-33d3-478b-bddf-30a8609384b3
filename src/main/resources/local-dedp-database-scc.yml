# module
project: dedp
module: scc
version: v1

# app
app_log_enable: ${default_log_enable:false}
app_autoddl_enable: true
app_bean_event_enable: ${default_bean_event_enable:false}
app_auth_enable: ${default_auth_enable:true}
app_gateway_http: http://**************:8858

# db
app_db_type: mysql
app_db_pool_type: ${default_mysql_db_pool_type:com.alibaba.druid.pool.DruidDataSource}
app_db_pool_idle: ${default_mysql_db_pool_idle:3}
app_db_pool_size: ${default_mydefault_mysql_db_pool_size:20}
app_db_ip: **************
app_db_port: ${default_mysql_db_port:3306}
app_db_dbname: business_scc
app_db_username: ${default_mysql_db_user:root}
app_db_password: ${default_mysql_db_password:<PERSON><PERSON><PERSON>fa_123456}
app_db_driver_class_name: ${default_mysql_driver_class_name:com.mysql.cj.jdbc.Driver}
app_db_url_advanced_parameter: ${default_mysql_url_advanced_parameter:}
#app_db_mybatis_sql_log_impl: ${default_db_mybatis_sql_log_impl:org.apache.ibatis.logging.slf4j.Slf4jImpl}
app_db_mybatis_sql_log_impl: org.apache.ibatis.logging.stdout.StdOutImpl
default_db_mybatis_sql_log_impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

#kafka
app_kafka_enable: ${default_kafka_enable:false}
app_kafka_ips: ${default_kafak_ips:127.0.0.1:9200}
app_kafka_enable_jaas: true
app_kafka_user_jaas: admin
app_kafka_pwd_jaas: Cdyanfa_123456

#redis
app_redis_enable: ${default_redis_enable:false}
app_redis_type: ${default_redis_type:single}
app_redis_ips: ${default_redis_ips:127.0.0.1:6379}
app_redis_password: ${default_redis_password:Cdyanfa_123456}
app_redis_database: ${default_redis_database:0}

# custom
dolphinscheduler: http://dscheduler.dsg.com:12345/dolphinscheduler
#  判断是否需要拼接日切时间（浙江农信需求）
cutoff_flag: false

# 导出配置
export_base_path: /tmp/export  # 导出文件基础路径，可根据环境调整