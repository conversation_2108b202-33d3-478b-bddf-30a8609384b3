#!/bin/bash

# 初始化startParams的JSON数据为空对象
startParams="{"

# 如果parameters.txt存在且不为空，则读取文件内容
if [ -s "parameters.txt" ]; then
    while IFS="=" read -r key value; do
        # 验证每行是否符合key=value格式
        if [[ -z "$key" || -z "$value" ]]; then
            echo "警告: 发现不符合规范的行 (key=value)，已跳过"
            continue
        fi

        # 如果startParams中已有内容（即不是初始的空对象），则添加逗号分隔
        [[ "$startParams" == "{" ]] || startParams+=","

        # 将读取的键值对加入到startParams中
        startParams+="\"$key\":\"$value\""
    done < "parameters.txt"
fi

# 检查至少提供了processDefinitionName
if [ "$#" -lt 1 ]; then
    echo "使用方法: $0 \"processDefinitionId\" \"额外的key=value参数，多个用&分割\""
    exit 1
fi

# 外部传入的processDefinitionName参数
input_parameter="$1"
shift # 移除第一个参数

# 解析剩余的复合参数字符串为多个key=value对
IFS='&' read -ra kv_pairs <<< "$1"
for pair in "${kv_pairs[@]}"; do
    IFS='=' read -r key value <<< "$pair"
    # 验证key和value是否有效
    if [[ -z "$key" || -z "$value" ]]; then
        echo "错误: 传入的额外参数不符合key=value格式"
        exit 1
    fi

    # 如果startParams中已有内容（即使是初始的空对象，此处也视为已有内容），则添加逗号分隔
    [[ "$startParams" == "{" ]] || startParams+=","
    startParams+="\"$key\":\"$value\""
done

# 关闭startParams的JSON对象（实际上已经是关闭状态，这里是为了逻辑上的完整性）
startParams+="}"

# 构造完整的请求数据
data="{\"id\":\"$input_parameter\",\"startParams\":$startParams}"

# 目标URL
url="http://gateway.dsg.com:8858/dedp/v1/scc/externalExpansion/startProcess?assetToken=ZHNnLDEyOTEzNzQ5NTI5NDcyLGRzZ195ZiwxMjkxMzc0ODk2NzA0MA=="

# 打印请求体
# echo "请求体=$data"

# 使用curl发送POST请求，将构造的JSON作为请求体内容
curl_response=$(curl -s -X POST -H "Content-Type: application/json" -d "$data" "$url")

# 打印curl的响应
echo "Curl响应:"
echo "$curl_response"