#!/bin/bash

# 检查是否传入了参数
if [ "$#" -ne 1 ]; then
    echo "使用方法: $0 \"你的参数\""
    exit 1
fi

# 外部传入的参数
input_parameter="$1"

# 目标URL
url="http://gateway.dsg.com:8858/dedp/v1/scc/externalExpansion/getProcessInstanceState?assetToken=ZHNnLDEyOTEzNzQ5NTI5NDcyLGRzZ195ZiwxMjkxMzc0ODk2NzA0MA=="

# 构造请求数据，这里假设我们直接将参数作为请求体发送
data="{\"id\":\"$input_parameter\"}"

# 使用curl发送POST请求，将参数作为请求体内容
curl_response=$(curl -s -X POST -H "Content-Type: application/json" -d "$data" "$url")

# 打印curl的响应
echo "Curl响应:"
echo "$curl_response"