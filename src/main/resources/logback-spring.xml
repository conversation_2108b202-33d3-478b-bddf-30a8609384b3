<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <!-- 日志存放路径 -->
    <property name="log.path" value="logs"/>
    <!-- 日志输出格式 -->
    <property name="console.log.pattern"
              value="-|%d{yyyy-MM-dd HH:mm:ss.SSS}|%.-5level|${APP_PID:-0}|%thread|%logger{36}.%M:%L|%msg%n"/>
    <property name="log.pattern"
              value="-|%d{yyyy-MM-dd HH:mm:ss.SSS}|%.-5level|${APP_PID:-0}|%thread|%logger{36}.%M:%L|%msg%n"/>
    <!-- 日志编码 -->
    <property name="charset" value="utf-8"/>
    <!-- 日志存储时间与大小 -->
    <property name="maxFileSize"
              value="10MB"/>
    <property name="maxHistory"
              value="7"/>
    <property name="totalSizeCap"
              value="3GB"/>
    <property name="cleanHistoryOnStart"
              value="true"/>
    <!-- 平台框架日志 debug: 打印细节信息， info 打印关键信息， error: 打印错误信息 -->
    <property name="dedpLogLevel"
              value="INFO"/>
    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <!-- 日志级别排序为： TRACE < DEBUG < INFO < WARN < ERROR -->
    <!-- 系统日志输出-info,warn,error -->
    <!-- 批次日志输出 - 基于MDC的batchNo分离日志 -->
    <appender name="batch_sift" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator class="ch.qos.logback.classic.sift.MDCBasedDiscriminator">
            <key>logFilePath</key>
            <defaultValue>unknown/unknown</defaultValue>
        </discriminator>
        <sift>
            <appender name="batch-${logFilePath}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${log.path}/${logFilePath}.log</file>
                <append>true</append>
                <prudent>false</prudent>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${log.path}/${logFilePath}.%i.log</fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%.-5level|%thread|%logger{10}.%M:%L|%msg%n
                    </pattern>
                    <charset>${charset}</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="file_warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>
    <appender name="nacos_property_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/nacos_property_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/nacos_property_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="nacos_config_service_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/nacos_config_service_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/nacos_config_service_debug.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="nacos_config_client_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/nacos_config_client_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/nacos_config_client_debug.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="nacos_common_client_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/nacos_common_client_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/nacos_common_client_debug.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="kafka_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/kafka_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/kafka_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="redis_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/redis_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/redis_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="feign_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/feign_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/feign_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="scheduled_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/scheduled_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/scheduled_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <appender name="sql_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sql_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/sql_debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
    </appender>
    <!-- 系统日志输出-sql -->
    <appender name="file_sql" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/sql.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>${cleanHistoryOnStart}</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${charset}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <!--专门针对SQL打印内容输出一个文件-->
    <logger name="p6spy" level="info" additivity="false">
        <appender-ref ref="file_sql"/>
    </logger>
    <!-- nacos 配置 -->
    <logger name="com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="nacos_property_debug"/>
    </logger>
    <logger name="com.alibaba.nacos.client.config.NacosConfigService" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="nacos_config_service_debug"/>
    </logger>
    <logger name="com.alibaba.nacos.client.config.impl.ClientWorker" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="nacos_config_client_debug"/>
    </logger>
    <logger name="com.alibaba.nacos.common.remote.client" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="nacos_common_client_debug"/>
    </logger>
    <!-- kafak 的发送消费日志 -->
    <logger name="com.joyadata.cores.kafka.start.KafkaStartLogger" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="kafka_debug"/>
    </logger>
    <!-- 框架redis调用 的日志 -->
    <logger name="com.joyadata.cores.redis.start.RedisStartLogger" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="redis_debug"/>
    </logger>
    <!-- 框架feign调用 日志  -->
    <logger name="com.joyadata.cores.feign.start.FeignStartLogger" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="feign_debug"/>
    </logger>
    <!-- 框架scheduled调用 日志  -->
    <logger name="com.joyadata.cores.scheduled.start.ScheduledStartLogger" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="scheduled_debug"/>
    </logger>
    <!-- 框架sql执行 的日志 -->
    <logger name="com.joyadata.cores.SqlProviderLogger" level="${dedpLogLevel}" additivity="false">
        <appender-ref ref="sql_debug"/>
    </logger>

    <!-- 导入导出任务记录控制器 - 批次日志分离 -->
    <logger name="com.joyadata.scc.controller.ImportExportTaskRecordController" level="INFO" additivity="false">
        <appender-ref ref="batch_sift"/>
    </logger>

    <!-- 导入导出相关Service - 批次日志分离 -->
    <logger name="com.joyadata.scc.service" level="INFO" additivity="false">
        <appender-ref ref="batch_sift"/>
    </logger>

    <!-- 剔除不需要的日志 -->
    <logger name="com.hazelcast" level="ERROR"/>
    <logger name="org.eclipse.jetty" level="ERROR"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="io.lettuce.core" level="ERROR"/>
    <logger name="org.apache.kafka" level="ERROR"/>
    <!-- 日志级别设置 -->
    <root level="info">
        <!-- 本地调试无控制台日志，需要开启console打印 -->
        <springProfile name="local">
            <appender-ref ref="console"/>
        </springProfile>
        <appender-ref ref="file_info"/>
        <appender-ref ref="file_warn"/>
        <appender-ref ref="file_error"/>
    </root>
</configuration>
