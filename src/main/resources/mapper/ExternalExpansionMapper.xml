<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joyadata.scc.mapper.ExternalExpansionMapper">

    <select id="getProcessDefinitionList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        *
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.name ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        GROUP_CONCAT( DISTINCT select_table.datasource_id ) AS datasource_id,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.business_name END ) AS source_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.business_name END ) AS target_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.simple_name END ) AS source_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.simple_name END ) AS target_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN select_table.table_name END ) AS source_table_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN select_table.table_name END ) AS target_table_name
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON FIND_IN_SET( td2.task_id, dp.task_ids )> 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.datasourceId' )) AS datasource_id,
        CASE
        WHEN JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) IS NULL
        OR JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) = '' THEN
        '落成文本' ELSE JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' ))
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        td1.create_time DESC
        ) select_table
        WHERE
        1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
        limit #{page},#{pager}
    </select>

    <select id="getProcessDefinitionListForPg" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        *
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.name ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        string_agg( DISTINCT select_table.datasource_id::text, ',') AS datasource_id,,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.business_name END , ',') AS source_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.business_name END , ',') AS target_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.simple_name END , ',') AS source_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.simple_name END , ',') AS target_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN select_table.table_name END , ',') AS source_table_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN select_table.table_name END , ',') AS target_table_name
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON strpos(',' || dp.task_ids || ',', ',' || td2.task_id || ',') > 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        (CAST(plugin_basics_config AS json)->>'datasourceId') AS datasource_id,
        CASE
        WHEN CAST(plugin_basics_config AS json)->>'tableName' IS NULL
        OR CAST(plugin_basics_config AS json)->>'tableName' = '' THEN
        '落成文本'
        ELSE CAST(plugin_basics_config AS json)->>'tableName'
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        MIN( td1.create_time ) DESC
        ) select_table
        WHERE
        1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
        limit #{pager} OFFSET #{page}
    </select>

    <select id="getProcessDefinitionListAndRunState" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        *
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.NAME ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        GROUP_CONCAT( DISTINCT select_table.datasource_id ) AS datasource_id,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.business_name END ) AS source_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.business_name END ) AS target_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.simple_name END ) AS source_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.simple_name END ) AS target_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN select_table.table_name END ) AS source_table_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN select_table.table_name END ) AS target_table_name,
        SUBSTRING_INDEX( GROUP_CONCAT( pi.state ORDER BY pi.start_time DESC ), ',', 1 ) AS last_task_state,
        SUBSTRING_INDEX( GROUP_CONCAT( pi.start_time ORDER BY pi.start_time DESC ), ',', 1 ) last_task_start_time,
        SUBSTRING_INDEX( GROUP_CONCAT( pi.end_time ORDER BY pi.start_time DESC ), ',', 1 ) last_task_end_time,
        MIN(pi.id) AS process_instance_id
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON FIND_IN_SET( td2.task_id, dp.task_ids )> 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.datasourceId' )) AS datasource_id,
        CASE

        WHEN JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) IS NULL
        OR JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) = '' THEN
        '落成文本' ELSE JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' ))
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        LEFT JOIN business_ds.t_ds_process_instance pi ON pi.process_definition_code = dp.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        td1.create_time DESC
        ) select_table where 1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="taskState != null and taskState != ''">
            AND last_task_state = #{taskState, jdbcType=VARCHAR}
        </if>
        <if test="taskStartTime != null and taskStartTime != ''">
            AND last_task_start_time &lt;= #{taskStartTime, jdbcType=VARCHAR}
        </if>
        limit #{page},#{pager}
    </select>
    <select id="getProcessDefinitionListAndRunStateForPg" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        *
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.NAME ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        string_agg( DISTINCT select_table.datasource_id::text, ',') AS datasource_id,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.business_name END , ',') AS source_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.business_name END , ',') AS target_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.simple_name END , ',') AS source_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.simple_name END , ',') AS target_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN select_table.table_name END , ',') AS source_table_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN select_table.table_name END , ',') AS target_table_name,
        split_part(string_agg(pi.state::text, ',' ORDER BY pi.start_time DESC), ',', 1) AS last_task_state,
        split_part( string_agg( pi.start_time::text, ',' ORDER BY pi.start_time DESC ), ',', 1 ) last_task_start_time,
        split_part( string_agg( pi.end_time::text, ',' ORDER BY pi.start_time DESC ), ',', 1 ) last_task_end_time,
        MIN(pi.id) AS process_instance_id
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON strpos(',' || dp.task_ids || ',', ',' || td2.task_id || ',')> 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        (CAST(plugin_basics_config AS json)->>'datasourceId') AS datasource_id,
        CASE
        WHEN CAST(plugin_basics_config AS json)->>'tableName' IS NULL
        OR CAST(plugin_basics_config AS json)->>'tableName' = '' THEN
        '落成文本'
        ELSE CAST(plugin_basics_config AS json)->>'tableName'
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        LEFT JOIN business_ds.t_ds_process_instance pi ON cast(pi.process_definition_code as varchar) = dp.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        MIN( td1.create_time ) DESC
        ) select_table where 1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="taskState != null and taskState != ''">
            AND last_task_state = #{taskState, jdbcType=VARCHAR}
        </if>
        <if test="taskStartTime != null and taskStartTime != ''">
            AND last_task_start_time &lt;= #{taskStartTime, jdbcType=VARCHAR}
        </if>
        limit #{pager} OFFSET #{page}
    </select>
    <select id="getInstanceTime" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        end_time,
        id
        FROM
        business_ds.t_ds_process_instance
        WHERE
        id IN
        <foreach collection="processInstanceIds" item="processInstanceId" separator="," open="(" close=")" index="">
            #{processInstanceId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getRWCount" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        process_instance_id,
        SUM( latest_read_row_count ) AS total_read_row_count,
        SUM( latest_write_row_count ) AS total_write_row_count
        FROM
        (
        SELECT
        pi.id AS process_instance_id,
        SUBSTRING_INDEX( GROUP_CONCAT( sejm.read_row_count ORDER BY sejm.create_time DESC ), ',', 1 ) AS latest_read_row_count,
        SUBSTRING_INDEX( GROUP_CONCAT( sejm.write_row_count ORDER BY sejm.create_time DESC ), ',', 1 ) AS latest_write_row_count
        FROM
        business_ds.t_ds_process_instance pi
        LEFT JOIN business_ds.t_ds_task_instance ti ON ti.process_instance_id = pi.id
        LEFT JOIN business_scc.scc_engine_job_metrics sejm ON ti.id = sejm.instance_id
        WHERE
        pi.id IN
        <foreach collection="processInstanceIds" item="processInstanceId" separator="," open="(" close=")" index="">
            #{processInstanceId,jdbcType=VARCHAR}
        </foreach>
        GROUP BY
        ti.id
        ) AS subquery;

    </select>
    <select id="getRWCountForPg" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        max(process_instance_id) as process_instance_id,
        SUM( latest_read_row_count::bigint ) AS total_read_row_count,
        SUM( latest_write_row_count::bigint ) AS total_write_row_count
        FROM
        (
        SELECT
        max(pi.id) AS process_instance_id,
        split_part( string_agg( sejm.read_row_count::text, ',' ORDER BY sejm.create_time DESC ), ',', 1 ) AS latest_read_row_count,
        split_part( string_agg( sejm.write_row_count::text, ',' ORDER BY sejm.create_time DESC ), ',', 1 ) AS latest_write_row_count
        FROM
        business_ds.t_ds_process_instance pi
        LEFT JOIN business_ds.t_ds_task_instance ti ON ti.process_instance_id = pi.id
        LEFT JOIN business_scc.scc_engine_job_metrics sejm ON ti.id = sejm.instance_id
        WHERE
        pi.id IN
        <foreach collection="processInstanceIds" item="processInstanceId" separator="," open="(" close=")" index="">
            #{processInstanceId,jdbcType=VARCHAR}
        </foreach>
        GROUP by
        ti.id
        ) AS subquery;

    </select>
    <select id="getProcessInstanceById" resultType="com.joyadata.scc.dto.ningbo.ProcessInstanceDTO">
        SELECT
        *
        FROM
        (
        SELECT
        min( business_integration.di_process_instance.id ) AS id,
        min( business_integration.task_catalogue.`name` ) AS catalogue_name,
        min( business_integration.di_process_instance.process_name ) AS process_definition_name,
        min( business_integration.di_process_instance.state ) AS state,
        CAST( min( business_integration.di_process_instance.start_time ) AS CHAR ) AS start_time,
        CAST( min( business_integration.di_process_instance.end_time ) AS CHAR ) AS end_time,
        sum( business_scc.scc_engine_job_metrics.read_row_count ) AS read_row_count,
        sum( business_scc.scc_engine_job_metrics.write_row_count ) AS write_row_count
        FROM
        business_integration.di_process_instance
        LEFT JOIN business_integration.task_catalogue ON business_integration.di_process_instance.task_catalogue_id = business_integration.task_catalogue.catalogue_id
        LEFT JOIN business_integration.di_task_instance ON business_integration.di_task_instance.process_instance_id = business_integration.di_process_instance.id
        LEFT JOIN business_scc.scc_engine_job_metrics ON business_scc.scc_engine_job_metrics.instance_id = business_integration.di_task_instance.id
        WHERE
        business_integration.di_process_instance.process_definition_id = #{processDefinitionCode,jdbcType=VARCHAR}
        AND business_integration.di_process_instance.tenant_code = #{tenantCode,jdbcType=VARCHAR}
        GROUP BY
        business_integration.di_process_instance.id
        ) table_a where 1=1
        <if test="startTime != null and startTime != ''">
            AND start_time &gt;= #{startTime, jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != ''">
            AND end_time &lt;=  #{endTime, jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            AND state = #{state, jdbcType=VARCHAR}
        </if>
        ORDER BY
        start_time DESC
        <if test="page != null and pager != null">
            limit #{page},#{pager}
        </if>


    </select>
    <select id="getProcessDefinitionById" resultType="com.alibaba.fastjson.JSONObject">
        SELECT dip.id       AS process_definition_code,
               dit.NAME     AS process_definition_name,
               dtc.NAME     AS catalogue_name,
               dip.task_ids as task_ids
        FROM business_integration.di_task dit
                 LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
                 LEFT JOIN business_integration.di_process_definition dip ON dit.id = dip.id
        WHERE dit.id = #{processDefinitionCode, jdbcType=VARCHAR}
          AND dit.tenant_code = #{tenantCode, jdbcType=VARCHAR}
    </select>
    <select id="getTaskList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        business_integration.task_definition_info.task_name AS task_name,
        business_integration.task_definition_info.task_id AS task_id,
        business_integration.task_config_info.plugin_table_structure AS plugin_table_structure,
        business_integration.task_config_info.plugin_advanced_config AS plugin_advanced_config,
        business_integration.task_config_info.plugin_basics_config AS plugin_basics_config
        FROM
        business_integration.task_definition_info
        LEFT JOIN business_integration.task_config_info ON business_integration.task_definition_info.current_version_id
        = business_integration.task_config_info.version_id
        AND business_integration.task_config_info.plugin_classify_id = 1
        WHERE
        business_integration.task_definition_info.task_id IN
        <foreach collection="taskIdList" item="taskId" separator="," open="(" close=")" index="">
            #{taskId,jdbcType=VARCHAR}
        </foreach>
        AND business_integration.task_definition_info.tenant_id=#{tenantCode,jdbcType=VARCHAR}
        AND business_integration.task_definition_info.task_type='0'
    </select>
    <select id="getProcessDefinitionListTotal" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.name ) AS catalogue_name,
        MIN( td1.project_id ) AS project_id,
        MIN( td1.tenant_id ) AS tenant_code,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        GROUP_CONCAT( DISTINCT select_table.datasource_id ) AS datasource_id,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.business_name END ) AS source_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.business_name END ) AS target_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.simple_name END ) AS source_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.simple_name END ) AS target_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN select_table.table_name END ) AS source_table_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN select_table.table_name END ) AS target_table_name
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON FIND_IN_SET( td2.task_id, dp.task_ids )> 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.datasourceId' )) AS datasource_id,
        CASE

        WHEN JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) IS NULL
        OR JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) = '' THEN
        '落成文本' ELSE JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' ))
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        td1.create_time DESC
        ) select_table
        WHERE
        1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
    </select>
    <select id="getProcessDefinitionListTotalForPg" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.name ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        string_agg( DISTINCT select_table.datasource_id::text, ',') AS datasource_id,,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.business_name END , ',') AS source_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.business_name END , ',') AS target_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.simple_name END , ',') AS source_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.simple_name END , ',') AS target_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN select_table.table_name END , ',') AS source_table_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN select_table.table_name END , ',') AS target_table_name
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON strpos(',' || dp.task_ids || ',', ',' || td2.task_id || ',') > 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        (CAST(plugin_basics_config AS json)->>'datasourceId') AS datasource_id,
        CASE
        WHEN CAST(plugin_basics_config AS json)->>'tableName' IS NULL
        OR CAST(plugin_basics_config AS json)->>'tableName' = '' THEN
        '落成文本'
        ELSE CAST(plugin_basics_config AS json)->>'tableName'
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        MIN( td1.create_time ) DESC
        ) select_table
        WHERE
        1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
    </select>
    <select id="getProcessDefinitionListAndRunStateTotal" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.NAME ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        GROUP_CONCAT( DISTINCT select_table.datasource_id ) AS datasource_id,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.business_name END ) AS source_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.business_name END ) AS target_business_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN business_database.datasource_business.simple_name END ) AS source_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN business_database.datasource_business.simple_name END ) AS target_simple_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 1 THEN select_table.table_name END ) AS source_table_name,
        GROUP_CONCAT( DISTINCT CASE WHEN plugin_classify_id = 2 THEN select_table.table_name END ) AS target_table_name,
        SUBSTRING_INDEX( GROUP_CONCAT( pi.state ORDER BY pi.start_time DESC ), ',', 1 ) AS last_task_state,
        SUBSTRING_INDEX( GROUP_CONCAT( pi.start_time ORDER BY pi.start_time DESC ), ',', 1 ) last_task_start_time,
        SUBSTRING_INDEX( GROUP_CONCAT( pi.end_time ORDER BY pi.start_time DESC ), ',', 1 ) last_task_end_time

        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON FIND_IN_SET( td2.task_id, dp.task_ids )> 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.datasourceId' )) AS datasource_id,
        CASE

        WHEN JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) IS NULL
        OR JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' )) = '' THEN
        '落成文本' ELSE JSON_UNQUOTE(
        JSON_EXTRACT( plugin_basics_config, '$.tableName' ))
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        LEFT JOIN business_ds.t_ds_process_instance pi ON pi.process_definition_code = dp.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        td1.create_time DESC
        ) select_table where 1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="taskState != null and taskState != ''">
            AND last_task_state = #{taskState, jdbcType=VARCHAR}
        </if>
        <if test="taskStartTime != null and taskStartTime != ''">
            AND last_task_start_time &lt;= #{taskStartTime, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getProcessDefinitionListAndRunStateTotalForPg" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        (
        SELECT
        MIN( dtc.id ) AS catalogue_id,
        MIN( dtc.NAME ) AS catalogue_name,
        MIN( td1.tenant_id ) AS tenant_code,
        MIN( td1.project_id ) AS project_id,
        COALESCE ( MIN( dis.crontab ), '非工作流定时' ) AS crontab,
        MIN( dp.id ) AS process_definition_code,
        MIN( dp.NAME ) AS process_definition_name,
        MIN( td1.task_id ) AS task_id,
        MIN( td1.create_by ) AS create_by_name,
        MIN( td1.create_user_id ) AS create_by,
        MIN( select_table.plugin_classify_id ) AS plugin_classify_id,
        string_agg( DISTINCT select_table.datasource_id::text, ',') AS datasource_id,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.business_name END , ',') AS source_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.business_name END , ',') AS target_business_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN business_database.datasource_business.simple_name END , ',') AS source_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN business_database.datasource_business.simple_name END , ',') AS target_simple_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '1' THEN select_table.table_name END , ',') AS source_table_name,
        string_agg( DISTINCT CASE WHEN plugin_classify_id = '2' THEN select_table.table_name END , ',') AS target_table_name,
        split_part(string_agg(pi.state::text, ',' ORDER BY pi.start_time DESC), ',', 1) AS last_task_state,
        split_part( string_agg( pi.start_time::text, ',' ORDER BY pi.start_time DESC ), ',', 1 ) last_task_start_time,
        split_part( string_agg( pi.end_time::text, ',' ORDER BY pi.start_time DESC ), ',', 1 ) last_task_end_time,
        MIN(pi.id) AS process_instance_id
        FROM
        business_integration.task_definition_info td1
        LEFT JOIN business_integration.di_process_definition dp ON dp.id = td1.task_id
        LEFT JOIN business_integration.task_definition_info td2 ON strpos(',' || dp.task_ids || ',', ',' || td2.task_id || ',')> 0
        LEFT JOIN (
        SELECT
        version_id,
        plugin_classify_id,
        (CAST(plugin_basics_config AS json)->>'datasourceId') AS datasource_id,
        CASE
        WHEN CAST(plugin_basics_config AS json)->>'tableName' IS NULL
        OR CAST(plugin_basics_config AS json)->>'tableName' = '' THEN
        '落成文本'
        ELSE CAST(plugin_basics_config AS json)->>'tableName'
        END AS table_name
        FROM
        business_integration.task_config_info
        ) select_table ON select_table.version_id = td2.current_version_id
        LEFT JOIN business_database.datasource_info ON business_database.datasource_info.datasource_info_id = select_table.datasource_id
        LEFT JOIN business_database.datasource_business ON business_database.datasource_business.uuid = business_database.datasource_info.business_uuid
        LEFT JOIN business_integration.di_scheduler dis ON dis.process_definition_code = dp.id
        LEFT JOIN business_integration.di_task dit ON dp.id = dit.id
        LEFT JOIN business_integration.di_task_catalogue dtc ON dit.task_catalogue_id = dtc.id
        LEFT JOIN business_ds.t_ds_process_instance pi ON cast(pi.process_definition_code as varchar) = dp.id
        WHERE
        1 = 1
        AND td1.tenant_id = #{tenantCode,jdbcType=VARCHAR}
        AND td1.task_type = '2'
        AND td1.online_status = 1
        GROUP BY
        td1.task_id
        ORDER BY
        MIN( td1.create_time ) DESC
        ) select_table where 1=1
        <if test="catalogId != null and catalogId != ''">
            AND catalogue_id = #{catalogId,jdbcType=VARCHAR}
        </if>
        <if test="catalogName != null and catalogName != ''">
            AND catalogue_name like CONCAT('%', #{catalogName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="processDefinitionName != null and processDefinitionName != ''">
            AND process_definition_name like CONCAT('%', #{processDefinitionName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceBusinessName != null and sourceBusinessName != ''">
            AND source_business_name like CONCAT('%', #{sourceBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetBusinessName != null and targetBusinessName != ''">
            AND target_business_name like CONCAT('%', #{targetBusinessName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceSimpleName != null and sourceSimpleName != ''">
            AND source_simple_name like CONCAT('%', #{sourceSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetSimpleName != null and targetSimpleName != ''">
            AND target_simple_name like CONCAT('%', #{targetSimpleName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceTableName != null and sourceTableName != ''">
            AND source_table_name like CONCAT('%', #{sourceTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="targetTableName != null and targetTableName != ''">
            AND target_table_name like CONCAT('%', #{targetTableName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="taskState != null and taskState != ''">
            AND last_task_state = #{taskState, jdbcType=VARCHAR}
        </if>
        <if test="taskStartTime != null and taskStartTime != ''">
            AND last_task_start_time &lt;= #{taskStartTime, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getCatalogList" resultType="com.joyadata.scc.dto.ningbo.IntegrationCatalogDTO">
        SELECT
            business_integration.task_catalogue.catalogue_id AS id,
            business_integration.task_catalogue.parent_id AS parentId,
            business_integration.task_catalogue.parent_level AS parentIds,
            business_integration.task_catalogue.name,
            business_integration.task_catalogue.task_id AS taskId,
            business_integration.task_catalogue.run_type AS runType,
            business_integration.task_catalogue.task_type AS taskType,
            business_integration.task_catalogue.task_status AS taskStatus,
            business_integration.task_catalogue.level,
            business_integration.task_catalogue.simple_name AS simpleName,
            business_integration.task_catalogue.options_id AS optionsId,
            business_integration.task_catalogue.is_leaf AS isLeaf,
            business_integration.task_catalogue.create_by AS createBy,
            business_integration.task_catalogue.create_time AS createTime,
            business_integration.task_catalogue.update_by AS updateBy,
            business_integration.task_catalogue.update_time AS updateTime,
            business_integration.task_catalogue.create_user_id AS createUserId,
            business_integration.task_catalogue.create_dept_id AS createDeptId,
            business_integration.task_catalogue.remark,
            business_integration.task_catalogue.project_id AS projectId,
            business_integration.task_catalogue.tenant_id AS tenantCode,
            joyadata.tms_project.NAME AS projectName
        FROM
            business_integration.task_catalogue
                LEFT JOIN joyadata.tms_project ON business_integration.task_catalogue.project_id = joyadata.tms_project.id
        WHERE tenant_id = #{tenantCode, jdbcType=VARCHAR}
          AND task_id is null
    </select>
    <select id="getProcessInstanceByIdTotal" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        (
        SELECT
        min( business_integration.di_process_instance.id ) AS id,
        min( business_integration.task_catalogue.name ) AS catalogue_name,
        min( business_integration.di_process_instance.process_name ) AS process_definition_name,
        min( business_integration.di_process_instance.state ) AS state,
        CAST( min( business_integration.di_process_instance.start_time ) AS CHAR ) AS start_time,
        CAST( min( business_integration.di_process_instance.end_time ) AS CHAR ) AS end_time,
        sum( business_scc.scc_engine_job_metrics.read_row_count ) AS read_row_count,
        sum( business_scc.scc_engine_job_metrics.write_row_count ) AS write_row_count
        FROM
        business_integration.di_process_instance
        LEFT JOIN business_integration.task_catalogue ON business_integration.di_process_instance.task_catalogue_id = business_integration.task_catalogue.catalogue_id
        LEFT JOIN business_integration.di_task_instance ON business_integration.di_task_instance.process_instance_id = business_integration.di_process_instance.id
        LEFT JOIN business_scc.scc_engine_job_metrics ON business_scc.scc_engine_job_metrics.instance_id = business_integration.di_task_instance.id
        WHERE
        business_integration.di_process_instance.process_definition_id = #{processDefinitionCode,jdbcType=VARCHAR}
        AND business_integration.di_process_instance.tenant_code = #{tenantCode,jdbcType=VARCHAR}
        GROUP BY
        business_integration.di_process_instance.id
        ) table_a where 1=1
        <if test="startTime != null and startTime != ''">
            AND start_time &gt;= #{startTime, jdbcType=VARCHAR}
        </if>
        <if test="endTime != null and endTime != ''">
            AND end_time &lt;=  #{endTime, jdbcType=VARCHAR}
        </if>
        <if test="state != null and state != ''">
            AND state = #{state, jdbcType=VARCHAR}
        </if>
    </select>
</mapper>