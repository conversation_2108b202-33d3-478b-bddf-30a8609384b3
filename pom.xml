<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.joyadata.apps</groupId>
    <artifactId>database.scc</artifactId>
    <properties>
        <app.name>dedp-database-scc</app.name>
        <app.version>1.0.0</app.version>
        <app.port>18186</app.port>
        <app.main.class>com.joyadata.scc.SccApplication</app.main.class>
    </properties>
    <parent>
        <groupId>com.joyadata.cloud</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.1</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <!-- seatunnel 依赖 -->
        <!--<dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-jackson</artifactId>
            <version>2.3.4</version>
            <classifier>optional</classifier>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-properties</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-engine-client</artifactId>
            <version>2.3.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!-- hazelcast 依赖 -->
        <!-- <dependency>
             <groupId>com.hazelcast</groupId>
             <artifactId>hazelcast</artifactId>
             <version>5.1.1</version>
         </dependency>
         <dependency>
             <groupId>com.hazelcast</groupId>
             <artifactId>hazelcast-spring</artifactId>
             <version>5.1.1</version>
         </dependency>-->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.joyadata.apps</groupId>
            <artifactId>business.engine.beans</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yml</include>
                    <include>**/*.yaml</include>
                    <include>**/logback*.xml</include>
                    <include>**/*.properties</include>
                    <include>mapper/**/*.xml</include>
                </includes>
                <excludes>
                    <exclude>**/*.keytab</exclude>
                    <exclude>/nmap/**</exclude>
                    <exclude>**/*.sh</exclude>
                    <exclude>**/**.pdf</exclude>
                </excludes>
            </resource>
            <!-- <resource>
                 <filtering>false</filtering>
                 <directory>src/main/resources</directory>
                 <includes>
                     <include>mapper/**/*.xml</include>
                 </includes>
             </resource>-->
            <resource>
                <filtering>false</filtering>
                <directory>src/main/resources/</directory>
                <targetPath>${project.build.outputDirectory}/../config</targetPath>
            </resource>
            <!--      <resource>
                      <includes>
                          <include>**/hazelcast-client.yaml</include>
                      </includes>
                      <filtering>false</filtering>
                      <directory>src/main/resources/</directory>
                      <targetPath>${project.build.outputDirectory}/../build/lib</targetPath>
                  </resource>-->
        </resources>
    </build>
</project>